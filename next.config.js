/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'b2b.instinctfusionx.xyz',
        port: '',
        pathname: '/storage/**',
      },
    ],
    domains: [
      'b2b.instinctfusionx.xyz',
      'placehold.co',
      'via.placeholder.com',
      'images.unsplash.com',
      'res.cloudinary.com'
    ],
    unoptimized: true
  },
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  reactStrictMode: true,
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;