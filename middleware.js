import { NextResponse } from 'next/server';
import {
  ROUTE_PERMISSIONS,
  ROLE_DEFAULT_ROUTES,
  TOKEN_CONFIG,
  ROLE_TOKEN_MAP,
  isRouteAccessibleByRole,
  getUserRoleFromTokens,
  getLoginPathForRoute,
  getSmartRedirectDestination
} from './src/utils/authUtils.js';

export async function middleware(request) {
  try {
    // Get all tokens from cookies
    const tokens = {
      superAdminAuthToken: request.cookies.get('superAdminAuthToken')?.value,
      partnerAuthToken: request.cookies.get('partnerAuthToken')?.value,
      userAuthToken: request.cookies.get('userAuthToken')?.value,
    };

    const currentPath = request.nextUrl.pathname;
    const searchParams = request.nextUrl.searchParams;
    const userRole = getUserRoleFromTokens(tokens);

    console.log('🛡️ Middleware Processing:');
    console.log('  - Current Path:', currentPath);
    console.log('  - User Role:', userRole);
    console.log('  - Tokens:', Object.keys(tokens).filter(key => tokens[key]));

    // Handle login pages - check if user is already authenticated and redirect appropriately
    if (currentPath.startsWith('/login/')) {
      console.log('🔐 Login page access detected');

      if (userRole) {
        // User is already authenticated - check intended destination and role-based access
        const intendedDestination = searchParams.get('redirect');
        console.log('  - User already authenticated, intended destination:', intendedDestination);

        // Use smart redirect logic to determine where to send the user
        const redirectDestination = getSmartRedirectDestination(userRole, intendedDestination);
        console.log('  - Smart redirect destination:', redirectDestination);

        return NextResponse.redirect(new URL(redirectDestination, request.url));
      }

      console.log('  - 👤 User not authenticated, allowing login page access');
      // User not authenticated, allow access to login page
      return NextResponse.next();
    }

    // STEP 1: Check if user is authenticated
    if (!userRole) {
      console.log('🚫 No authentication found');

      // Find matching protected route
      const matchedRoute = Object.keys(ROUTE_PERMISSIONS).find((route) =>
        currentPath.startsWith(route)
      );

      // If it's a protected route, redirect to login
      if (matchedRoute) {
        const intendedDestination = encodeURIComponent(currentPath + request.nextUrl.search);
        const loginPath = getLoginPathForRoute(currentPath);
        const loginUrl = new URL(loginPath, request.url);
        loginUrl.searchParams.set('redirect', intendedDestination);

        console.log('  - Redirecting to login:', loginUrl.toString());
        return NextResponse.redirect(loginUrl);
      }

      // Public route, allow access
      console.log('  - Public route, allowing access');
      return NextResponse.next();
    }

    // STEP 2: User is authenticated - check role-based access
    console.log('✅ User authenticated, checking role-based access');

    // Find matching protected route
    const matchedRoute = Object.keys(ROUTE_PERMISSIONS).find((route) =>
      currentPath.startsWith(route)
    );

    // If not a protected route, allow access
    if (!matchedRoute) {
      console.log('  - Public route, allowing access');
      return NextResponse.next();
    }

    // Check if user has the required token for this specific route
    const requiredTokens = ROUTE_PERMISSIONS[matchedRoute];
    const userToken = ROLE_TOKEN_MAP[userRole];
    const hasAccess = requiredTokens.includes(userToken);

    console.log('  - Matched Route:', matchedRoute);
    console.log('  - Required Tokens:', requiredTokens);
    console.log('  - User Token:', userToken);
    console.log('  - Has Access:', hasAccess);

    if (!hasAccess) {
      // User doesn't have access to this route - redirect to their role default
      const defaultRoute = ROLE_DEFAULT_ROUTES[userRole];
      console.log('  - 🚫 Access denied, redirecting to role default:', defaultRoute);
      return NextResponse.redirect(new URL(defaultRoute, request.url));
    }

    // User has access, allow through
    console.log('  - ✅ Access granted');
    return NextResponse.next();

  } catch (error) {
    console.error('Middleware error:', error);
    return NextResponse.redirect(new URL('/error', request.url));
  }
}

export const config = {
  matcher: [
    '/super-admin/:path*',
    '/partner-admin/:path*',
    '/customer-admin/:path*',
    // '/seo-admin/:path*', // Temporarily disabled for development
    '/cart/:path*',
    '/checkout/:path*',
    '/login/:path*', // Include login routes for smart redirect handling
    // Prevent running on API routes and static files
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};