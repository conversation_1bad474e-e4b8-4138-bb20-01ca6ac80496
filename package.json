{"name": "diptouch", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@heroicons/react": "^2.0.18", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "axios": "^1.9.0", "chart.js": "^4.4.9", "framer-motion": "^12.6.5", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "next": "15.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "sweetalert2": "^11.21.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.0", "tailwindcss": "^4"}}