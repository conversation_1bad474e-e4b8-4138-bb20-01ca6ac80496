const { default: axios } = require("axios");

const { WEB_SERVER_URL } = process.env;

if (!WEB_SERVER_URL) {
    throw new Error('WEB_SERVER_URL environment variable is not defined');
}

const instance = axios.create({
    baseURL: WEB_SERVER_URL,
    timeout: 20000, // 20 seconds timeout
});

const isValidToken = (token) => {
    if (!token) return false;
    // Basic check if token is a JWT format (you might want to add more validation)
    const parts = token.split('.');
    return parts.length === 3;
};

const getStorageToken = () => {
    try {
        if (typeof window !== 'undefined' && window.localStorage) {
            return localStorage.getItem('superAdminAuthToken');
        }
        return null;
    } catch (error) {
        console.error('Error accessing localStorage:', error);
        return null;
    }
};

const axiosSuperInstance = async function () {
    console.log("axiosSuperInstance");

    instance.interceptors.request.use(async function (config) {
        try {
            const token = getStorageToken();

            // Use the getStorageToken function instead of direct localStorage access
            console.log("token", token);

            if (token && isValidToken(token)) {
                config.headers.Authorization = `Bearer ${token}`;
            }
            return config;
        } catch (error) {
            console.error('Request interceptor error:', error);
            return Promise.reject(error);
        }
    }, (error) => {
        console.error('Request configuration error:', error);
        return Promise.reject(error);
    });

    instance.interceptors.response.use(
        (response) => response,
        (error) => {
            if (error.response) {
                // Server responded with an error status
                console.error('Response error:', {
                    status: error.response.status,
                    data: error.response.data
                });

                // Handle specific HTTP status codes
                switch (error.response.status) {
                    case 401:
                        // Handle unauthorized (e.g., invalid/expired token)
                        if (typeof window !== 'undefined' && window.localStorage) {
                            localStorage.removeItem('token');
                        }
                        break;
                    case 403:
                        // Handle forbidden
                        break;
                    case 404:
                        // Handle not found
                        break;
                    case 500:
                        // Handle server error
                        break;
                }
            } else if (error.request) {
                // Request was made but no response received
                console.error('No response received:', error.request);
            } else {
                // Error in request configuration
                console.error('Request setup error:', error.message);
            }
            return Promise.reject(error);
        }
    );

    return instance;
};

export default axiosSuperInstance;