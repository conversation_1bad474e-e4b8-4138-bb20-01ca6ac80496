/* internal modules imports */

import axiosPublicInstance from "@/src/axios/axios.public";

const brands = {};

brands.getBrands = async function () {
    try {
        const response = await axiosPublicInstance.get("/brands");
        // console.log(response);

        if (response.status !== 200 && !response.data) {
            return {};
        }

        const { data, meta } = response;

        const brandsName = data?.brands.map((brand) => brand.name) || [];
        console.log(brandsName);


        return { data, meta, brandsName } || {};
    } catch (error) {
        console.log(error);
        return {};
    }
};

/* exports module */
export default brands;