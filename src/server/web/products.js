/* internal modules imports */
import axiosPublicInstance from "@/src/axios/axios.public";

const products = {};

// get all products from server with pagination
products.getProducts = async function () {
    try {
        const response = await axiosPublicInstance.get("/superadmin/products");
        // console.log(response);

        if (response.status !== 200 && !response.data) {
            return [];
        }

        const { data, meta } = response.data;
        // console.log(data);

        return { data, meta } || [];
    } catch (error) {
        console.log(error);
        return [];
    }
}

/* module export */
export default products