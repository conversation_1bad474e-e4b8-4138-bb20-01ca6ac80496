/* internal modules imports */
import axiosPartnerInstance from "@/src/axios/axios.partner";

const partnerAdmin = {};

partnerAdmin.postProduct = async function (product) {
    try {
        const response = await axiosPartnerInstance.post("/products", product);
        // console.log(response);

        if (response.status !== 200 && !response.data) {
            return false;
        }

        return response || false;
    } catch (error) {
        console.log(error);
        return false;
    }
};

/* module export */
export default partnerAdmin;