/* internal modules imports */
import axiosPublicInstance from "@/src/axios/axios.public";

const categories = {};

categories.getCategories = async function () {
    try {
        const response = await axiosPublicInstance.get("/categories");

        if (response.status !== 200 && !response.data) return {};
        // console.log(response.data);

        const { data, meta } = response.data;
        const categoriesName = data?.categories.map((category) => category.name) || [];
        // console.log(categoriesName);

        return { data, meta, categoriesName } || {};
    } catch (error) {
        console.log(error);
        return {};
    }
};

/* module export */
export default categories