/* internal modules imports */

const ranking = {};

// Update single product ranking
ranking.updateProductRank = async function (categoryId, productId, rank) {
    try {
        const token = localStorage.getItem('superAdminAuthToken');
        const response = await fetch(`/api/admin/products/category/${categoryId}/product/${productId}/rank`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ rank })
        });

        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Failed to update product ranking');
        }

        return data;
    } catch (error) {
        console.error('Error updating product rank:', error);
        throw error;
    }
};

// Update multiple product rankings
ranking.updateMultipleRankings = async function (categoryId, rankings) {
    try {
        const token = localStorage.getItem('superAdminAuthToken');
        const response = await fetch(`/api/admin/products/category/${categoryId}/rankings`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ rankings })
        });

        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Failed to update product rankings');
        }

        return data;
    } catch (error) {
        console.error('Error updating product rankings:', error);
        throw error;
    }
};

// Get products by category
ranking.getCategoryProducts = async function (categoryId) {
    try {
        const response = await fetch(`/api/categories/${categoryId}/products`);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Failed to fetch category products');
        }

        return data;
    } catch (error) {
        console.error('Error fetching category products:', error);
        throw error;
    }
};

// Get sorted products by category
ranking.getSortedProducts = async function (categoryId) {
    try {
        const response = await fetch(`/api/products/category/${categoryId}/sorted`);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Failed to fetch sorted products');
        }

        return data;
    } catch (error) {
        console.error('Error fetching sorted products:', error);
        throw error;
    }
};

/* module export */
export default ranking;
