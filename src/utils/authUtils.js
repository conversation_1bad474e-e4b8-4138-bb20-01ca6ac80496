// Centralized authentication utilities and route permissions

// Define route permissions with required tokens - STRICT role-based access
export const ROUTE_PERMISSIONS = {
  '/super-admin': ['superAdminAuthToken'], // Only super admin
  '/partner-admin': ['partnerAuthToken'], // Only partner/vendor
  '/customer-admin': ['userAuthToken'], // Only customer/buyer
  // '/seo-admin': ['seoAdminAuthToken'], // Temporarily disabled for development
  '/cart': ['userAuthToken', 'partnerAuthToken', 'superAdminAuthToken'], // Allow any authenticated user
  '/checkout': ['userAuthToken', 'partnerAuthToken'], // Allow only buyers and vendors
};

// Define default routes for each user role
export const ROLE_DEFAULT_ROUTES = {
  superadmin: '/super-admin',
  vendor: '/partner-admin',
  buyer: '/products', // Changed from /customer-admin to /products for buyers
  seo: '/seo-admin',
};

// Define token types and their corresponding login paths
export const TOKEN_CONFIG = {
  superAdminAuthToken: '/login/super-admin',
  partnerAuthToken: '/login/partner',
  userAuthToken: '/login/user',
};

// Role to token mapping
export const ROLE_TOKEN_MAP = {
  superadmin: 'superAdminAuthToken',
  vendor: 'partnerAuthToken',
  buyer: 'userAuthToken',
};

/**
 * Helper function to determine if a route is accessible by a user role
 * @param {string} route - The route to check
 * @param {string} userRole - The user role (superadmin, vendor, buyer)
 * @returns {boolean} - Whether the route is accessible
 */
export function isRouteAccessibleByRole(route, userRole) {
  console.log('🔍 Checking route access:');
  console.log('  - Route:', route);
  console.log('  - User Role:', userRole);

  const matchedRoute = Object.keys(ROUTE_PERMISSIONS).find((protectedRoute) =>
    route.startsWith(protectedRoute)
  );

  console.log('  - Matched Route:', matchedRoute);

  if (!matchedRoute) {
    console.log('  - ✅ Public route - access granted');
    return true; // Public route
  }

  const requiredTokens = ROUTE_PERMISSIONS[matchedRoute];
  const userToken = ROLE_TOKEN_MAP[userRole];
  const hasAccess = requiredTokens.includes(userToken);

  console.log('  - Required Tokens:', requiredTokens);
  console.log('  - User Token:', userToken);
  console.log('  - Has Access:', hasAccess);

  return hasAccess;
}

/**
 * Helper function to get user role from tokens
 * @param {Object} tokens - Object containing auth tokens
 * @returns {string|null} - User role or null if no valid token
 */
export function getUserRoleFromTokens(tokens) {
  if (tokens.superAdminAuthToken) return 'superadmin';
  if (tokens.partnerAuthToken) return 'vendor';
  if (tokens.userAuthToken) return 'buyer';
  return null;
}

/**
 * Helper function to get smart redirect destination with strict role-based access control
 * @param {string} userRole - The user role (superadmin, vendor, buyer)
 * @param {string} intendedDestination - The intended destination URL (may be URL encoded)
 * @returns {string} - The redirect destination
 */
export function getSmartRedirectDestination(userRole, intendedDestination) {
  console.log('🎯 Smart Redirect Debug:');
  console.log('  - User Role:', userRole);
  console.log('  - Raw Intended Destination:', intendedDestination);

  // Decode the intended destination if it exists
  let decodedDestination = null;
  if (intendedDestination) {
    try {
      decodedDestination = decodeURIComponent(intendedDestination);
      console.log('  - Decoded Destination:', decodedDestination);
    } catch (error) {
      console.log('  - Failed to decode destination:', error);
      decodedDestination = intendedDestination;
    }
  }

  // Check if there's an intended destination and if the user role is allowed to access it
  if (decodedDestination) {
    const hasAccess = isRouteAccessibleByRole(decodedDestination, userRole);
    console.log('  - Can access intended destination:', hasAccess);

    if (hasAccess) {
      console.log('  - ✅ Redirecting to intended destination:', decodedDestination);
      return decodedDestination;
    } else {
      console.log('  - 🚫 User role not allowed to access intended destination');
    }
  }

  // Fall back to role-based default route based on user role
  const defaultRoute = ROLE_DEFAULT_ROUTES[userRole] || '/';
  console.log('  - 🔄 Redirecting to role-based default route:', defaultRoute);
  console.log('  - Role-based defaults:');
  console.log('    - Super Admin → /super-admin');
  console.log('    - Partner/Vendor → /partner-admin');
  console.log('    - Customer/Buyer → /products');

  return defaultRoute;
}

/**
 * Get the appropriate login path for a route
 * @param {string} route - The protected route
 * @returns {string} - The login path
 */
export function getLoginPathForRoute(route) {
  const matchedRoute = Object.keys(ROUTE_PERMISSIONS).find((protectedRoute) =>
    route.startsWith(protectedRoute)
  );

  if (!matchedRoute) {
    return '/login/user'; // Default to user login for public routes
  }

  // For cart and checkout routes, default to user login but allow partners too
  // The middleware will handle smart redirects based on user preference
  if (matchedRoute === '/cart' || matchedRoute === '/checkout') {
    return '/login/user'; // Default to user, but partners can also access these routes
  }

  // For other routes, redirect to the appropriate login page based on the first required token
  const requiredTokens = ROUTE_PERMISSIONS[matchedRoute];
  return TOKEN_CONFIG[requiredTokens[0]];
}
