import React from 'react';

// Simple test function to demonstrate SSR
export async function GetAllProductsByCategoryId(id, params = {}) {
    console.log('🔍 SSR Test: Function called with:', { id, params });

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
        success: true,
        data: {
            categoryId: id,
            params: params,
            products: [
                { id: 1, name: `Product 1 for Category ${id}`, price: '99.99' },
                { id: 2, name: `Product 2 for Category ${id}`, price: '149.99' },
                { id: 3, name: `Product 3 for Category ${id}`, price: '199.99' }
            ],
            timestamp: new Date().toISOString()
        }
    };
}

// SSR Test Page
export default async function TestSSRPage({ searchParams }) {
    console.log('🚀 SSR Test: Page component executing on server');
    console.log('🔍 SSR Test: Search params:', searchParams);

    // Extract parameters
    const categoryId = searchParams.id || '1';
    const queryParams = {
        page: searchParams.page || '1',
        search: searchParams.search || '',
        sort: searchParams.sort || 'name'
    };

    console.log('📊 SSR Test: Calling GetAllProductsByCategoryId...');

    // Call the SSR function
    const result = await GetAllProductsByCategoryId(categoryId, queryParams);

    console.log('✅ SSR Test: Function completed, result:', result);

    return (
        <div className="min-h-screen bg-gray-50 p-8">
            <div className="max-w-4xl mx-auto">
                <h1 className="text-3xl font-bold text-gray-900 mb-8">
                    SSR Test Page - Category Products
                </h1>

                {/* SSR Data Display */}
                <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                    <h2 className="text-xl font-semibold text-gray-800 mb-4">
                        🔍 SSR Function Result
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <div className="bg-blue-50 p-4 rounded-lg">
                            <h3 className="font-semibold text-blue-800 mb-2">Input Parameters</h3>
                            <div className="text-sm text-blue-700">
                                <div><strong>Category ID:</strong> {result.data.categoryId}</div>
                                <div><strong>Page:</strong> {result.data.params.page}</div>
                                <div><strong>Search:</strong> {result.data.params.search || 'None'}</div>
                                <div><strong>Sort:</strong> {result.data.params.sort}</div>
                            </div>
                        </div>

                        <div className="bg-green-50 p-4 rounded-lg">
                            <h3 className="font-semibold text-green-800 mb-2">SSR Info</h3>
                            <div className="text-sm text-green-700">
                                <div><strong>Success:</strong> {result.success ? '✅ Yes' : '❌ No'}</div>
                                <div><strong>Products Count:</strong> {result.data.products.length}</div>
                                <div><strong>Generated At:</strong> {new Date(result.data.timestamp).toLocaleTimeString()}</div>
                                <div><strong>Rendered:</strong> Server-side</div>
                            </div>
                        </div>
                    </div>

                    {/* Products List */}
                    <h3 className="font-semibold text-gray-800 mb-4">📦 Products from SSR</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {result.data.products.map(product => (
                            <div key={product.id} className="bg-gray-50 p-4 rounded-lg">
                                <h4 className="font-medium text-gray-900">{product.name}</h4>
                                <p className="text-orange-600 font-semibold">${product.price}</p>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Console Instructions */}
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                    <h2 className="text-lg font-semibold text-yellow-800 mb-3">
                        🖥️ Check Your Console
                    </h2>
                    <div className="text-yellow-700 space-y-2">
                        <p><strong>Server Console:</strong> Look for SSR logs in your terminal/server console</p>
                        <p><strong>Browser Console:</strong> Open DevTools to see client-side logs</p>
                        <p><strong>Expected Logs:</strong></p>
                        <ul className="list-disc list-inside ml-4 space-y-1 text-sm">
                            <li>🚀 SSR Test: Page component executing on server</li>
                            <li>🔍 SSR Test: Search params: {JSON.stringify(searchParams)}</li>
                            <li>📊 SSR Test: Calling GetAllProductsByCategoryId...</li>
                            <li>🔍 SSR Test: Function called with: {JSON.stringify({ id: categoryId, params: queryParams })}</li>
                            <li>✅ SSR Test: Function completed, result: [object]</li>
                        </ul>
                    </div>
                </div>

                {/* Test Links */}
                <div className="bg-white rounded-lg shadow-md p-6 mt-8">
                    <h2 className="text-lg font-semibold text-gray-800 mb-4">
                        🔗 Test Different Parameters
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <a
                            href="/products/categories/test-ssr?id=1&page=1&search=laptop"
                            className="block bg-blue-100 hover:bg-blue-200 p-3 rounded-lg text-blue-800 transition-colors"
                        >
                            Category 1 + Search "laptop"
                        </a>
                        <a
                            href="/products/categories/test-ssr?id=2&page=2&sort=price"
                            className="block bg-green-100 hover:bg-green-200 p-3 rounded-lg text-green-800 transition-colors"
                        >
                            Category 2 + Page 2 + Sort by Price
                        </a>
                        <a
                            href="/products/categories/test-ssr?id=3&search=headphones&sort=name"
                            className="block bg-purple-100 hover:bg-purple-200 p-3 rounded-lg text-purple-800 transition-colors"
                        >
                            Category 3 + Search "headphones"
                        </a>
                    </div>
                </div>

                {/* Implementation Guide */}
                <div className="bg-gray-100 rounded-lg p-6 mt-8">
                    <h2 className="text-lg font-semibold text-gray-800 mb-4">
                        📋 Implementation Guide
                    </h2>
                    <div className="text-gray-700 space-y-3 text-sm">
                        <div>
                            <strong>1. Function Usage:</strong>
                            <code className="block bg-gray-200 p-2 rounded mt-1">
                                const result = await GetAllProductsByCategoryId(categoryId, params);
                            </code>
                        </div>
                        <div>
                            <strong>2. URL Parameters:</strong>
                            <code className="block bg-gray-200 p-2 rounded mt-1">
                                /products/categories/[id]?page=1&search=query&sort=name
                            </code>
                        </div>
                        <div>
                            <strong>3. SSR Page Component:</strong>
                            <code className="block bg-gray-200 p-2 rounded mt-1">
                                export default async function Page({`{params, searchParams}`}) {`{...}`}
                            </code>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}


// task!! delete it later