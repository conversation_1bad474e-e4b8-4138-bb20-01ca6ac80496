import { Elements } from '@stripe/react-stripe-js'
import React from 'react'
import CheckoutForm from './CheckoutForm'
import { loadStripe } from '@stripe/stripe-js'

export default function Payment({ paymentInstance, billingAddress, shippingAddress }) {

    const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY)

    return (
        <div >
            <Elements stripe={stripePromise}>
                <CheckoutForm paymentInstance={paymentInstance} billingAddress={billingAddress} shippingAddress={shippingAddress}></CheckoutForm>
            </Elements>
        </div>
    )
}
