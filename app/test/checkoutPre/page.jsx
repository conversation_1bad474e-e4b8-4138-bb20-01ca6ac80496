"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Fa<PERSON><PERSON>Shopping, Fa<PERSON><PERSON>ck<PERSON>lane, FaLock } from "react-icons/fa6";
import { BsCreditCard2FrontFill } from "react-icons/bs";
import { TiArrowLeftThick, TiArrowRightThick } from "react-icons/ti";
import { MdPlaylistAddCheckCircle } from "react-icons/md";
import { HiXCircle } from "react-icons/hi";
import { PiMapPinFill } from "react-icons/pi";
import { GiWallet } from "react-icons/gi";
import { TbShieldCheckFilled } from "react-icons/tb";
import { LuLoaderPinwheel } from "react-icons/lu";
import Payment from './Payment';

// Mock Data (Replace with your actual data fetching)
const products = [
    { id: 'PROD-234', name: 'Sony WH-1000XM4', price: 349.00, quantity: 1, image: 'https://m.media-amazon.com/images/I/51+jEwFwcEL._AC_UF894,1000_QL80_.jpg' },
    { id: 'PROD-567', name: 'Apple MacBook Pro 16"', price: 2399.00, quantity: 1, image: 'https://m.media-amazon.com/images/I/71pGZT5KsDL._AC_UF1000,1000_QL80_.jpg' },
    { id: 'PROD-890', name: 'Samsung 65-Inch QLED TV', price: 1799.99, quantity: 1, image: 'https://m.media-amazon.com/images/I/81ZJt-qLEDL._AC_UF894,1000_QL80_.jpg' },
];

const Checkout = () => {
    const [step, setStep] = useState(1);
    const [shippingAddress, setShippingAddress] = useState({});
    const [billingAddress, setBillingAddress] = useState({});
    const [payment, setPayment] = useState({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [orderConfirmation, setOrderConfirmation] = useState(null);
    const [customerAddresses, setCustomerAddresses] = useState([]);
    const [isLoadingAddresses, setIsLoadingAddresses] = useState(true);
    const [addressError, setAddressError] = useState(null);
    const [addresses, setAddresses] = useState([]);
    const [showNewShippingAddress, setShowNewShippingAddress] = useState(false);
    const [paymentInstance, setPaymentInstance] = useState(null);

    const subtotal = products.reduce((acc, item) => acc + item.price * item.quantity, 0);
    const shippingCost = 12.95; // Example shipping cost
    const taxRate = 0.0825; // Example tax rate (NY)
    const tax = subtotal * taxRate;
    const total = subtotal + shippingCost + tax;

    const nextStep = async () => {
        if (step === 3) { // Assuming step 4 is payment
            try {
                setIsSubmitting(true);
                const stripeInstance = await postInitailOrder();
                // Handle the Stripe instance here
                console.log('Stripe instance:', stripeInstance);
                setStep(prev => prev + 1);
            } catch (error) {
                console.error('Error proceeding to payment:', error);
                // Handle error (show error message to user)
            } finally {
                setIsSubmitting(false);
            }
        } else {
            setStep(prev => prev + 1);
        }
    };
    const prevStep = () => setStep(prev => prev - 1);



    useEffect(() => {
        // getUserCart();
        // postInitailOrder();
        // getBillingShippingAddress();
    }, []);

    async function getUserCart() {
        const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/carts/${localStorage.getItem('cartId')}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('userAuthToken')}`,
            },
        })

        const data = await response.json();
        console.log(data);
    }

    // async function getBillingShippingAddress() {
    //     const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/customer/addresses`, {
    //         method: 'GET',
    //         headers: {
    //             'Content-Type': 'application/json',
    //             'Authorization': `Bearer ${localStorage.getItem('userAuthToken')}`,
    //         },
    //     })

    //     const data = await response.json();
    //     console.log(data);
    // }

    async function postInitailOrder() {
        console.log(localStorage.getItem('cartId'));
        const cartId = localStorage.getItem('cartId');

        const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/checkout/initialize`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('userAuthToken')}`,
            },
            body: JSON.stringify({
                cart_id: localStorage.getItem('cartId'),
                shipping_address: shippingAddress,
                billing_address: billingAddress,
                payment_method: payment.method,
                notes: "Please include tax invoice with the delivery"
            })
        });

        const data = await response.json();
        console.log(data);

        setPaymentInstance(data);
        return data;
    }

    // Add this function inside your Checkout component
    const fetchAddresses = async () => {
        try {
            const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/customer/addresses`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('userAuthToken')}`
                }
            });
            const data = await response.json();
            if (data.success) {
                // Update your addresses state with the new data
                setAddresses(data.data.addresses); // You'll need to add this state if you haven't already
            }
        } catch (error) {
            console.error('Error fetching addresses:', error);
        }
    };

    // Add this useEffect to fetch addresses when the component mounts
    useEffect(() => {
        fetchAddresses();
    }, []);

    // Fetch customer addresses
    useEffect(() => {
        const fetchCustomerAddresses = async () => {
            try {
                setIsLoadingAddresses(true);

                // First check for user token
                let token = localStorage.getItem('userAuthToken');
                let userType = 'user';

                // If no user token, check for partner token
                if (!token) {
                    token = localStorage.getItem('partnerAuthToken');
                    userType = 'partner';
                }

                // If no token found, redirect to login
                if (!token) {
                    window.location.href = '/login/user';
                    return;
                }

                console.log('Using token for:', userType); // Debug log

                const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/customer/addresses`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                if (data.success) {
                    setCustomerAddresses(data.data.addresses);
                    // Set default address if available
                    const defaultAddress = data.data.addresses.find(addr => addr.is_default);
                    if (defaultAddress) {
                        const formattedAddress = {
                            name: defaultAddress.contact_name,
                            street: defaultAddress.address_line1,
                            street2: defaultAddress.address_line2,
                            city: defaultAddress.city,
                            state: defaultAddress.state,
                            zip: defaultAddress.postal_code,
                            country: defaultAddress.country,
                            email: defaultAddress.contact_email,
                            phone: defaultAddress.contact_phone,
                            company: defaultAddress.company_name,
                            instructions: defaultAddress.delivery_instructions
                        };
                        setShippingAddress(formattedAddress);
                        setBillingAddress(prev => ({
                            ...prev,
                            ...formattedAddress
                        }));
                    }
                } else {
                    setAddressError('Failed to fetch addresses');
                }
            } catch (error) {
                console.error('Error fetching addresses:', error);
                setAddressError(`Failed to fetch addresses: ${error.message}`);
            } finally {
                setIsLoadingAddresses(false);
            }
        };

        fetchCustomerAddresses();
    }, []);

    const handleShippingChange = (e) => {
        const { name, value } = e.target;
        const updatedShippingAddress = { ...shippingAddress, [name]: value };
        setShippingAddress(updatedShippingAddress);
        setBillingAddress(prev => ({
            ...prev,
            [name]: value,
        }));
    };

    const handleBillingChange = (e) => {
        const { name, value, type, checked } = e.target;
        if (type === 'checkbox') {
            setBillingAddress(prev => ({
                ...prev,
                sameAsShipping: checked,
                ...(checked ? shippingAddress : {}), // Use shippingAddress here
            }));
        } else {
            setBillingAddress(prev => ({ ...prev, [name]: value }));
        }
    };

    const handlePaymentChange = (e) => {
        // const { name, value } = e.target;
        // setPayment(prev => ({ ...prev, [name]: value }));

        async function postInitailOrder() {
            console.log(localStorage.getItem('cartId'));
            const cartId = localStorage.getItem('cartId');

            const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/checkout/initialize`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('userAuthToken')}`,
                },
                body: JSON.stringify({
                    cart_id: cartId,
                    shipping_address: shippingAddress,
                    billing_address: billingAddress,
                    payment_method: payment.method,
                    notes: "Please include tax invoice with the delivery"
                })
            });

            const data = await response.json();
            console.log(data);
        }

    };
    // postInitailOrder();

    const placeOrder = async () => {
        setIsSubmitting(true);
        // Simulate an API call with a delay
        try {
            await new Promise(resolve => setTimeout(resolve, 2500)); // Simulate 2.5-second delay
            // In a real application, you'd send the data to your server here and get an order confirmation
            const orderId = `ORDER-${Math.random().toString(36).substring(7).toUpperCase()}`; // Generate a mock order ID
            setOrderConfirmation({ orderId, total });
        } catch (error) {
            console.error("Failed to place order:", error);
            alert("Failed to place order. Please try again."); // Basic error feedback
        } finally {
            setIsSubmitting(false);
        }
    };

    // Animation variants
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.2, // Stagger the appearance of children
            },
        },
    };

    const itemVariants = {
        hidden: { x: -20, opacity: 0 },
        visible: { x: 0, opacity: 1 },
        exit: { x: 20, opacity: 0 },
    };

    const shippingAddressForm = <>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label htmlFor="billingName" className="block text-gray-700 text-sm font-bold mb-2">
                    Full Name
                </label>
                <input
                    type="text"
                    id="billingName"
                    name="name"
                    value={shippingAddress.name}
                    onChange={handleShippingChange}
                    className="shadow appearance-none border rounded w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus-shadow-outline"
                    required={!shippingAddress.sameAsShipping}
                />
            </div>
            <div>
                <label htmlFor="billingStreet" className="block text-gray-700 text-sm font-bold mb-2">
                    Street Address
                </label>
                <input
                    type="text"
                    id="billingStreet"
                    name="street"
                    value={shippingAddress.street}
                    onChange={handleShippingChange}
                    className="shadow appearance-none border rounded w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus-shadow-outline"
                    required={!shippingAddress.sameAsShipping}
                />
            </div>
            <div>
                <label htmlFor="billingCity" className="block text-gray-700 text-sm font-bold mb-2">
                    City
                </label>
                <input
                    type="text"
                    id="billingCity"
                    name="city"
                    value={shippingAddress.city}
                    onChange={handleShippingChange}
                    className="shadow appearance-none border rounded w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus-shadow-outline"
                    required={!shippingAddress.sameAsShipping}
                />
            </div>
            <div>
                <label htmlFor="billingState" className="block text-gray-700 text-sm font-bold mb-2">
                    State
                </label>
                <input
                    type="text"
                    id="billingState"
                    name="state"
                    value={shippingAddress.state}
                    onChange={handleShippingChange}
                    className="shadow appearance-none border rounded w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus-shadow-outline"
                    required={!shippingAddress.sameAsShipping}
                />
            </div>
            <div>
                <label htmlFor="billingZip" className="block text-gray-700 text-sm font-bold mb-2">
                    ZIP Code
                </label>
                <input
                    type="text"
                    id="billingZip"
                    name="zip"
                    value={shippingAddress.zip}
                    onChange={handleShippingChange}
                    className="shadow appearance-none border rounded w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus-shadow-outline"
                    required={!shippingAddress.sameAsShipping}
                />
            </div>
            <div>
                <label htmlFor="billingCountry" className="block text-gray-700 text-sm font-bold mb-2">
                    Country
                </label>
                <select
                    id="billingCountry"
                    name="country"
                    value={shippingAddress.country}
                    onChange={handleShippingChange}
                    className="shadow appearance-none border rounded w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus-shadow-outline"
                    required={!shippingAddress.sameAsShipping}
                >
                    <option value="">Select a country</option>
                    <option value="USA">United States</option>
                    <option value="Canada">Canada</option>
                    <option value="UK">United Kingdom</option>
                    <option value="AUS">Australia</option>
                    {/* Add more countries */}
                </select>
            </div>
        </div>
    </>

    const renderStep = () => {
        switch (step) {
            case 1:
                return (
                    <motion.div variants={itemVariants} className="space-y-6">
                        <h2 className="text-2xl font-semibold text-gray-800 flex items-center gap-2">
                            <PiMapPinFill className="w-6 h-6 text-indigo-500" />
                            Shipping Address
                        </h2>

                        {isLoadingAddresses ? (
                            <div className="flex justify-center items-center py-8">
                                <motion.div
                                    animate={{
                                        rotate: 360
                                    }}
                                    transition={{
                                        duration: 1,
                                        repeat: Infinity,
                                        ease: "linear"
                                    }}
                                    className="w-8 h-8 border-4 border-indigo-400 border-t-transparent rounded-full"
                                />
                            </div>
                        ) : addressError ? (
                            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                                {addressError}
                            </div>
                        ) : customerAddresses.length > 0 ? (
                            <div className="space-y-6 w-full">
                                <div className="grid grid-cols-1 gap-6 w-full">
                                    {customerAddresses.map((address) => (
                                        <motion.div
                                            key={address.id}
                                            whileHover={{ scale: 1.02 }}
                                            className={`border rounded-xl p-6 cursor-pointer transition-all duration-200 w-full ${shippingAddress.name === address.contact_name
                                                ? 'border-indigo-500 bg-indigo-50 shadow-md'
                                                : 'border-gray-200 hover:border-indigo-300 hover:shadow-md'
                                                }`}
                                            onClick={() => {
                                                const formattedAddress = {
                                                    name: address.contact_name,
                                                    street: address.address_line1,
                                                    street2: address.address_line2,
                                                    city: address.city,
                                                    state: address.state,
                                                    zip: address.postal_code,
                                                    country: address.country,
                                                    email: address.contact_email,
                                                    phone: address.contact_phone,
                                                    company: address.company_name,
                                                    instructions: address.delivery_instructions
                                                };
                                                setShippingAddress(formattedAddress);
                                                if (billingAddress.sameAsShipping) {
                                                    setBillingAddress(prev => ({
                                                        ...prev,
                                                        ...formattedAddress
                                                    }));
                                                }
                                            }}
                                        >
                                            <div className="space-y-4 w-full">
                                                <div className="flex justify-between items-start w-full">
                                                    <div className="flex items-center gap-3">
                                                        <div className={`w-3 h-3 rounded-full ${shippingAddress.name === address.contact_name
                                                            ? 'bg-indigo-500'
                                                            : 'bg-gray-300'
                                                            }`} />
                                                        <h3 className="font-semibold text-lg text-gray-900">
                                                            {address.contact_name}
                                                        </h3>
                                                    </div>
                                                    {address.is_default && (
                                                        <span className="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full font-medium">
                                                            Default
                                                        </span>
                                                    )}
                                                </div>

                                                <div className="space-y-2 text-gray-600 w-full">
                                                    {address.company_name && (
                                                        <p className="font-medium text-gray-700">
                                                            {address.company_name}
                                                        </p>
                                                    )}
                                                    <p>{address.address_line1}</p>
                                                    {address.address_line2 && (
                                                        <p>{address.address_line2}</p>
                                                    )}
                                                    <p>{address.city}, {address.state} {address.postal_code}</p>
                                                    <p>{address.country}</p>
                                                </div>

                                                <div className="pt-3 border-t border-gray-100 space-y-2 w-full">
                                                    <div className="flex items-center gap-2 text-gray-600">
                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                                        </svg>
                                                        <span>{address.contact_email}</span>
                                                    </div>
                                                    <div className="flex items-center gap-2 text-gray-600">
                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                                        </svg>
                                                        <span>{address.contact_phone}</span>
                                                    </div>
                                                    {address.delivery_instructions && (
                                                        <div className="flex items-start gap-2 text-gray-600">
                                                            <svg className="w-4 h-4 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                            </svg>
                                                            <span className="text-sm">{address.delivery_instructions}</span>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </motion.div>
                                    ))}
                                </div>

                                <div className="text-center pt-4 w-full">
                                    <motion.button
                                        whileHover={{ scale: 1.05 }}
                                        whileTap={{ scale: 0.95 }}
                                        onClick={() => { setShowNewShippingAddress(!showNewShippingAddress) }}
                                        className="inline-flex items-center gap-2 bg-indigo-50 text-indigo-600 hover:bg-indigo-100 px-6 py-3 rounded-lg font-medium transition-colors"
                                    >
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                        </svg>
                                        Add New Address
                                    </motion.button>
                                </div>

                                {showNewShippingAddress && (
                                    <div className="text-center py-8 bg-gray-50 rounded-xl">
                                        <div className="mb-6">
                                            <svg className="w-16 h-16 mx-auto text-indigo-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <h3 className="text-lg font-medium text-gray-900 mb-2">Complete Your Profile First</h3>
                                            <p className="text-gray-600 mb-6">Please complete your profile and add your address information before proceeding with checkout.</p>
                                        </div>
                                        <motion.button
                                            whileHover={{ scale: 1.05 }}
                                            whileTap={{ scale: 0.95 }}
                                            onClick={() => window.location.href = '/customer-admin/profile'}
                                            className="inline-flex items-center gap-2 bg-indigo-500 text-white hover:bg-indigo-600 px-6 py-3 rounded-lg font-medium transition-colors"
                                        >
                                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                            Go to Profile
                                        </motion.button>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <div className="text-center py-12 bg-gray-50 rounded-xl w-full">
                                <svg className="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                <h3 className="text-lg font-medium text-gray-900 mb-2">No Addresses Found</h3>
                                <p className="text-gray-600 mb-6">Add your first delivery address to continue with checkout</p>

                                {<div className="text-center pt-4 w-full">
                                    <motion.button
                                        whileHover={{ scale: 1.05 }}
                                        whileTap={{ scale: 0.95 }}
                                        onClick={() => window.location.href = '/customer-admin/profile'}
                                        className="inline-flex items-center gap-2 bg-indigo-500 text-white hover:bg-indigo-600 px-6 py-3 rounded-lg font-medium transition-colors"
                                    >
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                        </svg>
                                        Complete Profile & Add Address
                                    </motion.button>
                                </div>}
                            </div>
                        )}

                        <div className="flex justify-end">
                            <button
                                onClick={nextStep}
                                className="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-3 px-6 rounded focus:outline-none focus:shadow-outline flex items-center gap-2"
                            >
                                Next <TiArrowRightThick className="w-5 h-5" />
                            </button>
                        </div>
                    </motion.div>
                );
            case 2:
                return (
                    <motion.div variants={itemVariants} className="space-y-6">
                        <h2 className="text-2xl font-semibold text-gray-800 flex items-center gap-2">
                            <GiWallet className="w-6 h-6 text-indigo-500" />
                            Billing Address
                        </h2>
                        <div className="mb-6">
                            <label className="inline-flex items-center">
                                <input
                                    type="checkbox"
                                    name="sameAsShipping"
                                    checked={billingAddress.sameAsShipping}
                                    onChange={handleBillingChange}
                                    className="form-checkbox h-6 w-6 text-indigo-600 rounded"
                                />
                                <span className="ml-3 text-gray-700 text-lg">Same as Shipping Address</span>
                            </label>
                        </div>
                        {!billingAddress.sameAsShipping && (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label htmlFor="billingName" className="block text-gray-700 text-sm font-bold mb-2">
                                        Full Name
                                    </label>
                                    <input
                                        type="text"
                                        id="billingName"
                                        name="name"
                                        value={billingAddress.name}
                                        onChange={handleBillingChange}
                                        className="shadow appearance-none border rounded w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus-shadow-outline"
                                        required={!billingAddress.sameAsShipping}
                                    />
                                </div>
                                <div>
                                    <label htmlFor="billingStreet" className="block text-gray-700 text-sm font-bold mb-2">
                                        Street Address
                                    </label>
                                    <input
                                        type="text"
                                        id="billingStreet"
                                        name="street"
                                        value={billingAddress.street}
                                        onChange={handleBillingChange}
                                        className="shadow appearance-none border rounded w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus-shadow-outline"
                                        required={!billingAddress.sameAsShipping}
                                    />
                                </div>
                                <div>
                                    <label htmlFor="billingCity" className="block text-gray-700 text-sm font-bold mb-2">
                                        City
                                    </label>
                                    <input
                                        type="text"
                                        id="billingCity"
                                        name="city"
                                        value={billingAddress.city}
                                        onChange={handleBillingChange}
                                        className="shadow appearance-none border rounded w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus-shadow-outline"
                                        required={!billingAddress.sameAsShipping}
                                    />
                                </div>
                                <div>
                                    <label htmlFor="billingState" className="block text-gray-700 text-sm font-bold mb-2">
                                        State
                                    </label>
                                    <input
                                        type="text"
                                        id="billingState"
                                        name="state"
                                        value={billingAddress.state}
                                        onChange={handleBillingChange}
                                        className="shadow appearance-none border rounded w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus-shadow-outline"
                                        required={!billingAddress.sameAsShipping}
                                    />
                                </div>
                                <div>
                                    <label htmlFor="billingZip" className="block text-gray-700 text-sm font-bold mb-2">
                                        ZIP Code
                                    </label>
                                    <input
                                        type="text"
                                        id="billingZip"
                                        name="zip"
                                        value={billingAddress.zip}
                                        onChange={handleBillingChange}
                                        className="shadow appearance-none border rounded w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus-shadow-outline"
                                        required={!billingAddress.sameAsShipping}
                                    />
                                </div>
                                <div>
                                    <label htmlFor="billingCountry" className="block text-gray-700 text-sm font-bold mb-2">
                                        Country
                                    </label>
                                    <select
                                        id="billingCountry"
                                        name="country"
                                        value={billingAddress.country}
                                        onChange={handleBillingChange}
                                        className="shadow appearance-none border rounded w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus-shadow-outline"
                                        required={!billingAddress.sameAsShipping}
                                    >
                                        <option value="">Select a country</option>
                                        <option value="USA">United States</option>
                                        <option value="Canada">Canada</option>
                                        <option value="UK">United Kingdom</option>
                                        <option value="AUS">Australia</option>
                                        {/* Add more countries */}
                                    </select>
                                </div>
                            </div>
                        )}
                        <div className="flex justify-between">
                            <button
                                onClick={prevStep}
                                className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-6 rounded focus:outline-none focus-shadow-outline flex items-center gap-2"
                            >
                                <TiArrowLeftThick className="w-5 h-5" /> Back
                            </button>
                            <button
                                onClick={nextStep}
                                className="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-3 px-6 rounded focus:outline-none focus-shadow-outline flex items-center gap-2"
                            >
                                Next <TiArrowRightThick className="w-5 h-5" />
                            </button>
                        </div>
                    </motion.div>
                );
            case 3:
                return (
                    <motion.div variants={itemVariants} className="space-y-6">
                        <h2 className="text-2xl font-semibold text-gray-800 flex items-center gap-2">
                            <TbShieldCheckFilled className="w-6 h-6 text-emerald-500" />
                            Review Your Order
                        </h2>
                        {/* <div>
                            <h3 className="text-xl font-semibold text-gray-700 mb-4">Order Summary</h3>
                            <ul className="border rounded-md shadow-sm">
                                {products.map(item => (
                                    <li key={item.id} className="flex items-center justify-between py-4 px-4 border-b last:border-b-0">
                                        <div className="flex items-center">
                                            <img src={item.image} alt={item.name} className="w-16 h-16 mr-4 rounded object-cover" />
                                            <div>
                                                <span className="font-medium text-gray-800 block">{item.name}</span>
                                                <span className="text-gray-600 text-sm">Quantity: {item.quantity}</span>
                                            </div>
                                        </div>
                                        <span className="text-gray-800 text-lg">${(item.price * item.quantity).toFixed(2)}</span>
                                    </li>
                                ))}
                            </ul>
                            <div className="py-4 px-4 border-b">
                                <div className="flex justify-between mb-2">
                                    <span className="text-gray-700 text-lg">Subtotal</span>
                                    <span className="text-gray-800 text-lg">${subtotal.toFixed(2)}</span>
                                </div>
                                <div className="flex justify-between mb-2">
                                    <span className="text-gray-700 text-lg">Shipping</span>
                                    <span className="text-gray-800 text-lg">${shippingCost.toFixed(2)}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-700 text-lg">Tax ({taxRate * 100}%)</span>
                                    <span className="text-gray-800 text-lg">${tax.toFixed(2)}</span>
                                </div>
                            </div>
                            <div className="py-4 px-4 flex justify-between">
                                <span className="text-gray-900 font-bold text-xl">Total</span>
                                <span className="text-indigo-600 font-bold text-xl">${total.toFixed(2)}</span>
                            </div>
                        </div> */}

                        <div>
                            <h3 className="text-xl font-semibold text-gray-700 mb-4 flex items-center gap-2">
                                <FaTruckPlane className="w-5 h-5 text-blue-500" />
                                Shipping Address
                            </h3>
                            <div className="border rounded-md shadow-sm p-4">
                                <p className="text-gray-800 text-lg font-medium">{shippingAddress.name}</p>
                                <p className="text-gray-600">{shippingAddress.street}</p>
                                <p className="text-gray-600">{shippingAddress.city}, {shippingAddress.state} {shippingAddress.zip}</p>
                                <p className="text-gray-600">{shippingAddress.country}</p>
                            </div>
                        </div>

                        <div>
                            <h3 className="text-xl font-semibold text-gray-700 mb-4 flex items-center gap-2">
                                <GiWallet className="w-5 h-5 text-purple-500" />
                                Billing Address
                            </h3>
                            <div className="border rounded-md shadow-sm p-4">
                                {billingAddress.sameAsShipping ? (
                                    <p className="text-gray-600 text-lg">Same as shipping address</p>
                                ) : (
                                    <>
                                        <p className="text-gray-800 text-lg font-medium">{billingAddress.name}</p>
                                        <p className="text-gray-600">{billingAddress.street}</p>
                                        <p className="text-gray-600">{billingAddress.city}, {billingAddress.state} {billingAddress.zip}</p>
                                        <p className="text-gray-600">{billingAddress.country}</p>
                                    </>
                                )}
                            </div>
                        </div>

                        <div className="flex justify-between">
                            <button
                                onClick={prevStep}
                                className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-6 rounded focus:outline-none focus-shadow-outline flex items-center gap-2"
                            >
                                <TiArrowLeftThick className="w-5 h-5 text-red-500" /> Back
                            </button>
                            <button
                                onClick={nextStep}
                                className="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-3 px-6 rounded focus:outline-none focus-shadow-outline flex items-center gap-2"
                            >
                                Payment <TiArrowRightThick className="w-5 h-5 text-green-500" />
                            </button>
                        </div>
                    </motion.div>
                );
            case 4:
                return (
                    <motion.div variants={itemVariants} className="space-y-6">
                        <Payment paymentInstance={paymentInstance} billingAddress={billingAddress} shippingAddress={shippingAddress}></Payment>

                        <div className="flex justify-between">
                            <button
                                onClick={prevStep}
                                className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-6 rounded focus:outline-none focus-shadow-outline flex items-center gap-2"
                            >
                                <TiArrowLeftThick className="w-5 h-5" /> Back
                            </button>
                            <button
                                onClick={placeOrder}
                                disabled={isSubmitting}
                                className={`bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-3 px-6 rounded focus:outline-none focus-shadow-outline flex items-center gap-2 ${isSubmitting ? 'opacity-70 cursor-not-allowed' : ''}`}
                            >
                                {isSubmitting ? (
                                    <>
                                        <LuLoaderPinwheel className="animate-spin w-5 h-5" />
                                        Processing...
                                    </>
                                ) : (
                                    <>
                                        Next <MdPlaylistAddCheckCircle className="w-5 h-5" />
                                    </>
                                )}
                            </button>
                        </div>
                    </motion.div>
                );
            default:
                return null;
        }
    };

    return (
        <div className="bg-gray-100 min-h-screen sm:p-10 text-gray-600">
            <div className="mx-auto bg-white sm:rounded-xl sm:shadow-lg min-h-[90dvh] p-10">
                <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center flex items-center justify-center gap-3">
                    <FaCartShopping className="w-8 h-8 text-indigo-500" />
                    Checkout Products
                </h1>

                {orderConfirmation ? (
                    // Render order confirmation
                    <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3 }}
                        className="text-center space-y-6"
                    >
                        <MdPlaylistAddCheckCircle className="w-16 h-16 mx-auto text-green-500" />
                        <h2 className="text-2xl font-semibold text-gray-900">Order Confirmed!</h2>
                        <p className="text-gray-600 text-lg">
                            Thank you for your purchase. Your order ID is:
                            <span className="font-medium text-indigo-600 ml-1">{orderConfirmation.orderId}</span>
                        </p>
                        <p className="text-gray-600 text-lg">
                            Your total was: <span className="font-medium text-indigo-600">${orderConfirmation.total.toFixed(2)}</span>
                        </p>
                        {/* Add a link to a "View Orders" page if you have one */}
                        <a
                            href="/" // Replace with your actual home page or shop page URL
                            className="inline-block bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-3 px-6 rounded focus:outline-none focus:shadow-outline"
                        >
                            Continue Shopping
                        </a>
                    </motion.div>
                ) : (
                    // Render checkout form
                    <motion.div
                        variants={containerVariants}
                        initial="hidden"
                        animate="visible"
                        className="space-y-8"
                    >
                        <div className="flex flex-wrap justify-between items-center border-b pb-4 gap-5">
                            <div className="flex items-center gap-2">
                                {step >= 1 ? <MdPlaylistAddCheckCircle className="w-6 h-6 text-green-500" /> : <FaTruckPlane className="w-6 h-6 text-gray-500" />}
                                <span className={step >= 1 ? "text-lg font-medium text-gray-900" : "text-lg font-medium text-gray-500"}>Shipping</span>
                            </div>
                            <div className="flex items-center gap-2">
                                {step >= 2 ? <MdPlaylistAddCheckCircle className="w-6 h-6 text-green-500" /> : <GiWallet className="w-6 h-6 text-gray-500" />}
                                <span className={step >= 2 ? "text-lg font-medium text-gray-900" : "text-lg font-medium text-gray-500"}>Billing</span>
                            </div>
                            <div className="flex items-center gap-2">
                                {step >= 3 ? <MdPlaylistAddCheckCircle className="w-6 h-6 text-green-500" /> : <BsCreditCard2FrontFill className="w-6 h-6 text-gray-500" />}
                                <span className={step >= 3 ? "text-lg font-medium text-gray-900" : "text-lg font-medium text-gray-500"}>Payment</span>
                            </div>
                            <div className="flex items-center gap-2">
                                {step >= 4 ? <MdPlaylistAddCheckCircle className="w-6 h-6 text-green-500" /> : <FaLock className="w-6 h-6 text-gray-500" />}
                                <span className={step >= 4 ? "text-lg font-medium text-gray-900" : "text-lg font-medium text-gray-500"}>Review</span>
                            </div>
                        </div>
                        <AnimatePresence mode='wait'>
                            {renderStep()}
                        </AnimatePresence>
                    </motion.div>
                )}
            </div>
        </div>
    );
};

export default Checkout;
