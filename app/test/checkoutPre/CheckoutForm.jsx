import { CardElement, useElements, useStripe } from '@stripe/react-stripe-js'
import React, { useState, useEffect } from 'react'
import toast from 'react-hot-toast';
import { BsCreditCard2FrontFill } from 'react-icons/bs';
import { FaHandHoldingDollar } from "react-icons/fa6";
import Swal from 'sweetalert2';

export default function CheckoutForm({ paymentInstance, billingAddress, shippingAddress }) {
    // Stripe hooks
    const stripe = useStripe();
    const elements = useElements();

    // Component state
    const [error, setError] = useState("");
    const [transactionId, setTransactionId] = useState("");
    const [isProcessing, setIsProcessing] = useState(false);
    const [isFormValid, setIsFormValid] = useState(false);

    // Validate payment instance data
    useEffect(() => {
        if (!paymentInstance?.data) {
            setError("Invalid payment instance");
            return;
        }

        const { amount, client_secret, currency, payment_intent_id } = paymentInstance.data;

        if (!amount || !client_secret || !currency || !payment_intent_id) {
            setError("Missing required payment details");
            return;
        }

        setIsFormValid(true);
    }, [paymentInstance]);

    // Validate addresses
    useEffect(() => {
        if (!billingAddress?.name || !shippingAddress) {
            setError("Missing address information");
            setIsFormValid(false);
        }
    }, [billingAddress, shippingAddress]);

    const { amount, client_secret, currency, payment_intent_id } = paymentInstance?.data || {};

    async function handleSubmit(e) {
        e.preventDefault();

        if (!isFormValid) {
            setError("Please fix form errors before submitting");
            return;
        }

        if (isProcessing) {
            return; // Prevent multiple submissions
        }

        // Validate required elements
        if (!stripe || !elements) {
            setError("Payment system not loaded yet");
            return;
        }

        const card = elements.getElement(CardElement);
        if (!card) {
            setError("Card element not found");
            return;
        }

        setIsProcessing(true);
        setError("");

        try {
            // Process payment
            const { paymentIntent, error: confirmError } = await stripe.confirmCardPayment(
                client_secret,
                {
                    payment_method: {
                        card: card,
                        billing_details: { name: billingAddress?.name || "anonymous" },
                        // task!! need to add user info from auth context
                    }
                }
            );
            console.log({ paymentIntent }, { confirmError });


            if (confirmError) {
                throw new Error(confirmError.message);
            }

            if (!paymentIntent) {
                throw new Error("Payment failed - No payment intent returned");
            }

            if (paymentIntent.status === "succeeded" && paymentIntent.id) {
                setTransactionId(paymentIntent.id);

                toast.success(`Payment successful! Transaction ID: ${paymentIntent.id}`, {
                    autoClose: 5000,
                    hideProgressBar: false,
                    closeOnClick: true,
                    pauseOnHover: true,
                    draggable: true,
                    progress: undefined,
                });

                const cartId = localStorage.getItem("cartId");
                if (!cartId) {
                    throw new Error("Cart ID not found");
                }

                const payment = {
                    payment_intent: paymentIntent,
                    payment_intent_id: paymentIntent.id,
                    cart_id: cartId,
                    shipping_address: shippingAddress,
                    billing_address: billingAddress,
                    payment_method: paymentIntent.payment_method_types,
                    notes: "Please include tax invoice with the delivery"
                };

                const token = localStorage.getItem("userAuthToken");
                if (!token) {
                    throw new Error("Authentication token not found");
                }

                const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/checkout/process`, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "Authorization": `Bearer ${token}`,
                    },
                    body: JSON.stringify(payment)
                });
                const result = await response.json();
                console.log("After Payment Order Process result:", result);


                if (result.success === "success") {
                    toast.success('Payment stored successfully!');
                    Swal.fire({
                        title: "Payment Successful!",
                        text: "Your payment has been processed successfully.",
                        icon: "success",
                        confirmButtonText: "OK",
                        timer: 5000,
                        timerProgressBar: true,
                    });
                } else {
                    toast.error(result.message || 'Failed to store payment in database', {
                        hideProgressBar: false,
                        closeOnClick: true,
                        pauseOnHover: true,
                        draggable: true,
                        progress: undefined,
                    });

                    Swal.fire({
                        title: "Payment Failed!",
                        text: result.message || "Your payment has failed to stroe in Database.",
                        icon: "error",
                        confirmButtonText: "OK",
                        timerProgressBar: true,
                    })
                }
            }
        } catch (err) {
            setError(err.message);
        }
        setIsProcessing(false);
    }

    return (
        <div>
            <div className="bg-white p-8 rounded-xl shadow-lg border border-gray-200 hover:shadow-xl transition-shadow duration-300 mb-10">
                <h2 className="text-3xl font-bold text-gray-800 flex items-center gap-4 mb-8">
                    <BsCreditCard2FrontFill className="w-10 h-10 text-indigo-600" />
                    Payment Details
                </h2>
                <div className="flex items-center justify-between p-6 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl text-xl">
                    <span className="text-gray-700 font-semibold">Total Amount</span>
                    <div className="flex items-center gap-2">
                        <FaHandHoldingDollar className="w-8 h-8 text-indigo-600" />
                        <span className="text-2xl font-bold text-gray-900">
                            ${amount?.toFixed(3) || '0.00'}
                        </span>
                    </div>
                </div>
            </div>

            <form onSubmit={handleSubmit}>
                <CardElement
                    options={{
                        style: {
                            base: {
                                fontSize: '18px',
                                color: '#2D3748',
                                fontFamily: '"Inter", system-ui, sans-serif',
                                fontSmoothing: 'antialiased',
                                '::placeholder': {
                                    color: '#A0AEC0',
                                    fontStyle: 'italic'
                                },
                                backgroundColor: '#fff',
                                padding: '16px',
                                borderRadius: '12px',
                                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                            },
                            invalid: {
                                color: '#E53E3E',
                                iconColor: '#E53E3E'
                            },
                        },
                        hidePostalCode: true
                    }}
                    className="p-6 mb-5 border-2 border-gray-200 rounded-xl shadow-md hover:shadow-lg focus:ring-4 focus:ring-blue-400 focus:border-blue-500 focus:outline-none transition-all duration-300 ease-in-out bg-white"
                    onChange={(e) => {
                        if (e.error) {
                            setError(e.error.message);
                        } else {
                            setError("");
                        }
                    }}
                />

                <button
                    className="px-6 py-3 my-10 text-white font-semibold rounded-lg bg-gradient-to-r from-blue-500 to-blue-700 hover:from-blue-600 hover:to-blue-800 transform hover:scale-105 transition-all duration-200 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:hover:scale-100 text-lg"
                    type="submit"
                    disabled={!stripe || isProcessing}
                >
                    <span className="flex items-center justify-center gap-2">
                        {isProcessing ? (
                            <svg className="animate-spin h-5 w-5 mr-3" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                            </svg>
                        ) : (
                            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                        )}
                        {isProcessing ? 'Processing...' : 'Pay Now'}
                    </span>
                </button>
            </form>

            {error && (
                <div className="p-4 mb-4 rounded-lg bg-red-50 border border-red-200">
                    <div className="flex items-center gap-3">
                        <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <p className="font-medium text-red-700">{error}</p>
                    </div>
                </div>
            )}

            {transactionId && (
                <div className="p-4 mb-4 rounded-lg bg-green-50 border border-green-200">
                    <div className="flex items-center gap-3">
                        <svg className="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                        </svg>
                        <div>
                            <p className="font-medium text-green-700">Payment Successful!</p>
                            <p className="text-sm text-green-600">Transaction ID: <span className="font-mono">{transactionId}</span></p>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
