// task!(delete) this is previous ui code.

"use client";

import React, { useEffect, useState } from 'react';
import {
    ShoppingBagIcon,
    MagnifyingGlassIcon,
    FunnelIcon,
    ChevronDownIcon,
    ChevronUpIcon,
    CheckCircleIcon,
    TruckIcon,
    ClockIcon,
    ExclamationTriangleIcon,
    XCircleIcon,
    UserIcon,
    CalendarIcon,
    CreditCardIcon,
    BanknotesIcon,
    CurrencyDollarIcon,
    ShieldCheckIcon,
    PhoneIcon,
    EnvelopeIcon,
    MapPinIcon,
    DocumentTextIcon,
    PaperAirplaneIcon,
    TagIcon,
    ArrowPathIcon,
    ArrowsUpDownIcon
} from '@heroicons/react/24/outline';

const ordersData = [
    {
        orderId: "ORD-2024-001",
        userId: "USR-001",
        items: [
            {
                productId: "PROD-001",
                productName: "Organic Green Tea (200g)",
                quantity: 2,
                perPackPrice: 12.99
            },
            {
                productId: "PROD-002",
                productName: "Herbal Infusion Set",
                quantity: 1,
                perPackPrice: 24.50
            }
        ],
        summary: {
            subtotal: 50.48,
            tax: 4.55, // 9% sales tax
            serviceCharge: 2.50, // flat service fee
            shippingCost: 5.99,
            discount: 5.00, // $5 off promo
            total: 58.52
        },
        payment: {
            transactionId: "TXN-001",
            method: "credit_card",
            status: "paid",
            details: {
                cardType: "Visa",
                cardLast4: "4242"
            },
            paidAt: "2024-05-15T10:30:00Z"
        },
        status: "delivered",
        shipping: {
            address: {
                recipientName: "John Doe",
                phone: "******-555-7890",
                street: "123 Main St, Apt 4B",
                city: "New York",
                state: "NY",
                zip: "10001",
                country: "USA"
            },
            method: "standard",
            carrier: "FedEx",
            trackingNumber: "FDX987654321",
            estimatedDelivery: "2024-05-18",
            deliveredAt: "2024-05-17T14:23:00Z"
        },
        createdAt: "2024-05-15T10:15:32Z",
        updatedAt: "2024-05-17T14:23:00Z"
    },
    {
        orderId: "ORD-2024-002",
        userId: "USR-002",
        items: [
            {
                productId: "PROD-003",
                productName: "Premium Loose Leaf Collection",
                quantity: 3,
                perPackPrice: 18.99
            }
        ],
        summary: {
            subtotal: 56.97,
            tax: 5.13, // 9% sales tax
            serviceCharge: 2.50, // flat service fee
            shippingCost: 0, // free shipping promo
            discount: 0,
            total: 64.60
        },
        payment: {
            transactionId: "TXN-002",
            method: "paypal",
            status: "paid",
            details: {
                paypalEmail: "<EMAIL>"
            },
            paidAt: "2024-05-16T15:45:00Z"
        },
        status: "shipped",
        shipping: {
            address: {
                recipientName: "Jane Smith",
                phone: "******-555-1234",
                street: "456 Oak Ave",
                city: "Los Angeles",
                state: "CA",
                zip: "90001",
                country: "USA"
            },
            method: "express",
            carrier: "UPS",
            trackingNumber: "UPS987654321",
            estimatedDelivery: "2024-05-19",
            deliveredAt: null
        },
        createdAt: "2024-05-16T15:30:22Z",
        updatedAt: "2024-05-17T09:15:00Z"
    },
    {
        orderId: "ORD-2024-003",
        userId: "USR-003",
        items: [
            {
                productId: "PROD-004",
                productName: "Signature Tea Gift Set",
                quantity: 1,
                perPackPrice: 49.99
            },
            {
                productId: "PROD-007",
                productName: "Ceramic Tea Infuser",
                quantity: 1,
                perPackPrice: 14.99
            }
        ],
        summary: {
            subtotal: 64.98,
            tax: 5.85, // 9% sales tax
            serviceCharge: 2.50, // flat service fee
            shippingCost: 8.99, // international shipping
            discount: 0,
            total: 82.32
        },
        payment: {
            transactionId: "TXN-003",
            method: "bank_transfer",
            status: "pending",
            details: {
                bankName: "Global Bank",
                referenceNumber: "BT789012345"
            },
            paidAt: null
        },
        status: "processing",
        shipping: {
            address: {
                recipientName: "Robert Johnson",
                phone: "+44-20-1234-5678",
                street: "789 Pine Lane",
                city: "London",
                state: "",
                zip: "SW1A 1AA",
                country: "United Kingdom"
            },
            method: "standard",
            carrier: "",
            trackingNumber: "",
            estimatedDelivery: "2024-05-22",
            deliveredAt: null
        },
        createdAt: "2024-05-17T08:45:12Z",
        updatedAt: "2024-05-17T12:30:00Z"
    },
    {
        orderId: "ORD-2024-004",
        userId: "USR-004",
        items: [
            {
                productId: "PROD-005",
                productName: "Monthly Tea Subscription Box",
                quantity: 1,
                perPackPrice: 29.99
            },
            {
                productId: "PROD-006",
                productName: "Electric Kettle (1.7L)",
                quantity: 1,
                perPackPrice: 59.99
            }
        ],
        summary: {
            subtotal: 89.98,
            tax: 8.10, // 9% sales tax
            serviceCharge: 2.50, // flat service fee
            shippingCost: 5.99,
            discount: 10.00, // $10 off for new subscribers
            total: 96.57
        },
        payment: {
            transactionId: "TXN-004",
            method: "credit_card",
            status: "failed",
            details: {
                cardType: "Mastercard",
                cardLast4: "8765",
                error: "Insufficient funds"
            },
            paidAt: null
        },
        status: "cancelled",
        shipping: {
            address: {
                recipientName: "Mary Wilson",
                phone: "******-555-9876",
                street: "321 Elm St",
                city: "Houston",
                state: "TX",
                zip: "77001",
                country: "USA"
            },
            method: "standard",
            carrier: "",
            trackingNumber: "",
            estimatedDelivery: null,
            deliveredAt: null
        },
        createdAt: "2024-05-16T19:20:45Z",
        updatedAt: "2024-05-16T19:35:12Z"
    },
    {
        orderId: "ORD-2024-005",
        userId: "USR-005",
        items: [
            {
                productId: "PROD-008",
                productName: "Tea Master Pro Brewing System",
                quantity: 1,
                perPackPrice: 89.99
            },
            {
                productId: "PROD-009",
                productName: "Premium Tea Sampler Pack",
                quantity: 2,
                perPackPrice: 22.50
            }
        ],
        summary: {
            subtotal: 134.99,
            tax: 12.15, // 9% sales tax
            serviceCharge: 2.50, // flat service fee
            shippingCost: 0, // free shipping on orders over $100
            discount: 15.00, // seasonal promotion
            total: 134.64
        },
        payment: {
            transactionId: "TXN-005",
            method: "credit_card",
            status: "paid",
            details: {
                cardType: "American Express",
                cardLast4: "1234"
            },
            paidAt: "2024-05-17T09:15:00Z"
        },
        status: "pending",
        shipping: {
            address: {
                recipientName: "David Brown",
                phone: "+61-2-9876-5432",
                street: "567 Maple Lane",
                city: "Sydney",
                state: "NSW",
                zip: "2000",
                country: "Australia"
            },
            method: "express",
            carrier: "DHL",
            trackingNumber: "DHL123456789",
            estimatedDelivery: "2024-05-24",
            deliveredAt: null
        },
        createdAt: "2024-05-17T09:05:30Z",
        updatedAt: "2024-05-17T10:20:15Z"
    }
];

export default function OrdersPage() {
    const [orders, setOrders] = useState([]);
    const [expandedOrderId, setExpandedOrderId] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [sortField, setSortField] = useState('createdAt');
    const [sortDirection, setSortDirection] = useState('desc');

    async function getOrders() {
        try {
            const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/admin/orders`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('superAdminAuthToken')}`
                }
            });
            const data = await response.json();
            console.log(data);

            // setOrders(data);
        } catch (error) {
            console.error('Error fetching orders:', error);
        }
    }

    useEffect(() => {
        getOrders();
        setOrders(ordersData);
    }, []);

    // Format date to a readable format
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    // Format price to USD
    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(price);
    };

    // Handle sorting
    const handleSort = (field) => {
        if (sortField === field) {
            // If already sorting by this field, toggle direction
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            // New field, set to default sort direction
            setSortField(field);
            setSortDirection('desc');
        }
    };

    // Sort icon based on current sort status
    const getSortIcon = (field) => {
        if (sortField !== field) {
            return <ArrowsUpDownIcon className="w-4 h-4 ml-1 text-gray-400" />;
        }
        return sortDirection === 'asc'
            ? <ChevronUpIcon className="w-4 h-4 ml-1 text-indigo-600" />
            : <ChevronDownIcon className="w-4 h-4 ml-1 text-indigo-600" />;
    };

    // Get color for payment status
    const getStatusColor = (status) => {
        switch (status) {
            case 'delivered':
                return 'bg-green-100 text-green-800';
            case 'shipped':
                return 'bg-blue-100 text-blue-800';
            case 'processing':
                return 'bg-yellow-100 text-yellow-800';
            case 'pending':
                return 'bg-purple-100 text-purple-800';
            case 'cancelled':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    // Get status icon
    const getStatusIcon = (status) => {
        switch (status) {
            case 'delivered':
                return <CheckCircleIcon className="w-4 h-4 mr-1" />;
            case 'shipped':
                return <TruckIcon className="w-4 h-4 mr-1" />;
            case 'processing':
                return <ArrowPathIcon className="w-4 h-4 mr-1" />;
            case 'pending':
                return <ClockIcon className="w-4 h-4 mr-1" />;
            case 'cancelled':
                return <XCircleIcon className="w-4 h-4 mr-1" />;
            default:
                return null;
        }
    };

    // Get payment method display and icon
    const getPaymentMethodDisplay = (method) => {
        switch (method) {
            case 'credit_card':
                return {
                    text: 'Credit Card',
                    icon: <CreditCardIcon className="w-4 h-4 mr-1" />
                };
            case 'paypal':
                return {
                    text: 'PayPal',
                    icon: <ShieldCheckIcon className="w-4 h-4 mr-1" />
                };
            case 'bank_transfer':
                return {
                    text: 'Bank Transfer',
                    icon: <BanknotesIcon className="w-4 h-4 mr-1" />
                };
            default:
                return {
                    text: method,
                    icon: <CurrencyDollarIcon className="w-4 h-4 mr-1 text-green-500" />
                };
        }
    };

    // Toggle order details expansion
    const toggleOrderDetails = (orderId) => {
        if (expandedOrderId === orderId) {
            setExpandedOrderId(null);
        } else {
            setExpandedOrderId(orderId);
        }
    };

    // Filter and sort orders based on search term, status and sort settings
    const filteredOrders = orders.filter(order => {
        const matchesSearch = order.orderId.toLowerCase().includes(searchTerm.toLowerCase()) ||
            order.shipping.address.recipientName.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesStatus = statusFilter === 'all' || order.status === statusFilter;

        return matchesSearch && matchesStatus;
    }).sort((a, b) => {
        // Sort based on selected field
        if (sortField === 'total') {
            return sortDirection === 'asc'
                ? a.summary.total - b.summary.total
                : b.summary.total - a.summary.total;
        } else if (sortField === 'createdAt') {
            return sortDirection === 'asc'
                ? new Date(a.createdAt) - new Date(b.createdAt)
                : new Date(b.createdAt) - new Date(a.createdAt);
        } else if (sortField === 'items') {
            return sortDirection === 'asc'
                ? a.items.length - b.items.length
                : b.items.length - a.items.length;
        }
        return 0;
    });

    return (
        <div className="px-4 sm:px-6 lg:px-8 py-8 text-gray-600">
            <div className="sm:flex sm:items-center">
                <div className="sm:flex-auto">
                    <h1 className="text-3xl font-semibold text-gray-900 flex items-center">
                        <ShoppingBagIcon className="w-7 h-7 mr-2" />
                        Orders
                    </h1>
                    <p className="mt-2 text-base text-gray-700">
                        A list of all orders including their ID, customer, status, date, and total.
                    </p>
                </div>
            </div>

            {/* Search and Filter Controls */}
            <div className="mt-4 flex flex-col sm:flex-row gap-4" role="search" aria-label="Orders search">
                <div className="relative rounded-md shadow-sm flex-1">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                    </div>
                    <label htmlFor="search-orders" className="sr-only">Search orders</label>
                    <input
                        id="search-orders"
                        type="text"
                        className="block w-full pl-10 rounded-md border-gray-300 py-2 px-3 focus:border-indigo-500 focus:ring-indigo-500 text-base"
                        placeholder="Search by order ID or customer name"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        aria-label="Search orders by ID or customer name"
                    />
                </div>
                <div className="w-full sm:w-48">
                    <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <FunnelIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                        </div>
                        <label htmlFor="status-filter" className="sr-only">Filter by status</label>
                        <select
                            id="status-filter"
                            value={statusFilter}
                            onChange={(e) => setStatusFilter(e.target.value)}
                            className="block w-full pl-10 rounded-md border-gray-300 py-2 px-3 focus:border-indigo-500 focus:ring-indigo-500 text-base appearance-none"
                            aria-label="Filter orders by status"
                        >
                            <option value="all">All Statuses</option>
                            <option value="delivered">Delivered</option>
                            <option value="shipped">Shipped</option>
                            <option value="processing">Processing</option>
                            <option value="pending">Pending</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <ChevronDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                        </div>
                    </div>
                </div>
            </div>

            {/* Orders Table */}
            <div className="mt-8 flex flex-col">
                <div className="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
                        <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                            <table className="min-w-full divide-y divide-gray-300" role="table" aria-label="Orders list">
                                <caption className="sr-only">List of customer orders</caption>
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-base font-semibold text-gray-900 sm:pl-6">
                                            <div className="flex items-center">
                                                <DocumentTextIcon className="w-5 h-5 mr-2" />
                                                Order ID
                                            </div>
                                        </th>
                                        <th scope="col" className="px-3 py-3.5 text-left text-base font-semibold text-gray-900">
                                            <div className="flex items-center">
                                                <UserIcon className="w-5 h-5 mr-2" />
                                                Customer
                                            </div>
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-3 py-3.5 text-left text-base font-semibold text-gray-900 cursor-pointer"
                                            onClick={() => handleSort('createdAt')}
                                        >
                                            <div className="flex items-center">
                                                <CalendarIcon className="w-5 h-5 mr-2" />
                                                Date
                                                {getSortIcon('createdAt')}
                                            </div>
                                        </th>
                                        <th scope="col" className="px-3 py-3.5 text-left text-base font-semibold text-gray-900">
                                            <div className="flex items-center">
                                                <ClockIcon className="w-5 h-5 mr-2" />
                                                Status
                                            </div>
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-3 py-3.5 text-left text-base font-semibold text-gray-900 cursor-pointer hover:text-indigo-600"
                                            onClick={() => handleSort('total')}
                                            aria-sort={sortField === 'total' ? (sortDirection === 'asc' ? 'ascending' : 'descending') : 'none'}
                                        >
                                            <div className="flex items-center">
                                                <CurrencyDollarIcon className="w-5 h-5 mr-2 text-green-500" />
                                                Total
                                                {getSortIcon('total')}
                                            </div>
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-3 py-3.5 text-left text-base font-semibold text-gray-900 cursor-pointer hover:text-indigo-600"
                                            onClick={() => handleSort('items')}
                                            aria-sort={sortField === 'items' ? (sortDirection === 'asc' ? 'ascending' : 'descending') : 'none'}
                                        >
                                            <div className="flex items-center">
                                                <ShoppingBagIcon className="w-5 h-5 mr-2" />
                                                Items
                                                {getSortIcon('items')}
                                            </div>
                                        </th>
                                        <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                                            <span className="sr-only">View details</span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="divide-y divide-gray-200 bg-white">
                                    {filteredOrders.map((order) => (
                                        <React.Fragment key={order.orderId}>
                                            <tr className="hover:bg-gray-50" data-order-id={order.orderId}>
                                                <td className="whitespace-nowrap py-4 pl-4 pr-3 text-base font-medium text-gray-900 sm:pl-6">
                                                    {order.orderId}
                                                </td>
                                                <td className="whitespace-nowrap px-3 py-4 text-base text-gray-600">
                                                    <div className="flex items-center">
                                                        <UserIcon className="w-5 h-5 mr-2 text-gray-400" />
                                                        {order.shipping.address.recipientName}
                                                    </div>
                                                </td>
                                                <td className="whitespace-nowrap px-3 py-4 text-base text-gray-600">
                                                    <div className="flex items-center">
                                                        <CalendarIcon className="w-5 h-5 mr-2 text-gray-400" />
                                                        <time dateTime={order.createdAt}>{formatDate(order.createdAt)}</time>
                                                    </div>
                                                </td>
                                                <td className="whitespace-nowrap px-3 py-4 text-base">
                                                    <span
                                                        className={`inline-flex items-center rounded-full px-3 py-0.5 text-sm font-semibold leading-5 ${getStatusColor(order.status)}`}
                                                        aria-label={`Status: ${order.status}`}
                                                    >
                                                        {getStatusIcon(order.status)}
                                                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                                                    </span>
                                                </td>
                                                <td className="whitespace-nowrap px-3 py-4 text-base text-gray-600">
                                                    <div className="flex items-center">
                                                        <CurrencyDollarIcon className="w-5 h-5 mr-2 text-gray-400" />
                                                        {formatPrice(order.summary.total)}
                                                    </div>
                                                </td>
                                                <td className="whitespace-nowrap px-3 py-4 text-base text-gray-600">
                                                    <div className="flex items-center">
                                                        <ShoppingBagIcon className="w-5 h-5 mr-2 text-gray-400" />
                                                        {order.items.length}
                                                    </div>
                                                </td>
                                                <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-base font-medium sm:pr-6">
                                                    <button
                                                        onClick={() => toggleOrderDetails(order.orderId)}
                                                        className="text-indigo-600 hover:text-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-md px-2 py-1 flex items-center"
                                                        aria-expanded={expandedOrderId === order.orderId}
                                                        aria-controls={`order-details-${order.orderId}`}
                                                    >
                                                        {expandedOrderId === order.orderId ? (
                                                            <>
                                                                <ChevronUpIcon className="w-5 h-5 mr-1" />
                                                                Hide Details
                                                            </>
                                                        ) : (
                                                            <>
                                                                <ChevronDownIcon className="w-5 h-5 mr-1" />
                                                                View Details
                                                            </>
                                                        )}
                                                    </button>
                                                </td>
                                            </tr>

                                            {/* Expanded Order Details */}
                                            {expandedOrderId === order.orderId && (
                                                <tr id={`order-details-${order.orderId}`} aria-label={`Details for order ${order.orderId}`}>
                                                    <td colSpan="7" className="px-6 py-4 bg-gray-50">
                                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                            {/* Order Items */}
                                                            <div>
                                                                <h3 className="text-base font-medium text-gray-900 mb-2 flex items-center" id={`items-heading-${order.orderId}`}>
                                                                    <ShoppingBagIcon className="w-5 h-5 mr-2" />
                                                                    Items
                                                                </h3>
                                                                <div className="border rounded-md overflow-hidden">
                                                                    <table className="min-w-full divide-y divide-gray-200" aria-labelledby={`items-heading-${order.orderId}`}>
                                                                        <thead className="bg-gray-100">
                                                                            <tr>
                                                                                <th scope="col" className="px-3 py-2 text-left text-sm font-medium text-gray-600 uppercase">Product</th>
                                                                                <th scope="col" className="px-3 py-2 text-left text-sm font-medium text-gray-600 uppercase">Qty</th>
                                                                                <th scope="col" className="px-3 py-2 text-left text-sm font-medium text-gray-600 uppercase">Price</th>
                                                                                <th scope="col" className="px-3 py-2 text-left text-sm font-medium text-gray-600 uppercase">Total</th>
                                                                            </tr>
                                                                        </thead>
                                                                        <tbody className="divide-y divide-gray-200 bg-white">
                                                                            {order.items.map((item) => (
                                                                                <tr key={item.productId}>
                                                                                    <td className="px-3 py-2 text-sm text-gray-900">
                                                                                        <div className="flex items-center">
                                                                                            <TagIcon className="w-4 h-4 mr-2 text-gray-400" />
                                                                                            {item.productName}
                                                                                        </div>
                                                                                    </td>
                                                                                    <td className="px-3 py-2 text-sm text-gray-600">{item.quantity}</td>
                                                                                    <td className="px-3 py-2 text-sm text-gray-600">{formatPrice(item.perPackPrice)}</td>
                                                                                    <td className="px-3 py-2 text-sm text-gray-600">{formatPrice(item.quantity * item.perPackPrice)}</td>
                                                                                </tr>
                                                                            ))}
                                                                        </tbody>
                                                                    </table>
                                                                </div>

                                                                {/* Order Summary */}
                                                                <h3 className="text-base font-medium text-gray-900 mt-4 mb-2 flex items-center" id={`summary-heading-${order.orderId}`}>
                                                                    <DocumentTextIcon className="w-5 h-5 mr-2" />
                                                                    Order Summary
                                                                </h3>
                                                                <div className="bg-white rounded-md border p-3" aria-labelledby={`summary-heading-${order.orderId}`}>
                                                                    <dl>
                                                                        <div className="flex justify-between text-sm py-1">
                                                                            <dt className="text-gray-600">Subtotal:</dt>
                                                                            <dd>{formatPrice(order.summary.subtotal)}</dd>
                                                                        </div>
                                                                        <div className="flex justify-between text-sm py-1">
                                                                            <dt className="text-gray-600">Tax:</dt>
                                                                            <dd>{formatPrice(order.summary.tax)}</dd>
                                                                        </div>
                                                                        <div className="flex justify-between text-sm py-1">
                                                                            <dt className="text-gray-600">Service Charge:</dt>
                                                                            <dd>{formatPrice(order.summary.serviceCharge)}</dd>
                                                                        </div>
                                                                        <div className="flex justify-between text-sm py-1">
                                                                            <dt className="text-gray-600">Shipping:</dt>
                                                                            <dd>{formatPrice(order.summary.shippingCost)}</dd>
                                                                        </div>
                                                                        {order.summary.discount > 0 && (
                                                                            <div className="flex justify-between text-sm py-1">
                                                                                <dt className="text-gray-600">Discount:</dt>
                                                                                <dd className="text-red-600">-{formatPrice(order.summary.discount)}</dd>
                                                                            </div>
                                                                        )}
                                                                        <div className="flex justify-between text-base font-medium py-1 border-t mt-1 pt-1">
                                                                            <dt>Total:</dt>
                                                                            <dd>{formatPrice(order.summary.total)}</dd>
                                                                        </div>
                                                                    </dl>
                                                                </div>
                                                            </div>

                                                            {/* Customer and Payment Details */}
                                                            <div>
                                                                {/* Shipping Address */}
                                                                <h3 className="text-base font-medium text-gray-900 mb-2 flex items-center" id={`shipping-heading-${order.orderId}`}>
                                                                    <MapPinIcon className="w-5 h-5 mr-2" />
                                                                    Shipping Address
                                                                </h3>
                                                                <div className="bg-white rounded-md border p-3 mb-4" aria-labelledby={`shipping-heading-${order.orderId}`}>
                                                                    <address className="not-italic">
                                                                        <p className="text-sm flex items-center">
                                                                            <UserIcon className="w-4 h-4 mr-2 text-gray-400" />
                                                                            {order.shipping.address.recipientName}
                                                                        </p>
                                                                        <p className="text-sm flex items-center mt-1">
                                                                            <MapPinIcon className="w-4 h-4 mr-2 text-gray-400" />
                                                                            {order.shipping.address.street}
                                                                        </p>
                                                                        <p className="text-sm ml-6">
                                                                            {order.shipping.address.city}, {order.shipping.address.state} {order.shipping.address.zip}
                                                                        </p>
                                                                        <p className="text-sm ml-6">{order.shipping.address.country}</p>
                                                                        <p className="text-sm mt-1 flex items-center">
                                                                            <PhoneIcon className="w-4 h-4 mr-2 text-gray-400" />
                                                                            <a href={`tel:${order.shipping.address.phone}`} className="hover:underline">
                                                                                {order.shipping.address.phone}
                                                                            </a>
                                                                        </p>
                                                                    </address>
                                                                </div>

                                                                {/* Payment Details */}
                                                                <h3 className="text-base font-medium text-gray-900 mb-2 flex items-center" id={`payment-heading-${order.orderId}`}>
                                                                    <CreditCardIcon className="w-5 h-5 mr-2" />
                                                                    Payment Details
                                                                </h3>
                                                                <div className="bg-white rounded-md border p-3 mb-4" aria-labelledby={`payment-heading-${order.orderId}`}>
                                                                    <dl>
                                                                        <div className="flex justify-between text-sm py-1">
                                                                            <dt className="text-gray-600">Method:</dt>
                                                                            <dd className="flex items-center">
                                                                                {getPaymentMethodDisplay(order.payment.method).icon}
                                                                                {getPaymentMethodDisplay(order.payment.method).text}
                                                                            </dd>
                                                                        </div>
                                                                        <div className="flex justify-between text-sm py-1">
                                                                            <dt className="text-gray-600">Status:</dt>
                                                                            <dd className={
                                                                                order.payment.status === 'paid' ? 'text-green-600 flex items-center' :
                                                                                    order.payment.status === 'pending' ? 'text-yellow-600 flex items-center' : 'text-red-600 flex items-center'
                                                                            }>
                                                                                {order.payment.status === 'paid' ? <CheckCircleIcon className="w-4 h-4 mr-1" /> :
                                                                                    order.payment.status === 'pending' ? <ClockIcon className="w-4 h-4 mr-1" /> :
                                                                                        <ExclamationTriangleIcon className="w-4 h-4 mr-1" />}
                                                                                {order.payment.status.charAt(0).toUpperCase() + order.payment.status.slice(1)}
                                                                            </dd>
                                                                        </div>
                                                                        {order.payment.paidAt && (
                                                                            <div className="flex justify-between text-sm py-1">
                                                                                <dt className="text-gray-600">Paid At:</dt>
                                                                                <dd className="flex items-center">
                                                                                    <CalendarIcon className="w-4 h-4 mr-1 text-gray-400" />
                                                                                    <time dateTime={order.payment.paidAt}>{formatDate(order.payment.paidAt)}</time>
                                                                                </dd>
                                                                            </div>
                                                                        )}
                                                                        {order.payment.details.cardType && (
                                                                            <div className="flex justify-between text-sm py-1">
                                                                                <dt className="text-gray-600">Card:</dt>
                                                                                <dd className="flex items-center">
                                                                                    <CreditCardIcon className="w-4 h-4 mr-1 text-gray-400" />
                                                                                    {order.payment.details.cardType} ending in {order.payment.details.cardLast4}
                                                                                </dd>
                                                                            </div>
                                                                        )}
                                                                        {order.payment.details.paypalEmail && (
                                                                            <div className="flex justify-between text-sm py-1">
                                                                                <dt className="text-gray-600">PayPal:</dt>
                                                                                <dd className="flex items-center">
                                                                                    <EnvelopeIcon className="w-4 h-4 mr-1 text-gray-400" />
                                                                                    <a href={`mailto:${order.payment.details.paypalEmail}`} className="hover:underline">
                                                                                        {order.payment.details.paypalEmail}
                                                                                    </a>
                                                                                </dd>
                                                                            </div>
                                                                        )}
                                                                        {order.payment.details.error && (
                                                                            <div className="flex justify-between text-sm py-1">
                                                                                <dt className="text-gray-600">Error:</dt>
                                                                                <dd className="text-red-600 flex items-center">
                                                                                    <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                                                                                    {order.payment.details.error}
                                                                                </dd>
                                                                            </div>
                                                                        )}
                                                                    </dl>
                                                                </div>

                                                                {/* Shipping Details */}
                                                                <h3 className="text-base font-medium text-gray-900 mb-2 flex items-center" id={`shipping-info-heading-${order.orderId}`}>
                                                                    <TruckIcon className="w-5 h-5 mr-2" />
                                                                    Shipping Information
                                                                </h3>
                                                                <div className="bg-white rounded-md border p-3" aria-labelledby={`shipping-info-heading-${order.orderId}`}>
                                                                    <dl>
                                                                        <div className="flex justify-between text-sm py-1">
                                                                            <dt className="text-gray-600">Method:</dt>
                                                                            <dd className="flex items-center">
                                                                                <TruckIcon className="w-4 h-4 mr-1 text-gray-400" />
                                                                                {order.shipping.method.charAt(0).toUpperCase() + order.shipping.method.slice(1)}
                                                                            </dd>
                                                                        </div>
                                                                        {order.shipping.carrier && (
                                                                            <div className="flex justify-between text-sm py-1">
                                                                                <dt className="text-gray-600">Carrier:</dt>
                                                                                <dd>{order.shipping.carrier}</dd>
                                                                            </div>
                                                                        )}
                                                                        {order.shipping.trackingNumber && (
                                                                            <div className="flex justify-between text-sm py-1">
                                                                                <dt className="text-gray-600">Tracking:</dt>
                                                                                <dd className="flex items-center">
                                                                                    <PaperAirplaneIcon className="w-4 h-4 mr-1 text-gray-400" />
                                                                                    <a
                                                                                        href="#"
                                                                                        className="text-indigo-600 hover:underline focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-sm"
                                                                                        aria-label={`Track shipment ${order.shipping.trackingNumber}`}
                                                                                    >
                                                                                        {order.shipping.trackingNumber}
                                                                                    </a>
                                                                                </dd>
                                                                            </div>
                                                                        )}
                                                                        {order.shipping.estimatedDelivery && (
                                                                            <div className="flex justify-between text-sm py-1">
                                                                                <dt className="text-gray-600">Est. Delivery:</dt>
                                                                                <dd className="flex items-center">
                                                                                    <CalendarIcon className="w-4 h-4 mr-1 text-gray-400" />
                                                                                    <time dateTime={order.shipping.estimatedDelivery}>{order.shipping.estimatedDelivery}</time>
                                                                                </dd>
                                                                            </div>
                                                                        )}
                                                                        {order.shipping.deliveredAt && (
                                                                            <div className="flex justify-between text-sm py-1">
                                                                                <dt className="text-gray-600">Delivered:</dt>
                                                                                <dd className="text-green-600 flex items-center">
                                                                                    <CheckCircleIcon className="w-4 h-4 mr-1" />
                                                                                    <time dateTime={order.shipping.deliveredAt}>{formatDate(order.shipping.deliveredAt)}</time>
                                                                                </dd>
                                                                            </div>
                                                                        )}
                                                                    </dl>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            )}
                                        </React.Fragment>
                                    ))}
                                </tbody>
                            </table>

                            {/* Empty state */}
                            {filteredOrders.length === 0 && (
                                <div className="text-center py-12 px-6 bg-white" role="status" aria-live="polite">
                                    <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400" />
                                    <p className="mt-2 text-base text-gray-500">No orders found matching your criteria.</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
