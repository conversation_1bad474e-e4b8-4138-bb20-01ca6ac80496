
import React from 'react'
import SearchBar from '../../components/Layout/SreachBar'
import Link from 'next/link'
import ProductShowcase from '../../components/Products/productsSuggestion'
import metaInfo from '@/src/seo/meta-info'
import config from '@/config'
import publicProductCard from '../../components/Cards/PublicProductCard'
import Navbar from '../../components/LandingPage/Navbar'

/* export metadata */
export const metadata = metaInfo.ProductsPage;

export const getProductsApi = async function () {
    // const response = await fetch(`${config.API_CONFIG.BASE_URL}/api/products`); // confidential api
    const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/products/public/all/view`);
    const data = await response.json();
    // console.log(data);

    return data;
};

// task!! delete it later
export default async function ProductsPage({ searchParams }) {
    const query = await searchParams;
    // console.log(query);
    // console.log(config);
    const apiData = await getProductsApi();
    // console.log(apiData);
    // console.log(apiData.products.data);

    /* async function getProducts(queryParam) {
        try {
            const res = await fetch(`https://www.themealdb.com/api/json/v1/1/search.php?s=${queryParam}`);
            const data = await res.json();
            // console.log(data);
            return data.meals || [];
        } catch (error) {
            console.log(error);
            return [];
        }
    }
    const products = await getProducts(query.search) || []; */
    // console.log(products);

    return (
        <div className='bg-gray-50 min-h-screen'>
            {/* Navigation */}
            <Navbar />

            <section className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6'>
                {/* Search Bar */}
                <div className='mb-6'>
                    <SearchBar />
                </div>

                <ul className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-5 p-5'>
                    {/* task!!! List sll products */}
                    {
                        apiData?.data && apiData?.data.map((product, index) => <publicProductCard key={index} product={product} />)
                    }
                </ul>

                {/* <ProductShowcase /> */}
            </section>
        </div>
    )
}
