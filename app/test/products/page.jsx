'use client';

import publicProductCard from '@/app/components/Cards/PublicProductCard';
import React, { useEffect, useState } from 'react';
import { Fa<PERSON>ilter, FaSearch, FaSort } from 'react-icons/fa';
import { MdCategory } from 'react-icons/md';
import Navbar from '@/app/components/LandingPage/Navbar';

export default function ProductsPage() {
    const [products, setProducts] = useState([]);
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(true);
    const [filters, setFilters] = useState({
        category: '',
        search: '',
        sort: 'newest'
    });

    useEffect(() => {
        fetchProducts();
        fetchCategories();
    }, [filters.category]);

    const fetchProducts = async () => {
        try {
            const url = filters.category
                ? `https://b2b.instinctfusionx.xyz/public/api/v1/categories/${filters.category}/products`
                : 'https://b2b.instinctfusionx.xyz/public/api/v1/products';
            const response = await fetch(url);
            const data = await response.json();
            setProducts(data.data.products.data || []);
        } catch (error) {
            console.error('Error fetching products:', error);
        } finally {
            setLoading(false);
        }
    };

    const fetchCategories = async () => {
        try {
            const response = await fetch('https://b2b.instinctfusionx.xyz/public/api/v1/categories');
            const data = await response.json();
            setCategories(data.data.categories.data || []);
        } catch (error) {
            console.error('Error fetching categories:', error);
        }
    };

    // console.log({ products }, "products", { categories }, "categories");


    return (
        <div className='min-h-screen bg-gray-50'>
            {/* Navigation */}
            <Navbar />

            <section className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 text-gray-600'>
                {/* Filters Section */}
                <div className="bg-white rounded-xl shadow-md p-4 sm:p-6 mb-8">
                    <div className="space-y-4">
                        <h2 className="text-xl sm:text-2xl font-bold text-gray-800 flex items-center gap-2">
                            <FaFilter className="text-orange-500" />
                            Product Filters
                        </h2>

                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                            {/* Search Input */}
                            <div className="relative">
                                <input
                                    type="text"
                                    placeholder="Search products..."
                                    value={filters.search}
                                    onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                />
                                <FaSearch className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
                            </div>

                            {/* Category Filter */}
                            <div className="relative">
                                <select
                                    value={filters.category}
                                    onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
                                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 appearance-none cursor-pointer"
                                >
                                    <option value="">All Categories</option>
                                    {categories.map(category => (
                                        <option key={category.id} value={category.id}>
                                            {category.name}
                                        </option>
                                    ))}
                                </select>
                                <MdCategory className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
                            </div>

                            {/* Sort Filter */}
                            <div className="relative sm:col-span-2 lg:col-span-1">
                                <select
                                    value={filters.sort}
                                    onChange={(e) => setFilters(prev => ({ ...prev, sort: e.target.value }))}
                                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 appearance-none cursor-pointer"
                                >
                                    <option value="newest">Newest First</option>
                                    <option value="oldest">Oldest First</option>
                                    <option value="price-asc">Price: Low to High</option>
                                    <option value="price-desc">Price: High to Low</option>
                                </select>
                                <FaSort className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
                            </div>
                        </div>
                    </div>
                </div>

                {/* Products Grid */}
                <div className="mt-8">
                    {loading ? (
                        // Loading Skeleton
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                            {[1, 2, 3, 4, 5, 6, 8].map((item) => (
                                <div key={item} className="bg-white rounded-xl shadow-md p-4 animate-pulse">
                                    <div className="w-full h-48 bg-gray-200 rounded-lg mb-4"></div>
                                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                                    <div className='h-6 bg-orange-300'></div>
                                </div>
                            ))}
                        </div>
                    ) : products.length === 0 ? (
                        // No Products Found
                        <div className="text-center py-12">
                            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <h3 className="mt-2 text-sm font-medium text-gray-900">No products found</h3>
                            <p className="mt-1 text-sm text-gray-500">Try adjusting your search or filter to find what you're looking for.</p>
                        </div>
                    ) : (
                        // Products Grid
                        <ul className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                            {products.map((product) => (
                                <publicProductCard key={product.id} product={product} />
                            ))}
                        </ul>
                    )}
                </div>
            </section>
        </div>
    )
}
