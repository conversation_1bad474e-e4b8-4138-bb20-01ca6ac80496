import React, { useState } from 'react'
import {
    useStripe,
    useElements,
    PaymentElement,
} from '@stripe/react-stripe-js';
import toast from 'react-hot-toast';

export default function CheckoutPage({ paymentInstance: serverPaymentInstance, billingAddress, shippingAddress }) {
    const stripe = useStripe();
    const elements = useElements();

    const [errorMessage, setErrorMessage] = useState(null);
    const [loading, setLoading] = useState(false);
    const [transactionId, setTransactionId] = useState(null);

    async function handleSubmit(event) {
        event.preventDefault();
        setLoading(true);

        console.log(serverPaymentInstance);


        if (!stripe || !elements) {
            toast.error("Stripe not loaded");
            return;
        }

        const { error: submitError } = elements.submit();
        console.log({ submitError });

        if (submitError) {
            setLoading(false);
            setErrorMessage(submitError.message);
            toast.error(submitError.message);
            return;
        }

        const { error: confirmError, paymentIntent } = await stripe.confirmPayment({
            elements,
            clientSecret: serverPaymentInstance?.data?.client_secret,
            confirmParams: {
                receipt_email: "<EMAIL>",
                shipping: shippingAddress,
                payment_method_data: {
                    billing_details: billingAddress
                },
                return_url: `${process.env.NEXT_PUBLIC_BASE_URL}/cart`
            },
            redirect: 'if_required'
        });
        console.log({ confirmError }, { paymentIntent });

        if (confirmError) {
            setLoading(false);
            setErrorMessage(confirmError.message);
            toast.error(confirmError.message);
            return;
        }

        if (paymentIntent.status === "succeeded" && paymentIntent.id) {
            setTransactionId(paymentIntent.id);

            toast.success(`Payment successful! Transaction ID: ${paymentIntent.id}`, {
                autoClose: 5000,
                hideProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                draggable: true,
                progress: undefined,
            });

            const cartId = localStorage.getItem("cartId");
            if (!cartId) {
                throw new Error("Cart ID not found");
            }

            const payment = {
                payment_intent: paymentIntent,
                payment_intent_id: paymentIntent.id,
                cart_id: cartId,
                shipping_address: shippingAddress,
                billing_address: billingAddress,
                payment_method: paymentIntent.payment_method_types,
                notes: "Please include tax invoice with the delivery"
            };

            const token = localStorage.getItem("userAuthToken");
            if (!token) {
                throw new Error("Authentication token not found");
            }

            const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/checkout/process`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${token}`,
                },
                body: JSON.stringify(payment)
            });
            const result = await response.json();
            console.log("After Payment Order Process result:", result);


            if (result.success === "success") {
                toast.success('Payment stored successfully!');
                Swal.fire({
                    title: "Payment Successful!",
                    text: "Your payment has been processed successfully.",
                    icon: "success",
                    confirmButtonText: "OK",
                    timer: 5000,
                    timerProgressBar: true,
                });
            } else {
                toast.error(result.message || 'Failed to store payment in database', {
                    hideProgressBar: false,
                    closeOnClick: true,
                    pauseOnHover: true,
                    draggable: true,
                    progress: undefined,
                });

                Swal.fire({
                    title: "Payment Failed!",
                    text: result.message || "Your payment has failed to stroe in Database.",
                    icon: "error",
                    confirmButtonText: "OK",
                    timerProgressBar: true,
                })
            }
        }

        toast.error("Something went wrong!");
        setLoading(false);
    }

    if (loading && !stripe && !elements) {
        return (
            <div className="flex items-center justify-center">
                <div
                    className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-e-transparent align-[-0.125em] text-surface motion-reduce:animate-[spin_1.5s_linear_infinite] dark:text-white"
                    role="status"
                >
                    <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
                        Loading...
                    </span>
                </div>
            </div>
        );
    }


    return (
        <section className='text-white max-w-md mx-auto'>
            {serverPaymentInstance?.data?.client_secret &&
                <form onSubmit={handleSubmit}>


                    <PaymentElement
                        onChange={(e) => {
                            if (e.error) {
                                setErrorMessage(e.error.message);
                            } else {
                                setErrorMessage("");
                            }
                        }}
                    />

                    <button
                        disabled={loading || !stripe || !elements}
                        id="submit"
                        className='bg-[#141414] text-white rounded-xl px-5 py-2 w-full mt-5'
                    >
                        <span id="button-text">
                            {loading ? <div className="spinner" id="spinner"></div> : "Pay now"}
                        </span>
                    </button>

                </form>
            }

            {errorMessage && (
                <div className="p-4 mb-4 rounded-lg bg-red-50 border border-red-200">
                    <div className="flex items-center gap-3">
                        <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <p className="font-medium text-red-700">{errorMessage}</p>
                    </div>
                </div>
            )}

            {transactionId && (
                <div className="p-4 mb-4 rounded-lg bg-green-50 border border-green-200">
                    <div className="flex items-center gap-3">
                        <svg className="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                        </svg>
                        <div>
                            <p className="font-medium text-green-700">Payment Successful!</p>
                            <p className="text-sm text-green-600">Transaction ID: <span className="font-mono">{transactionId}</span></p>
                        </div>
                    </div>
                </div>
            )}

            <pre>{JSON.stringify(serverPaymentInstance, null, 2)}</pre>
        </section>
    )
}
