"use client";

import React, { useEffect, useState } from 'react'

import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import CheckoutPage from './CheckoutPage';


export default function page() {
    const [paymentInstance, setPaymentInstance] = useState({});
    const [addresses, setAddresses] = useState([]);
    const [payment, setPayment] = useState({});
    const [clientSecret, setClientSecret] = useState('');


    async function fetchAddresses() {
        try {
            const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/customer/addresses`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('userAuthToken')}`
                }
            });
            const data = await response.json();
            console.log(data);

            if (data.success) {
                // Update your addresses state with the new data
                setAddresses(data.data.addresses); // You'll need to add this state if you haven't already

                postInitailOrder();
            }
        } catch (error) {
            console.error('Error fetching addresses:', error);
        }

        return;
    };

    async function postInitailOrder() {
        const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/checkout/initialize`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('userAuthToken')}`,
            },
            body: JSON.stringify({
                cart_id: localStorage.getItem('cartId'),
                shipping_address: addresses[0],
                billing_address: addresses[0],
                // payment_method: payment.method,
                notes: "Please include tax invoice with the delivery"
            })
        });

        const data = await response.json();
        console.log(data);

        setPaymentInstance(data);
        setClientSecret(data.data.client_secret);
        return data.data;
    }

    useEffect(() => {
        fetchAddresses();
    }, []);

    const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);

    return (
        <div>

            {
                paymentInstance?.data &&
                <Elements
                    stripe={stripePromise}
                    options={{
                        mode: "payment",
                        amount: parseInt(paymentInstance?.data?.amount),
                        currency: "usd",
                    }}>
                    <CheckoutPage paymentInstance={paymentInstance} />
                </Elements>
            }

        </div>
    )
}
