import axiosPublicInstance from "@/src/axios/axios.public";
import { NextResponse } from "next/server";

export async function GET() {
    try {
        const response = await axiosPublicInstance('/brands');
        // console.log(response.data);

        if (response.status !== 200 && !response.data) {
            return NextResponse.json({
                error: 'Failed to fetch brands',
                statusCode: 500
            });
        }

        const { brands, meta } = response.data;

        const brandsName = brands?.map((brand) => brand.name) || [];

        return NextResponse.json({
            brands, meta, status: "success", message: "Brands retrieved successfully", brandsName, statusCode: 200
        });
    } catch (error) {
        console.log(error);
        return NextResponse.json({
            error: 'Failed to fetch brands',
            statusCode: 500
        });

    }
}