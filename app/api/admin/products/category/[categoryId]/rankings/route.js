import { NextResponse } from "next/server";

export async function PATCH(request, { params }) {
    try {
        const { categoryId } = await params;
        const { rankings } = await request.json();

        // Get auth token from headers
        const authHeader = request.headers.get('authorization');
        const token = authHeader?.replace('Bearer ', '');
        
        if (!token) {
            return NextResponse.json({
                success: false,
                message: 'Authentication token required',
            }, { status: 401 });
        }

        // Validate input
        if (!categoryId || !rankings || !Array.isArray(rankings)) {
            return NextResponse.json({
                success: false,
                message: 'Category ID and rankings array are required',
            }, { status: 400 });
        }

        // Validate rankings structure
        for (const ranking of rankings) {
            if (!ranking.product_id || ranking.rank === undefined) {
                return NextResponse.json({
                    success: false,
                    message: 'Each ranking must have product_id and rank',
                }, { status: 400 });
            }
        }

        // Make API call to backend
        const response = await fetch(`${process.env.WEB_SERVER_URL}/admin/products/category/${categoryId}/rankings`, {
            method: 'PATCH',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ rankings }),
        });

        const data = await response.json();

        if (!response.ok) {
            return NextResponse.json({
                success: false,
                message: data.message || 'Failed to update product rankings',
            }, { status: response.status });
        }

        return NextResponse.json(data);
    } catch (error) {
        console.error('Error updating product rankings:', error);
        return NextResponse.json({
            success: false,
            message: 'Internal server error',
        }, { status: 500 });
    }
}
