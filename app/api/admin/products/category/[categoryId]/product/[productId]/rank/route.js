import { NextResponse } from "next/server";

export async function PATCH(request, { params }) {
    try {
        const { categoryId, productId } = await params;
        const { rank } = await request.json();

        // Get auth token from headers
        const authHeader = request.headers.get('authorization');
        const token = authHeader?.replace('Bearer ', '');
        
        if (!token) {
            return NextResponse.json({
                success: false,
                message: 'Authentication token required',
            }, { status: 401 });
        }

        // Validate input
        if (!categoryId || !productId || rank === undefined) {
            return NextResponse.json({
                success: false,
                message: 'Category ID, Product ID, and rank are required',
            }, { status: 400 });
        }

        // Make API call to backend
        const response = await fetch(`${process.env.WEB_SERVER_URL}/admin/products/category/${categoryId}/product/${productId}/rank`, {
            method: 'PATCH',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ rank }),
        });

        const data = await response.json();

        if (!response.ok) {
            return NextResponse.json({
                success: false,
                message: data.message || 'Failed to update product ranking',
            }, { status: response.status });
        }

        return NextResponse.json(data);
    } catch (error) {
        console.error('Error updating product ranking:', error);
        return NextResponse.json({
            success: false,
            message: 'Internal server error',
        }, { status: 500 });
    }
}
