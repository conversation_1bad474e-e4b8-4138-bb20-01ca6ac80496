import axiosPartnerInstance from "@/src/axios/axios.partner";
import axiosPublicInstance from "@/src/axios/axios.public";
import { NextResponse } from "next/server";

export async function GET() {
    try {
        const response = await axiosPublicInstance('/products');
        // console.log(response);
        if (response.status !== 200 && !response.data) {
            return NextResponse.json({
                error: 'Failed to fetch products', statusCode: 500
            });
        }

        const { data, meta, status, message } = response.data;

        return NextResponse.json(
            { products: data.products, meta, status, message },
            { statusCode: 200 }
        );
    } catch (error) {
        console.log(error);
        return NextResponse.json({
            error: 'Failed to fetch products', statusCode: 500
        });
    }
}

export async function POST(req) {
    try {
        const body = await req.json();
        console.log(body);

        const response = await axiosPartnerInstance("/products", body, {
            headers: { "Content-Type": "application/json" },
        });

        console.log(response.data);

        if (response.status !== 200 && !response.data.data) {
            return NextResponse.json({
                error: response?.data || "Something went wrong",
                statusCode: response?.status || 500,
            });
        }

        const { data, meta, status, message } = response.data;

        return NextResponse.json({
            data, meta, status, message, statusCode: 200
        });
    } catch (error) {
        console.log(error.response.data);
        return NextResponse.json({
            error: error?.response?.data || "Something went wrong",
            statusCode: error?.response?.status || 500,
        });
    }
}

