import { NextResponse } from "next/server";

export async function GET(request, { params }) {
    try {
        const { categoryId } = await params;
        
        if (!categoryId) {
            return NextResponse.json({
                success: false,
                message: 'Category ID is required',
            }, { status: 400 });
        }

        // Make API call to backend to get sorted products
        const response = await fetch(`${process.env.WEB_SERVER_URL}/products/category/${categoryId}/sorted`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });

        const data = await response.json();

        if (!response.ok) {
            return NextResponse.json({
                success: false,
                message: data.message || 'Failed to fetch sorted products',
            }, { status: response.status });
        }

        return NextResponse.json(data);
    } catch (error) {
        console.error('Error fetching sorted products:', error);
        return NextResponse.json({
            success: false,
            message: 'Internal server error',
        }, { status: 500 });
    }
}
