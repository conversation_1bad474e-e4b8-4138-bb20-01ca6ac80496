import axiosPublicInstance from "@/src/axios/axios.public";
import axiosSuperInstance from "@/src/axios/axios.super";
import { NextResponse } from "next/server";

export async function POST(request) {
    try {
        // Parse request body
        const { email, password } = await request.json();

        // Call authentication API
        const response = await axiosPublicInstance.post('auth/login', { email, password });

        if (!response.status === 200 || !response.data) {
            return NextResponse.json({
                response: response.data
            }, { status: 400 });
        }

        const { token, user, message } = response.data;

        return NextResponse.json({ token, user, message, statusCode: 200 });
    } catch (error) {
        // Handle API response errors
        if (error.response && error.response.data) {
            return NextResponse.json({
                message: error.response.data.message || error.response.data,
            }, { status: error.response.status || 500 });
        }

        // Handle request errors (network issues, etc.)
        if (error.request) {
            return NextResponse.json({
                message: 'No response received from authentication server',
            }, { status: 503 });
        }

        // Handle other unexpected errors
        return NextResponse.json({
            message: 'Authentication request failed',
        }, { status: 500 });
    }
}
