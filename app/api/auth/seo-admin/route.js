import { NextResponse } from "next/server";

export async function POST(request) {
    try {
        // Parse request body
        const { email, password } = await request.json();

        // Simple demo authentication for SEO admin
        // In production, this should connect to your actual authentication system
        if (email === '<EMAIL>' && password === 'seo123') {
            const mockToken = 'seo_demo_token_' + Date.now();
            const mockUser = {
                id: 'seo_1',
                email: email,
                role: 'seo',
                name: 'SEO Expert'
            };

            return NextResponse.json({ 
                token: mockToken, 
                user: mockUser, 
                message: 'SEO Admin login successful',
                statusCode: 200 
            });
        } else {
            return NextResponse.json({
                message: 'Invalid SEO admin credentials',
            }, { status: 401 });
        }
    } catch (error) {
        // Handle request errors (network issues, etc.)
        if (error.request) {
            return NextResponse.json({
                message: 'No response received from authentication server',
            }, { status: 503 });
        }

        // Handle other unexpected errors
        return NextResponse.json({
            message: 'Authentication request failed',
        }, { status: 500 });
    }
}
