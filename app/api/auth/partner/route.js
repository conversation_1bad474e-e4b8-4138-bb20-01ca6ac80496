import axiosSuperInstance from "@/src/axios/axios.super";
import { NextResponse } from "next/server";

export async function POST(request) {
    try {
        const { name, email, user_type, password, password_confirmation } = await request.json();

        // console.log(name, email, user_type, password, password_confirmation);

        const response = await axiosSuperInstance.post('/auth/register', { name, email, user_type, password, password_confirmation });

        // console.log(response.data);

        if (!response.status === 200 || !response.data || !response.data.user || !response.data.token) {
            return NextResponse.json({
                error: response.data,
                status: 500
            });
        }

        return NextResponse.json({
            message: "User created successfully",
            data: response.data,
            response,
            status: 200
        });

    } catch (error) {
        // console.log(error);

        return NextResponse.json({
            error: error.response?.data,
            status: 500
        });
    }

}
