import { NextResponse } from "next/server";

export async function GET(request, { params }) {
    try {
        const { categoryId } = await params;
        
        if (!categoryId) {
            return NextResponse.json({
                success: false,
                message: 'Category ID is required',
            }, { status: 400 });
        }

        // Make API call to backend to get products in category
        const response = await fetch(`${process.env.WEB_SERVER_URL}/categories/${categoryId}/products`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });

        const data = await response.json();

        if (!response.ok) {
            return NextResponse.json({
                success: false,
                message: data.message || 'Failed to fetch products',
            }, { status: response.status });
        }

        return NextResponse.json(data);
    } catch (error) {
        console.error('Error fetching category products:', error);
        return NextResponse.json({
            success: false,
            message: 'Internal server error',
        }, { status: 500 });
    }
}
