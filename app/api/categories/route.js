import axiosPublicInstance from "@/src/axios/axios.public";
import { NextResponse } from "next/server";

export async function GET() {
    try {
        const response = await axiosPublicInstance('/categories');
        // console.log('Categories API response:', JSON.stringify(response.data, null, 2));

        if (response.status !== 200 && !response.data) {
            return NextResponse.json({
                error: 'Failed to fetch categories', statusCode: 500
            });
        }

        const { data, meta, status, message } = response.data;

        const categoriesName = data?.categories?.data?.map((category) => category.name) ||
                              data?.categories?.map((category) => category.name) || [];

        return NextResponse.json({
            data, meta, status, message, categoriesName, statusCode: 200
        });
    } catch (error) {
        console.log('Categories API error:', error);
        return NextResponse.json({
            error: 'Failed to fetch categories',
            statusCode: 500
        });
    }
}