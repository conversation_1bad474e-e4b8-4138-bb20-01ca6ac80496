import axiosPublicInstance from "@/src/axios/axios.public";
import { NextResponse } from "next/server";

export async function POST(request) {
    try {
        const body = await request.json();
        const { email } = body;

        // Basic validation
        if (!email || !/^\S+@\S+\.\S+$/.test(email)) {
            return NextResponse.json({
                success: false,
                message: 'Please enter a valid email address',
                statusCode: 400
            }, { status: 400 });
        }

        // Forward the request to the backend
        const response = await axiosPublicInstance.post('/subscribe', {
            email: email
        });

        // Check if the backend request was successful
        if (response.status === 200 || response.status === 201) {
            return NextResponse.json({
                success: true,
                message: response.data.message || 'Thank you for subscribing!',
                statusCode: 200
            });
        } else {
            return NextResponse.json({
                success: false,
                message: response.data.message || 'Something went wrong. Please try again.',
                statusCode: response.status
            }, { status: response.status });
        }

    } catch (error) {
        console.error('Newsletter subscription error:', error);

        // Handle different types of errors
        if (error.response) {
            // Backend returned an error response
            return NextResponse.json({
                success: false,
                message: error.response.data.message || 'Something went wrong. Please try again.',
                statusCode: error.response.status
            }, { status: error.response.status });
        } else if (error.request) {
            // Network error
            return NextResponse.json({
                success: false,
                message: 'Network error. Please check your connection and try again.',
                statusCode: 500
            }, { status: 500 });
        } else {
            // Other error
            return NextResponse.json({
                success: false,
                message: 'An unexpected error occurred. Please try again.',
                statusCode: 500
            }, { status: 500 });
        }
    }
}
