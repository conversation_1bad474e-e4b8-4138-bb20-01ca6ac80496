'use client';

import { useState } from 'react';

export function useComingSoon() {
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [featureName, setFeatureName] = useState('This feature');

  const showComingSoon = (name = 'This feature') => {
    setFeatureName(name);
    setIsPopupOpen(true);
  };

  const hideComingSoon = () => {
    setIsPopupOpen(false);
  };

  return {
    isPopupOpen,
    featureName,
    showComingSoon,
    hideComingSoon
  };
}
