import Swal from "sweetalert2";

const contentUpload = {};

// return image paths in an array
contentUpload.MultipleImageUpload = async function (files, tokenKey) {
    // console.log("MultipleImageUpload", files);

    const imageFiles = files instanceof FileList ?
        Array.from(files) :
        files?.files ?
            Array.from(files.files) :
            [];

    if (imageFiles.length > 0) {
        // Create FormData
        const formData = new FormData();

        // Append each image file to FormData with the same field name
        imageFiles.forEach(file => {
            formData.append('images[]', file);
            // formData.append('max_width', ''); // Optional
            // formData.append('max_height', ''); // Optional
            // formData.append('format', 'webp'); // Optional
            // formData.append('name', file.name); // Optional
            // formData.append('max_size', file.size); // Optional
            // formData.append('type', file.type); // Optional
            // formData.append('lastModified', file.lastModified); // Optional
        });

        try {
            const token = localStorage.getItem(String(tokenKey));
            const imageResponse = await fetch('https://b2b.instinctfusionx.xyz/public/api/v1/images/upload', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                },
                body: formData
            });

            const imageData = await imageResponse.json();
            // console.log(imageData);
            // console.log(`https://b2b.instinctfusionx.xyz/storage/app/public/uploads/images/${imageData.data.images[0].filename}`);

            if (imageData.success) {
                // images uploaded successfully []
                return imageData.data.images.map(image => `https://b2b.instinctfusionx.xyz/storage/app/public/uploads/images/${image.filename}`) || [];
            } else {
                Swal.fire({
                    icon: 'error',
                    title: imageData.message || 'Upload failed',
                    text: imageData.details || 'Failed to upload images'
                });
                return [];
            }
        } catch (error) {
            Swal.fire({
                icon: 'error',
                title: 'Upload error',
                text: 'An error occurred while uploading the images'
            });
            return [];
        }
    }
};

export default contentUpload;
