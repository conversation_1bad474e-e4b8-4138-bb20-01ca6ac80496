
// const calculations = {};

// calculations.singleProduct = async function (product, quantity, userType) {
//     const gst = 0.1;

//     let amount = 0;
//     let totalAmount = 0;
//     let amountMargin = 0;
//     let amountMarginGst = 0;
//     let amountMarginGstDelivery = 0;
//     let amountTotalSaving = 0;
//     let amountPerPackSaving = 0;
//     let finalAmount = 0;
//     let calculatedCustomerMargin = 0;
//     let calculatedPartnerMargin = 0;
//     let calculatedFinalPrice = 0;

//     const sanitizedProdcut = {};
//     const sanitizedQuantity = parseInt(quantity);
//     // const sanitizedCustomerMargin = parseFloat(customerMargin).toFixed(2);
//     // const sanitizedPartnerMargin = parseFloat(partnerMargin).toFixed(2);
//     const error = {};

//     // sanitize product info
//     const sanitizedProduct = {
//         ...product,
//         pack_price: {
//             number_of_products: parseInt(product.pack_price.number_of_products),
//             customer_margin: parseFloat(product.pack_price.customer_margin).toFixed(2),
//             partner_margin: parseFloat(product.pack_price.partner_margin).toFixed(2),
//             per_pack_price: parseFloat(product.pack_price.per_pack_price).toFixed(2),
//             per_pack_special_price: parseFloat(product.pack_price.per_pack_special_price).toFixed(2),
//             delivery_fee: typeof product.pack_price.delivery_fee === 'number' ?
//                 parseFloat(product.pack_price.delivery_fee).toFixed(2) :
//                 0,
//         },

//         min_order_quantity: parseInt(product.min_order_quantity),
//         min_order_value: parseFloat(product.min_order_value).toFixed(2),
//         free_shipping_threshold: parseFloat(product.free_shipping_threshold).toFixed(2),
//         products_per_pallet: parseInt(product.products_per_pallet),
//         pallet_delivery_fee: parseFloat(product.pallet_delivery_fee).toFixed(2),
//         default_customer_margin: parseFloat(product.default_customer_margin).toFixed(2),
//         default_partner_margin: parseFloat(product.default_partner_margin).toFixed(2),
//         bulk_prices: product.bulk_prices.map(bulkPrice => ({
//             number_of_packs: parseInt(bulkPrice.number_of_packs),
//             per_pack_price: parseFloat(bulkPrice.per_pack_price).toFixed(2),
//             per_pack_special_price: parseFloat(bulkPrice.per_pack_special_price).toFixed(2),
//             customer_margin: parseFloat(bulkPrice.customer_margin).toFixed(2),
//             partner_margin: parseFloat(bulkPrice.partner_margin).toFixed(2),
//             delivery_fee: typeof bulkPrice.delivery_fee === 'number' ?
//                 parseFloat(bulkPrice.delivery_fee).toFixed(2) :
//                 0,
//         })) || [],

//     }

//     // validate sanitized product 
//     if (!sanitizedProduct) {
//         error.product = ['Product is required'];
//     }

//     // validate quantity not less than 1, not null, not undefined, not decimal
//     if (!sanitizedQuantity || sanitizedQuantity <= 0 || sanitizedQuantity % 1 !== 0 || sanitizedQuantity === null || sanitizedQuantity === undefined) {
//         error.quantity = ['Quantity is required'];
//     }

//     // frist check if quantity is greater than min_order_quantity
//     if (sanitizedProduct.min_order_quantity && sanitizedQuantity < sanitizedProduct.min_order_quantity) {
//         error.quantity = error.quantity.push(`Quantity must be greater than ${sanitizedProduct.min_order_quantity}`);
//     }

//     // check if quantity is less than bulk_prices[0].number_of_packs then calculate pack price
//     if (sanitizedProduct.bulk_prices.length <= 0 && sanitizedProduct.bulk_prices[0].number_of_packs < sanitizedQuantity) {
//         // check if packs special price
//         if (sanitizedProduct.pack_price.per_pack_special_price > 0) {
//             amount = sanitizedProduct.pack_price.per_pack_special_price * sanitizedQuantity;

//             calculatedFinalPrice = sanitizedProduct.pack_price.per_pack_special_price;
//         } else {
//             // calculate regular price
//             amount = sanitizedProduct.pack_price.per_pack_price * sanitizedQuantity;
//             calculatedFinalPrice = sanitizedProduct.pack_price.per_pack_price;
//         }
//         if (userType === 'customer') {
//             amountMargin = amount * (sanitizedProduct.pack_price.customer_margin / 100);
//         } else {
//             amountMargin = amount * (sanitizedProduct.pack_price.partner_margin / 100);
//         }

//         totalAmount = amount + amountMargin;
//         amountMarginGst = totalAmount + totalAmount * gst;
//         amountMarginGstDelivery = amountMarginGst + sanitizedProduct.pack_price.delivery_fee;
//         finalAmount = amountMarginGstDelivery;
//     }

//     // check if quantity is greater than bulk_prices[0].number_of_packs then calculate bulk price, if so then select that bulk object
//     const selectedBulkOption = sanitizedProduct.bulk_prices.find(bulkPrice => {
//         return bulkPrice.number_of_packs >= sanitizedQuantity || {};
//     });
//     if (selectedBulkOption && sanitizedQuantity >= selectedBulkOption.number_of_packs) {
//         if (selectedBulkOption.per_pack_special_price > 0) {
//             amount = selectedBulkOption.per_pack_special_price * sanitizedQuantity;

//             calculatedFinalPrice = selectedBulkOption.per_pack_special_price;
//         } else {
//             amount = selectedBulkOption.per_pack_price * sanitizedQuantity;

//             calculatedFinalPrice = selectedBulkOption.per_pack_price;
//         }
//         if (userType === 'customer') {
//             amountMargin = amount * (selectedBulkOption.customer_margin / 100);
//         } else {
//             amountMargin = amount * (selectedBulkOption.partner_margin / 100);
//         }
//         totalAmount = amount + amountMargin;
//         amountMarginGst = totalAmount + totalAmount * gst;
//         amountMarginGstDelivery = amountMarginGst + selectedBulkOption.delivery_fee;
//         finalAmount = amountMarginGstDelivery;
//     }

//     return {
//         product: product,
//         quantity: quantity,
//         // calculatedCustomerMargin: calculatedCustomerMargin.toFixed(2),
//         // calculatedPartnerMargin: calculatedPartnerMargin.toFixed(2),
//         calculatedFinalPrice: calculatedFinalPrice,
//         amount: amount.toFixed(2),
//         totalAmount: totalAmount.toFixed(2),
//         amountMargin: amountMargin.toFixed(2),
//         amountMarginGst: amountMarginGst.toFixed(2),
//         amountMarginGstDelivery: amountMarginGstDelivery.toFixed(2),
//         amountPerPackSaving: amountPerPackSaving.toFixed(2),
//         amoutnTotalSaving: amountTotalSaving.toFixed(2),
//         finalAmount: finalAmount.toFixed(2),
//         error: error,
//     };
// };

// export default calculations;



const calculations = {};

calculations.singleProduct = async function (product, quantity, userType) {
    const gst = 0.1;
    const calculatedBaseOnObject = {};
    const error = {};
    const sanitizedQuantity = parseInt(quantity);

    let sanitizedProduct = {};
    let amount = 0;
    let margin = 0;
    let amountWithMargin = 0;
    let amountWithMarginGst = 0;
    let amountWithMarginGstDelivery = 0;
    let finalPrice = 0;
    let calculatedBaseOnPrice = 0; // which object is used to calculate the price (pack_price or bulk_prices)
    let savingAllPacks = 0;
    let savingPerPack = 0;
    let totalSavingPerPack = 0;
    let totalSavingAllPacks = 0;


    // sanitize product info
    sanitizedProduct = {
        ...product,
        pack_price: {
            number_of_products: parseInt(product.pack_price.number_of_products),
            per_pack_price: parseFloat(product.pack_price.per_pack_price).toFixed(2),
            per_pack_special_price: parseFloat(product.pack_price.per_pack_special_price).toFixed(2),
            customer_margin: parseFloat(product.pack_price.customer_margin).toFixed(2),
            partner_margin: parseFloat(product.pack_price.partner_margin).toFixed(2),
            delivery_fee: typeof product.pack_price.delivery_fee === 'number' ?
                parseFloat(product.pack_price.delivery_fee).toFixed(2) :
                0,
        },

        min_order_quantity: parseInt(product.min_order_quantity),
        min_order_value: parseFloat(product.min_order_value).toFixed(2),
        free_shipping_threshold: parseFloat(product.free_shipping_threshold).toFixed(2),
        products_per_pallet: parseInt(product.products_per_pallet),
        pallet_delivery_fee: parseFloat(product.pallet_delivery_fee).toFixed(2),
        default_customer_margin: parseFloat(product.default_customer_margin).toFixed(2),
        default_partner_margin: parseFloat(product.default_partner_margin).toFixed(2),
        bulk_prices: product.bulk_prices.map(bulkPrice => ({
            number_of_packs: parseInt(bulkPrice.number_of_packs),
            per_pack_price: parseFloat(bulkPrice.per_pack_price).toFixed(2),
            per_pack_special_price: parseFloat(bulkPrice.per_pack_special_price).toFixed(2),
            customer_margin: parseFloat(bulkPrice.customer_margin).toFixed(2),
            partner_margin: parseFloat(bulkPrice.partner_margin).toFixed(2),
            delivery_fee: typeof bulkPrice.delivery_fee === 'number' ?
                parseFloat(bulkPrice.delivery_fee).toFixed(2) :
                0,
        })) || [],

    };

    function marginCalculation(amount, selectedPriceObject) {
        let margin = 0;

        if (userType === 'partner') {
            margin = parseFloat(selectedPriceObject.partner_margin || parseFloat(sanitizedProduct.default_partner_margin));
        } else {
            margin = parseFloat(selectedPriceObject.customer_margin || parseFloat(sanitizedProduct.default_customer_margin));
        }

        return margin;
    }

    function calculateDeliveryFee(amount, selectedPriceObject) {
        let deliveryFee = 0;

        if (amount >= sanitizedProduct.free_shipping_threshold) {
            deliveryFee = 0;
        } else {
            if (sanitizedQuantity >= sanitizedProduct.products_per_pallet) {
                deliveryFee = sanitizedProduct.pallet_delivery_fee;
            } else {
                deliveryFee = selectedPriceObject.delivery_fee || 0;
            }
        }

        return deliveryFee;
    }

    // validate sanitized product 
    if (!sanitizedProduct) {
        error.product = ['Product is required'];
    }

    // validate quantity not less than 1, not null, not undefined, not decimal
    if (!sanitizedQuantity || sanitizedQuantity <= 0 || sanitizedQuantity % 1 !== 0 || sanitizedQuantity === null || sanitizedQuantity === undefined) {
        error.quantity = ['Quantity is required'];
    }

    // frist check if quantity is greater than min_order_quantity
    if (sanitizedProduct.min_order_quantity && sanitizedQuantity < sanitizedProduct.min_order_quantity) {
        error.quantity = error.quantity.push(`Quantity must be greater than ${sanitizedProduct.min_order_quantity}`);
    }

    // check if quantity is less than bulk_prices[0].number_of_packs then calculate pack price
    if (sanitizedQuantity >= sanitizedProduct.min_order_quantity && sanitizedQuantity < sanitizedProduct.bulk_prices[0].number_of_packs) {
        calculatedBaseOnObject.pack = sanitizedProduct.pack_price;


        if (sanitizedProduct.pack_price.per_pack_special_price > 0) {
            calculatedBaseOnPrice = sanitizedProduct.pack_price.per_pack_special_price;

            // Total amount base on quantity
            amount = sanitizedProduct.pack_price.per_pack_special_price * sanitizedQuantity;

            // Per pack saving
            savingPerPack = sanitizedProduct.pack_price.per_pack_price - sanitizedProduct.pack_price.per_pack_special_price;

            // Base on All packs saving
            savingAllPacks = savingPerPack * sanitizedQuantity;
        } else {
            calculatedBaseOnPrice = sanitizedProduct.pack_price.per_pack_price;

            amount = sanitizedProduct.pack_price.per_pack_price * sanitizedQuantity;

            savingPerPack = 0;
            savingAllPacks = 0;
        }

        // delete later
        // if (userType === 'customer') {
        //     margin = sanitizedProduct.pack_price.customer_margin;
        // } else {
        //     margin = sanitizedProduct.pack_price.partner_margin;
        // }
        margin = marginCalculation(amount, sanitizedProduct.pack_price);

        amountWithMargin = amount + (amount * margin / 100);
        amountWithMarginGst = amountWithMargin + (amountWithMargin * gst);
        amountWithMarginGstDelivery = amountWithMarginGst + sanitizedProduct.pack_price.delivery_fee;
        finalPrice = amountWithMarginGstDelivery;

        totalSavingPerPack = savingPerPack + (savingPerPack * margin / 100);
        totalSavingAllPacks = savingAllPacks + (savingAllPacks * margin / 100);
    }

    // check if quantity is greater than bulk_prices[0].number_of_packs then calculate bulk price, if so then select that bulk object
    const bulkOptions = sanitizedProduct.bulk_prices.filter(bulkPrice => {
        return sanitizedQuantity >= bulkPrice.number_of_packs;
    });

    console.log("bulk options: ======================", bulkOptions, bulkOptions.length);

    const selectedBulkOption = bulkOptions[bulkOptions.length - 1];

    if (selectedBulkOption && sanitizedQuantity >= selectedBulkOption.number_of_packs) {
        calculatedBaseOnObject.bulk = selectedBulkOption;

        if (selectedBulkOption.per_pack_special_price > 0) {
            calculatedBaseOnPrice = selectedBulkOption.per_pack_special_price;

            // Total amount base on quantity
            amount = selectedBulkOption.per_pack_special_price * sanitizedQuantity;

            // Per pack saving
            savingPerPack = selectedBulkOption.per_pack_price - selectedBulkOption.per_pack_special_price;

            // Base on All packs saving
            savingAllPacks = savingPerPack * sanitizedQuantity;
        } else {
            calculatedBaseOnPrice = selectedBulkOption.per_pack_price;

            amount = selectedBulkOption.per_pack_price * sanitizedQuantity;

            savingPerPack = 0;
            savingAllPacks = 0;
        }

        // delete later
        // if (userType === 'customer') {
        //     margin = selectedBulkOption.customer_margin;
        // } else {
        //     margin = selectedBulkOption.partner_margin;
        // }
        margin = marginCalculation(amount, selectedBulkOption);

        amountWithMargin = amount + (amount * margin / 100);
        amountWithMarginGst = amountWithMargin + (amountWithMargin * gst);
        amountWithMarginGstDelivery = amountWithMarginGst + selectedBulkOption.delivery_fee;
        finalPrice = amountWithMarginGstDelivery;

        totalSavingPerPack = savingPerPack + (savingPerPack * margin / 100);
        totalSavingAllPacks = savingAllPacks + (savingAllPacks * margin / 100);
    }

    // if (error && Object.keys(error).length > 0) {
    //     return error;
    // }

    // check if amount is less than min_order_value
    if (amount === 0 || amount === null || amount === undefined || amount === 'NaN' || amount <= parseFloat(sanitizedProduct.min_order_value)) {
        error.amount = ['Amount is less than minimum order value'];
    }

    return {
        product: product,
        quantity: quantity,
        error: error,
        calculatedBaseOnObject: calculatedBaseOnObject,
        calculatedBaseOnPrice: calculatedBaseOnPrice,
        amount: amount.toFixed(2),
        margin: margin,
        savingPerPack: savingPerPack.toFixed(2),
        savingAllPacks: savingAllPacks.toFixed(2),
        totalSavingPerPack: totalSavingPerPack.toFixed(2),
        totalSavingAllPacks: totalSavingAllPacks.toFixed(2),
        finalPrice: finalPrice.toFixed(2),
        currencyCode: sanitizedProduct.currency_code || 'AUD',
    }
};

export default calculations;