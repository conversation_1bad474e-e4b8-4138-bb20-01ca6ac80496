import jsPDF from 'jspdf';

export const generateOrderPDF = (order) => {
    const pdf = new jsPDF();
    const pageWidth = pdf.internal.pageSize.width;
    const pageHeight = pdf.internal.pageSize.height;
    
    // Colors
    const primaryColor = [59, 130, 246]; // Blue
    const darkColor = [31, 41, 55]; // Dark gray
    const lightColor = [156, 163, 175]; // Light gray
    
    // Header
    pdf.setFillColor(...primaryColor);
    pdf.rect(0, 0, pageWidth, 40, 'F');
    
    // Company Logo/Name
    pdf.setTextColor(255, 255, 255);
    pdf.setFontSize(24);
    pdf.setFont('helvetica', 'bold');
    pdf.text('DIPTOUCH', 20, 25);
    
    // Invoice Title
    pdf.setFontSize(16);
    pdf.text('INVOICE', pageWidth - 60, 25);
    
    // Order Information Section
    pdf.setTextColor(...darkColor);
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Order Information', 20, 60);
    
    // Order details
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(10);
    
    const orderInfo = [
        [`Order Number:`, order.order_number],
        [`Invoice ID:`, order.invoice_id || 'N/A'],
        [`Order Date:`, new Date(order.created_at).toLocaleDateString()],
        [`Payment Status:`, order.payment_status.toUpperCase()],
        [`Shipping Status:`, order.shipping_status.toUpperCase()],
        [`Payment Method:`, order.payment_method.replace('_', ' ').toUpperCase()],
    ];
    
    let yPos = 70;
    orderInfo.forEach(([label, value]) => {
        pdf.setFont('helvetica', 'bold');
        pdf.text(label, 20, yPos);
        pdf.setFont('helvetica', 'normal');
        pdf.text(value, 80, yPos);
        yPos += 8;
    });
    
    // Billing Address
    pdf.setFont('helvetica', 'bold');
    pdf.text('Billing Address', 20, yPos + 10);
    pdf.setFont('helvetica', 'normal');
    const billingLines = order.billing_address.split('\n');
    yPos += 20;
    billingLines.forEach(line => {
        pdf.text(line, 20, yPos);
        yPos += 6;
    });
    
    // Shipping Address
    pdf.setFont('helvetica', 'bold');
    pdf.text('Shipping Address', 110, 130);
    pdf.setFont('helvetica', 'normal');
    const shippingLines = order.shipping_address.split('\n');
    let shippingYPos = 140;
    shippingLines.forEach(line => {
        pdf.text(line, 110, shippingYPos);
        shippingYPos += 6;
    });
    
    // Items Table Header
    const tableStartY = Math.max(yPos + 20, 180);
    pdf.setFillColor(248, 250, 252);
    pdf.rect(20, tableStartY, pageWidth - 40, 12, 'F');
    
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(9);
    pdf.text('Product', 25, tableStartY + 8);
    pdf.text('SKU', 80, tableStartY + 8);
    pdf.text('Qty', 120, tableStartY + 8);
    pdf.text('Unit Price', 140, tableStartY + 8);
    pdf.text('Total', 170, tableStartY + 8);
    
    // Items
    pdf.setFont('helvetica', 'normal');
    let itemY = tableStartY + 20;
    
    order.items.forEach((item, index) => {
        if (itemY > pageHeight - 60) {
            pdf.addPage();
            itemY = 40;
        }
        
        // Alternate row background
        if (index % 2 === 0) {
            pdf.setFillColor(249, 250, 251);
            pdf.rect(20, itemY - 6, pageWidth - 40, 12, 'F');
        }
        
        pdf.setTextColor(...darkColor);
        pdf.text(item.product_name.substring(0, 25) + (item.product_name.length > 25 ? '...' : ''), 25, itemY);
        pdf.text(item.product_sku, 80, itemY);
        pdf.text(item.quantity.toString(), 125, itemY);
        pdf.text(`${order.currency_code} ${parseFloat(item.unit_price).toFixed(2)}`, 140, itemY);
        pdf.text(`${order.currency_code} ${parseFloat(item.total_amount).toFixed(2)}`, 170, itemY);
        
        itemY += 15;
    });
    
    // Totals Section
    const totalsY = itemY + 20;
    const totalsX = pageWidth - 80;
    
    pdf.setFont('helvetica', 'normal');
    pdf.text('Subtotal:', totalsX - 40, totalsY);
    pdf.text(`${order.currency_code} ${parseFloat(order.subtotal).toFixed(2)}`, totalsX, totalsY);
    
    pdf.text('Shipping:', totalsX - 40, totalsY + 10);
    pdf.text(`${order.currency_code} ${parseFloat(order.shipping_amount).toFixed(2)}`, totalsX, totalsY + 10);
    
    pdf.text('Tax:', totalsX - 40, totalsY + 20);
    pdf.text(`${order.currency_code} ${parseFloat(order.tax_amount).toFixed(2)}`, totalsX, totalsY + 20);
    
    if (parseFloat(order.discount_amount) > 0) {
        pdf.text('Discount:', totalsX - 40, totalsY + 30);
        pdf.text(`-${order.currency_code} ${parseFloat(order.discount_amount).toFixed(2)}`, totalsX, totalsY + 30);
    }
    
    // Total line
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(12);
    pdf.line(totalsX - 50, totalsY + 35, totalsX + 30, totalsY + 35);
    pdf.text('TOTAL:', totalsX - 40, totalsY + 45);
    pdf.text(`${order.currency_code} ${parseFloat(order.total_amount).toFixed(2)}`, totalsX, totalsY + 45);
    
    // Footer
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(8);
    pdf.setTextColor(...lightColor);
    pdf.text('Thank you for your business!', 20, pageHeight - 20);
    pdf.text(`Generated on ${new Date().toLocaleDateString()}`, pageWidth - 80, pageHeight - 20);
    
    // Customer Notes
    if (order.customer_notes) {
        pdf.setTextColor(...darkColor);
        pdf.setFont('helvetica', 'bold');
        pdf.setFontSize(10);
        pdf.text('Customer Notes:', 20, totalsY + 60);
        pdf.setFont('helvetica', 'normal');
        pdf.text(order.customer_notes, 20, totalsY + 70);
    }
    
    return pdf;
};

export const downloadOrderPDF = (order) => {
    const pdf = generateOrderPDF(order);
    pdf.save(`invoice-${order.order_number}.pdf`);
};
