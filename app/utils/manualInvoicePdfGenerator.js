import jsPDF from 'jspdf';

export const generateManualInvoicePDF = (invoiceData) => {
    const pdf = new jsPDF();
    const pageWidth = pdf.internal.pageSize.width;
    const pageHeight = pdf.internal.pageSize.height;

    // Colors
    const orangeColor = [255, 140, 0]; // Diptouch orange
    const blackColor = [0, 0, 0];
    const grayColor = [128, 128, 128];

    let yPosition = 20;

    // Header - Diptouch Logo/Name with two-tone effect
    pdf.setFontSize(24);
    pdf.setFont('helvetica', 'bold');

    // Calculate the width of "D" to position "iptouch" precisely
    const dWidth = pdf.getTextWidth('D');

    // Orange "D"
    pdf.setTextColor(...orangeColor);
    pdf.text('D', 20, yPosition);

    // Black "iptouch" - positioned immediately after "D" to form one word "Diptouch"
    pdf.setTextColor(...blackColor);
    pdf.text('iptouch', 20 + dWidth, yPosition);

    yPosition += 15;

    // Tax Invoice Title
    pdf.setTextColor(...blackColor);
    pdf.setFontSize(16);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Tax Invoice', 20, yPosition);
    yPosition += 20;

    // Company Information Section
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');
    pdf.text(`${invoiceData.companyInfo.name}`, 20, yPosition);
    yPosition += 5;
    pdf.text(`ABN: ${invoiceData.companyInfo.abn}`, 20, yPosition);
    yPosition += 5;
    pdf.text(`Email: ${invoiceData.companyInfo.email}`, 20, yPosition);
    yPosition += 15;

    // Invoice To Section
    pdf.setTextColor(...blackColor);
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Invoice To:', 20, yPosition);
    yPosition += 5;

    pdf.setFont('helvetica', 'normal');
    pdf.text(invoiceData.customerInfo.name, 20, yPosition);
    yPosition += 5;

    if (invoiceData.customerInfo.abn) {
        pdf.text(`ABN: ${invoiceData.customerInfo.abn}`, 20, yPosition);
        yPosition += 5;
    }

    if (invoiceData.customerInfo.address) {
        pdf.text(invoiceData.customerInfo.address, 20, yPosition);
        yPosition += 5;
    }

    yPosition += 10;

    // Invoice Details (Right side)
    const rightColumnX = 120;
    let rightYPosition = 55;

    pdf.setFont('helvetica', 'bold');
    pdf.text('Invoice Date:', rightColumnX, rightYPosition);
    pdf.setFont('helvetica', 'normal');
    pdf.text(new Date(invoiceData.invoiceDetails.invoiceDate).toLocaleDateString(), rightColumnX + 35, rightYPosition);
    rightYPosition += 5;

    pdf.setFont('helvetica', 'bold');
    pdf.text('Due Date:', rightColumnX, rightYPosition);
    pdf.setFont('helvetica', 'normal');
    pdf.text(new Date(invoiceData.invoiceDetails.dueDate).toLocaleDateString(), rightColumnX + 35, rightYPosition);
    rightYPosition += 5;

    pdf.setFont('helvetica', 'bold');
    pdf.text('Invoice Number:', rightColumnX, rightYPosition);
    pdf.setFont('helvetica', 'normal');
    pdf.text(invoiceData.invoiceDetails.invoiceNumber, rightColumnX + 35, rightYPosition);
    rightYPosition += 5;

    // Invoice Status (prominent display)
    pdf.setFont('helvetica', 'bold');
    pdf.text('Status:', rightColumnX, rightYPosition);
    pdf.setTextColor(...orangeColor);
    pdf.text(invoiceData.invoiceDetails.status, rightColumnX + 35, rightYPosition);
    pdf.setTextColor(...blackColor);

    yPosition = Math.max(yPosition, rightYPosition + 15);

    // Products Purchased Section
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Products Purchased', 20, yPosition);
    yPosition += 10;

    // Table dimensions and positioning
    const tableStartX = 20;
    const tableWidth = pageWidth - 40; // Full width minus margins
    const tableStartY = yPosition;
    const rowHeight = 15; // Increased row height
    const headerHeight = 15; // Increased header height

    // Column widths (proportional to table width)
    const colWidths = {
        number: tableWidth * 0.08,      // 8% for #
        description: tableWidth * 0.45, // 45% for Product Description
        quantity: tableWidth * 0.15,    // 15% for Quantity
        price: tableWidth * 0.16,       // 16% for Final Price
        total: tableWidth * 0.16        // 16% for Line Total
    };

    // Column X positions
    const colPositions = {
        number: tableStartX,
        description: tableStartX + colWidths.number,
        quantity: tableStartX + colWidths.number + colWidths.description,
        price: tableStartX + colWidths.number + colWidths.description + colWidths.quantity,
        total: tableStartX + colWidths.number + colWidths.description + colWidths.quantity + colWidths.price
    };

    pdf.setDrawColor(0, 0, 0);
    pdf.setLineWidth(0.5);

    // Draw table header background
    pdf.setFillColor(240, 240, 240);
    pdf.rect(tableStartX, tableStartY, tableWidth, headerHeight, 'F');

    // Draw table header border
    pdf.rect(tableStartX, tableStartY, tableWidth, headerHeight);

    // Draw vertical lines for header
    pdf.line(colPositions.description, tableStartY, colPositions.description, tableStartY + headerHeight);
    pdf.line(colPositions.quantity, tableStartY, colPositions.quantity, tableStartY + headerHeight);
    pdf.line(colPositions.price, tableStartY, colPositions.price, tableStartY + headerHeight);
    pdf.line(colPositions.total, tableStartY, colPositions.total, tableStartY + headerHeight);

    // Column headers with proper alignment
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(9);
    pdf.setTextColor(...blackColor);

    // Center-aligned headers
    pdf.text('#', colPositions.number + (colWidths.number / 2), tableStartY + (headerHeight / 2) + 2, { align: 'center' });
    pdf.text('Product Description', colPositions.description + 5, tableStartY + (headerHeight / 2) + 2);
    pdf.text('Quantity', colPositions.quantity + (colWidths.quantity / 2), tableStartY + (headerHeight / 2) + 2, { align: 'center' });
    pdf.text('Final Price (AUD)', colPositions.price + (colWidths.price / 2), tableStartY + (headerHeight / 2) + 2, { align: 'center' });
    pdf.text('Line Total (AUD)', colPositions.total + (colWidths.total / 2), tableStartY + (headerHeight / 2) + 2, { align: 'center' });

    yPosition = tableStartY + headerHeight;

    // Table rows
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(9);

    invoiceData.products.forEach((product, index) => {
        // Draw row border
        pdf.rect(tableStartX, yPosition, tableWidth, rowHeight);

        // Draw vertical lines for each row
        pdf.line(colPositions.description, yPosition, colPositions.description, yPosition + rowHeight);
        pdf.line(colPositions.quantity, yPosition, colPositions.quantity, yPosition + rowHeight);
        pdf.line(colPositions.price, yPosition, colPositions.price, yPosition + rowHeight);
        pdf.line(colPositions.total, yPosition, colPositions.total, yPosition + rowHeight);

        // Row content with proper alignment
        const textY = yPosition + (rowHeight / 2) + 2;

        // Row number (centered)
        pdf.text((index + 1).toString(), colPositions.number + (colWidths.number / 2), textY, { align: 'center' });

        // Product description (left-aligned with padding)
        const maxDescLength = Math.floor(colWidths.description / 3); // Approximate character limit
        const description = product.description.length > maxDescLength ?
            product.description.substring(0, maxDescLength - 3) + '...' :
            product.description;
        pdf.text(description, colPositions.description + 3, textY);

        // Quantity (centered)
        pdf.text(product.quantity.toString(), colPositions.quantity + (colWidths.quantity / 2), textY, { align: 'center' });

        // Final Price (centered)
        pdf.text(`$${parseFloat(product.finalPrice).toFixed(2)}`, colPositions.price + (colWidths.price / 2), textY, { align: 'center' });

        // Line Total (centered)
        pdf.text(`$${product.lineTotal.toFixed(2)}`, colPositions.total + (colWidths.total / 2), textY, { align: 'center' });

        yPosition += rowHeight;
    });

    yPosition += 10;

    // Totals Section
    const totalsStartX = 120;

    // Total GST calculation
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(10);
    pdf.text(`Total GST (${invoiceData.gst}%): $${invoiceData.gstAmount.toFixed(2)}`, totalsStartX, yPosition);
    yPosition += 5;

    // Delivery charge
    pdf.text(`Delivery charge: $${invoiceData.deliveryCharge.toFixed(2)}`, totalsStartX, yPosition);
    yPosition += 10;

    // Total Amount - changes based on invoice status
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(11);

    let totalLabel = 'Total Amount Due';
    if (invoiceData.invoiceDetails.status === 'Paid') {
        totalLabel = 'Total Amount Paid';
    } else if (invoiceData.invoiceDetails.status === 'Unpaid') {
        totalLabel = 'Total Amount Unpaid';
    }

    pdf.text(`${totalLabel}: $${invoiceData.total.toFixed(2)}`, totalsStartX, yPosition);
    yPosition += 5;

    // Payment Information (if custom payment is specified)
    if (invoiceData.customPayment && invoiceData.amountPaid > 0) {
        pdf.setFont('helvetica', 'normal');
        pdf.setFontSize(10);
        pdf.text(`Amount Paid: $${invoiceData.amountPaid.toFixed(2)}`, totalsStartX, yPosition);
        yPosition += 5;

        pdf.setFont('helvetica', 'bold');
        pdf.setTextColor(...orangeColor);
        pdf.text(`Remaining Balance: $${invoiceData.remainingBalance.toFixed(2)}`, totalsStartX, yPosition);
        pdf.setTextColor(...blackColor);
        yPosition += 5;
    }

    yPosition += 15;

    // Payment Instructions Section
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(10);
    pdf.text('Payment Instructions', 20, yPosition);
    yPosition += 10;

    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(9);

    // Bank details
    pdf.text(`Bank Name: ${invoiceData.paymentInfo.bankName}`, 20, yPosition);
    yPosition += 5;
    pdf.text(`Account Name: ${invoiceData.paymentInfo.accountName}`, 20, yPosition);
    yPosition += 5;
    pdf.text(`BSB: ${invoiceData.paymentInfo.bsb}`, 20, yPosition);
    yPosition += 5;
    pdf.text(`Account Number: ${invoiceData.paymentInfo.accountNumber}`, 20, yPosition);
    yPosition += 5;
    pdf.text(`Reference: ${invoiceData.paymentInfo.reference}`, 20, yPosition);

    return pdf;
};

export const downloadManualInvoicePDF = (invoiceData) => {
    const pdf = generateManualInvoicePDF(invoiceData);
    const fileName = `Tax-Invoice-${invoiceData.invoiceDetails.invoiceNumber}-${invoiceData.customerInfo.name.replace(/\s+/g, '-')}.pdf`;
    pdf.save(fileName);
    return fileName;
};
