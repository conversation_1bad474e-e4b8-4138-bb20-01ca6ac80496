/**
 * This file contains configuration and utilities for animations
 * Used as a centralized place to manage animation settings
 */

// Basic animation variants that can be used across components
export const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0, 
    transition: { duration: 0.6 }
  }
};

export const fadeInLeft = {
  hidden: { opacity: 0, x: -20 },
  visible: { 
    opacity: 1, 
    x: 0, 
    transition: { duration: 0.5 }
  }
};

export const fadeInRight = {
  hidden: { opacity: 0, x: 20 },
  visible: { 
    opacity: 1, 
    x: 0, 
    transition: { duration: 0.5 }
  }
};

export const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

export const itemFadeIn = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5
    }
  }
};

// Function to check if we're running on client-side
export const isClient = () => typeof window !== 'undefined';

// Function to safely use motion features
export function safelyUseMotion(Component) {
  // This is a higher-order component that ensures motion components
  // are only rendered on the client side
  return function SafeMotionComponent(props) {
    const [isMounted, setIsMounted] = React.useState(false);
    
    React.useEffect(() => {
      setIsMounted(true);
    }, []);
    
    if (!isMounted) {
      // Return a placeholder or null during SSR
      return null;
    }
    
    return <Component {...props} />;
  };
}
