import React from 'react';
import { notFound } from 'next/navigation';
import CategoryProductsClient from './CategoryProductsClient';

// API function to fetch category info for SEO
async function getCategoryInfo(categoryId) {
    try {
        console.log('🔍 SEO: Fetching category info for ID:', categoryId);

        const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/categories/${categoryId}`, {
            cache: 'force-cache', // Cache for better performance
            next: { revalidate: 3600 } // Revalidate every hour
        });

        if (!response.ok) {
            throw new Error(`Failed to fetch category: ${response.status}`);
        }

        const data = await response.json();
        console.log('✅ SEO: Category info received:', data.data?.category?.name);

        if (data.success && data.data?.category) {
            return data.data.category;
        }

        throw new Error('Invalid category data structure');

    } catch (error) {
        console.error('❌ SEO: Error fetching category info:', error);

        // Fallback category data
        return {
            id: categoryId,
            name: `Category ${categoryId}`,
            slug: `category-${categoryId}`,
            description: "Products in this category",
            product_count: 0,
            is_active: true
        };
    }
}

// Generate metadata for SEO
export async function generateMetadata({ params }) {
    const categoryId = params.id;

    console.log('🎯 SEO: Generating metadata for category ID:', categoryId);

    // Fetch real category data from API
    const category = await getCategoryInfo(categoryId);

    // Generate SEO-optimized metadata
    const title = `${category.name} - B2B Products | InstinctFusion`;
    const description = `Browse ${category.description || 'quality products'} in our B2B marketplace. ${category.product_count || 0} products available with competitive pricing and bulk discounts.`;
    const keywords = [
        category.name,
        category.slug,
        'B2B',
        'wholesale',
        'bulk products',
        'business products',
        'InstinctFusion',
        `category ${categoryId}`
    ].filter(Boolean).join(', ');

    // Handle category image for SEO
    const categoryImage = category.image;
    const fallbackImage = 'https://b2b.instinctfusionx.xyz/images/default-category.jpg'; // Default category image
    const seoImage = categoryImage || fallbackImage;

    // Ensure image URL is absolute
    const absoluteImageUrl = seoImage.startsWith('http')
        ? seoImage
        : `https://b2b.instinctfusionx.xyz${seoImage.startsWith('/') ? '' : '/'}${seoImage}`;

    console.log('✅ SEO: Generated metadata for:', category.name);
    console.log('🖼️ SEO: Using image:', absoluteImageUrl);

    return {
        title,
        description,
        keywords,
        robots: {
            index: category.is_active !== false,
            follow: true,
        },
        openGraph: {
            title,
            description,
            type: 'website',
            url: `/products/categories/${categoryId}`,
            siteName: 'InstinctFusion B2B',
            locale: 'en_US',
            images: [
                {
                    url: absoluteImageUrl,
                    width: 1200,
                    height: 630,
                    alt: `${category.name} - B2B Products Category`,
                    type: 'image/jpeg',
                }
            ],
        },
        twitter: {
            card: 'summary_large_image',
            title,
            description,
            creator: '@InstinctFusion',
            site: '@InstinctFusion',
            images: [
                {
                    url: absoluteImageUrl,
                    alt: `${category.name} - B2B Products Category`,
                }
            ],
        },
        alternates: {
            canonical: `/products/categories/${categoryId}`,
        },
        other: {
            'og:image:secure_url': absoluteImageUrl,
            'og:image:type': 'image/jpeg',
            'og:image:width': '1200',
            'og:image:height': '630',
        },
    };
}

// API function for fetching products by category ID
export async function GetAllProductsByCategoryId(id, params = {}) {
    console.log('🔍 SSR: Fetching products for category ID:', id);
    console.log('🔍 SSR: Query parameters:', params);

    try {
        // Build query parameters
        const queryParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
            if (value && value !== '' && value !== 'all') {
                queryParams.set(key, value.toString());
            }
        });

        const url = `https://b2b.instinctfusionx.xyz/public/api/v1/products/category/${id}?${queryParams.toString()}`;
        console.log('🌐 SSR: API URL:', url);

        const response = await fetch(url, {
            cache: 'force-cache',
            next: { revalidate: 300 } // Revalidate every 5 minutes
        });

        if (!response.ok) {
            throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('✅ SSR: API response received:', data.success);
        console.log('📊 SSR: Products count:', data.data?.products?.data?.length || 0);

        return data;

    } catch (error) {
        console.error('❌ SSR: Error fetching products, using fallback data:', error);

        // Simulate API delay for fallback
        await new Promise(resolve => setTimeout(resolve, 500));

        // Mock response with category-specific data (simulating different categories)
        const categoryDataMap = {
            1: { name: "General Products", description: "Default category for general products" },
            2: { name: "Electronics", description: "Electronic devices and gadgets" },
            3: { name: "Office Supplies", description: "Office equipment and supplies" }
        };

        const categoryInfo = categoryDataMap[id] || { name: "Unknown Category", description: "Category not found" };

        console.log('🏷️ SSR: Category info for API response:', categoryInfo);

        return {
            "success": true,
            "data": {
                "products": {
                    "current_page": 1,
                    "data": [
                        {
                            "id": 99,
                            "name": "Medium Wireless Headphones 33",
                            "slug": "medium-wireless-headphones-33",
                            "sku": "WH-PREMIUM-011133",
                            "price": "129.99",
                            "special_price": "129.99",
                            "currency": "AUD",
                            "image": "https://example.com/images/headphones-main.jpg",
                            "brand": "Updated Brand 1747237351",
                            "rating": null,
                            "review_count": null,
                            "is_active": true,
                            "approval_status": "pending",
                            "pack_price": {
                                "number_of_products": 1,
                                "per_pack_price": "149.99",
                                "per_pack_special_price": "129.99",
                                "customer_margin": "15.00",
                                "partner_margin": "10.00",
                                "customer_margin_type": "percentage",
                                "partner_margin_type": "percentage",
                                "delivery_fee": null
                            },
                            "bulk_prices": [
                                {
                                    "number_of_packs": 5,
                                    "per_pack_price": "140.99",
                                    "per_pack_special_price": "119.99",
                                    "customer_margin": "12.00",
                                    "partner_margin": "8.00",
                                    "customer_margin_type": "percentage",
                                    "partner_margin_type": "percentage",
                                    "delivery_fee": null,
                                    "sort_order": 0
                                },
                                {
                                    "number_of_packs": 10,
                                    "per_pack_price": "130.99",
                                    "per_pack_special_price": "109.99",
                                    "customer_margin": "10.00",
                                    "partner_margin": "7.00",
                                    "customer_margin_type": "percentage",
                                    "partner_margin_type": "percentage",
                                    "delivery_fee": null,
                                    "sort_order": 1
                                },
                                {
                                    "number_of_packs": 20,
                                    "per_pack_price": "120.99",
                                    "per_pack_special_price": "99.99",
                                    "customer_margin": "8.00",
                                    "partner_margin": "6.00",
                                    "customer_margin_type": "percentage",
                                    "partner_margin_type": "percentage",
                                    "delivery_fee": null,
                                    "sort_order": 2
                                }
                            ]
                        },
                        {
                            "id": 79,
                            "name": "New Product Name",
                            "slug": "new-product-name",
                            "sku": "TEST-SKU-09555560gsss0",
                            "price": "90.00",
                            "special_price": "90.00",
                            "currency": "AUD",
                            "image": "http://example.com/image1.jpg",
                            "brand": "Updated Brand 1747237351",
                            "rating": null,
                            "review_count": null,
                            "is_active": true,
                            "approval_status": "pending",
                            "pack_price": {
                                "number_of_products": 1,
                                "per_pack_price": "100.00",
                                "per_pack_special_price": "90.00",
                                "customer_margin": "10.00",
                                "partner_margin": "5.00",
                                "customer_margin_type": "percentage",
                                "partner_margin_type": "percentage",
                                "delivery_fee": "10.00"
                            },
                            "bulk_prices": [
                                {
                                    "number_of_packs": 5,
                                    "per_pack_price": "95.00",
                                    "per_pack_special_price": "85.00",
                                    "customer_margin": "10.00",
                                    "partner_margin": "5.00",
                                    "customer_margin_type": "percentage",
                                    "partner_margin_type": "percentage",
                                    "delivery_fee": "8.00",
                                    "sort_order": 0
                                },
                                {
                                    "number_of_packs": 10,
                                    "per_pack_price": "90.00",
                                    "per_pack_special_price": "80.00",
                                    "customer_margin": "10.00",
                                    "partner_margin": "5.00",
                                    "customer_margin_type": "percentage",
                                    "partner_margin_type": "percentage",
                                    "delivery_fee": "5.00",
                                    "sort_order": 1
                                }
                            ]
                        },
                        {
                            "id": 55,
                            "name": "Admin Product67900gy5",
                            "slug": "admin-product67900gy5",
                            "sku": "TEST-SKU-09555560gy0",
                            "price": "90.00",
                            "special_price": "90.00",
                            "currency": "AUD",
                            "image": "https://example.com/image1.jpg",
                            "brand": "Updated Brand 1747237351",
                            "rating": null,
                            "review_count": null,
                            "is_active": true,
                            "approval_status": "pending",
                            "pack_price": {
                                "number_of_products": 1,
                                "per_pack_price": "100.00",
                                "per_pack_special_price": "90.00",
                                "customer_margin": "10.00",
                                "partner_margin": "5.00",
                                "customer_margin_type": "percentage",
                                "partner_margin_type": "percentage",
                                "delivery_fee": "10.00"
                            },
                            "bulk_prices": [
                                {
                                    "number_of_packs": 5,
                                    "per_pack_price": "95.00",
                                    "per_pack_special_price": "85.00",
                                    "customer_margin": "10.00",
                                    "partner_margin": "5.00",
                                    "customer_margin_type": "percentage",
                                    "partner_margin_type": "percentage",
                                    "delivery_fee": "8.00",
                                    "sort_order": 0
                                },
                                {
                                    "number_of_packs": 10,
                                    "per_pack_price": "90.00",
                                    "per_pack_special_price": "80.00",
                                    "customer_margin": "10.00",
                                    "partner_margin": "5.00",
                                    "customer_margin_type": "percentage",
                                    "partner_margin_type": "percentage",
                                    "delivery_fee": "5.00",
                                    "sort_order": 1
                                }
                            ]
                        },
                        {
                            "id": 54,
                            "name": "Medium Wireless Headphones1",
                            "slug": "medium-wireless-headphones1",
                            "sku": "WH-PREMIUM-0111",
                            "price": "129.99",
                            "special_price": "129.99",
                            "currency": "AUD",
                            "image": "https://example.com/images/headphones-main.jpg",
                            "brand": "Updated Brand 1747237351",
                            "rating": null,
                            "review_count": null,
                            "is_active": true,
                            "approval_status": "pending",
                            "pack_price": {
                                "number_of_products": 1,
                                "per_pack_price": "149.99",
                                "per_pack_special_price": "129.99",
                                "customer_margin": "15.00",
                                "partner_margin": "10.00",
                                "customer_margin_type": "percentage",
                                "partner_margin_type": "percentage",
                                "delivery_fee": null
                            },
                            "bulk_prices": [
                                {
                                    "number_of_packs": 5,
                                    "per_pack_price": "140.99",
                                    "per_pack_special_price": "119.99",
                                    "customer_margin": "12.00",
                                    "partner_margin": "8.00",
                                    "customer_margin_type": "percentage",
                                    "partner_margin_type": "percentage",
                                    "delivery_fee": null,
                                    "sort_order": 0
                                },
                                {
                                    "number_of_packs": 10,
                                    "per_pack_price": "130.99",
                                    "per_pack_special_price": "109.99",
                                    "customer_margin": "10.00",
                                    "partner_margin": "7.00",
                                    "customer_margin_type": "percentage",
                                    "partner_margin_type": "percentage",
                                    "delivery_fee": null,
                                    "sort_order": 1
                                },
                                {
                                    "number_of_packs": 20,
                                    "per_pack_price": "120.99",
                                    "per_pack_special_price": "99.99",
                                    "customer_margin": "8.00",
                                    "partner_margin": "6.00",
                                    "customer_margin_type": "percentage",
                                    "partner_margin_type": "percentage",
                                    "delivery_fee": null,
                                    "sort_order": 2
                                }
                            ]
                        },
                        {
                            "id": 46,
                            "name": "Premium Business Laptop testing 32",
                            "slug": "premium-business-laptop-testing-32",
                            "sku": "PBL-12345-testing-32",
                            "price": "120.99",
                            "special_price": null,
                            "currency": "AUD",
                            "image": "https://b2b.instinctfusionx.xyz/storage/app/public/uploads/images/39de39bf-d2fe-4919-b8b6-cb2100e8d128.jpeg",
                            "brand": "Updated Brand 1747237351",
                            "rating": null,
                            "review_count": null,
                            "is_active": true,
                            "approval_status": "pending",
                            "pack_price": {
                                "number_of_products": 1,
                                "per_pack_price": "120.99",
                                "per_pack_special_price": null,
                                "customer_margin": "15.00",
                                "partner_margin": "10.00",
                                "customer_margin_type": "percentage",
                                "partner_margin_type": "percentage",
                                "delivery_fee": null
                            },
                            "bulk_prices": []
                        },
                        {
                            "id": 45,
                            "name": "Premium Business Laptop testing 31",
                            "slug": "premium-business-laptop-testing-31",
                            "sku": "PBL-12345-testing-31",
                            "price": "120.99",
                            "special_price": null,
                            "currency": "AUD",
                            "image": "https://b2b.instinctfusionx.xyz/storage/app/public/uploads/images/7dea6b2e-71fc-434d-a83d-ca9bf846bbe9.jpeg",
                            "brand": "Updated Brand 1747237351",
                            "rating": null,
                            "review_count": null,
                            "is_active": true,
                            "approval_status": "pending",
                            "pack_price": {
                                "number_of_products": 1,
                                "per_pack_price": "120.99",
                                "per_pack_special_price": null,
                                "customer_margin": "15.00",
                                "partner_margin": "10.00",
                                "customer_margin_type": "percentage",
                                "partner_margin_type": "percentage",
                                "delivery_fee": null
                            },
                            "bulk_prices": []
                        },
                        {
                            "id": 44,
                            "name": "Premium Business Laptop testing 30",
                            "slug": "premium-business-laptop-testing-30",
                            "sku": "PBL-12345-testing-30",
                            "price": "120.99",
                            "special_price": null,
                            "currency": "AUD",
                            "image": "https://b2b.instinctfusionx.xyz/storage/app/public/uploads/images/40424808-6629-4dbf-9567-d65a7058983d.jpeg",
                            "brand": "Updated Brand 1747237351",
                            "rating": null,
                            "review_count": null,
                            "is_active": true,
                            "approval_status": "pending",
                            "pack_price": {
                                "number_of_products": 1,
                                "per_pack_price": "120.99",
                                "per_pack_special_price": null,
                                "customer_margin": "15.00",
                                "partner_margin": "10.00",
                                "customer_margin_type": "percentage",
                                "partner_margin_type": "percentage",
                                "delivery_fee": null
                            },
                            "bulk_prices": []
                        },
                        {
                            "id": 43,
                            "name": "Premium Business Laptop testing 29",
                            "slug": "premium-business-laptop-testing-29",
                            "sku": "PBL-12345-testing-29",
                            "price": "120.99",
                            "special_price": null,
                            "currency": "AUD",
                            "image": "https://b2b.instinctfusionx.xyz/storage/app/public/uploads/images/167462c8-d89e-47ce-90e9-f0c8fb9a75a1.jpeg",
                            "brand": "Updated Brand 1747237351",
                            "rating": null,
                            "review_count": null,
                            "is_active": true,
                            "approval_status": "pending",
                            "pack_price": {
                                "number_of_products": 1,
                                "per_pack_price": "120.99",
                                "per_pack_special_price": null,
                                "customer_margin": "15.00",
                                "partner_margin": "10.00",
                                "customer_margin_type": "percentage",
                                "partner_margin_type": "percentage",
                                "delivery_fee": null
                            },
                            "bulk_prices": []
                        },
                        {
                            "id": 42,
                            "name": "Premium Business Laptop testing 28",
                            "slug": "premium-business-laptop-testing-28",
                            "sku": "PBL-12345-testing-28",
                            "price": "120.99",
                            "special_price": null,
                            "currency": "AUD",
                            "image": "https://b2b.instinctfusionx.xyz/storage/app/public/uploads/images/51a03c7e-94f0-44fc-a0e5-ef42d12aadb7.jpeg",
                            "brand": "Updated Brand 1747237351",
                            "rating": null,
                            "review_count": null,
                            "is_active": true,
                            "approval_status": "pending",
                            "pack_price": {
                                "number_of_products": 1,
                                "per_pack_price": "120.99",
                                "per_pack_special_price": null,
                                "customer_margin": "15.00",
                                "partner_margin": "10.00",
                                "customer_margin_type": "percentage",
                                "partner_margin_type": "percentage",
                                "delivery_fee": null
                            },
                            "bulk_prices": []
                        },
                        {
                            "id": 41,
                            "name": "Premium Business Laptop testing 27",
                            "slug": "premium-business-laptop-testing-27",
                            "sku": "PBL-12345-testing-27",
                            "price": "120.99",
                            "special_price": null,
                            "currency": "AUD",
                            "image": "https://b2b.instinctfusionx.xyz/storage/app/public/uploads/images/15f4d7e9-e51c-49f8-a082-c3fd3fce3de9.jpeg",
                            "brand": "Updated Brand 1747237351",
                            "rating": null,
                            "review_count": null,
                            "is_active": true,
                            "approval_status": "pending",
                            "pack_price": {
                                "number_of_products": 1,
                                "per_pack_price": "120.99",
                                "per_pack_special_price": null,
                                "customer_margin": "15.00",
                                "partner_margin": "10.00",
                                "customer_margin_type": "percentage",
                                "partner_margin_type": "percentage",
                                "delivery_fee": null
                            },
                            "bulk_prices": []
                        },
                        {
                            "id": 40,
                            "name": "Premium Business Laptop testing 26",
                            "slug": "premium-business-laptop-testing-26",
                            "sku": "PBL-12345-testing-26",
                            "price": "120.99",
                            "special_price": null,
                            "currency": "AUD",
                            "image": "https://b2b.instinctfusionx.xyz/storage/app/public/uploads/images/378ab50b-4580-4fab-b0b2-70195f4f97fa.jpeg",
                            "brand": "Updated Brand 1747237351",
                            "rating": null,
                            "review_count": null,
                            "is_active": true,
                            "approval_status": "pending",
                            "pack_price": {
                                "number_of_products": 1,
                                "per_pack_price": "120.99",
                                "per_pack_special_price": null,
                                "customer_margin": "15.00",
                                "partner_margin": "10.00",
                                "customer_margin_type": "percentage",
                                "partner_margin_type": "percentage",
                                "delivery_fee": null
                            },
                            "bulk_prices": []
                        },
                        {
                            "id": 39,
                            "name": "testin vend 1.",
                            "slug": "testin-vend-1-2",
                            "sku": "GADGET-001",
                            "price": "1.00",
                            "special_price": "1.00",
                            "currency": "AUD",
                            "image": "https://b2b.instinctfusionx.xyz/storage/uploads/images/714e4337-f961-4073-979e-0f055901aa56.jpeg",
                            "brand": "Updated Brand 1747237351",
                            "rating": null,
                            "review_count": null,
                            "is_active": true,
                            "approval_status": "approved",
                            "pack_price": {
                                "number_of_products": 1,
                                "per_pack_price": "1.00",
                                "per_pack_special_price": "1.00",
                                "customer_margin": "15.00",
                                "partner_margin": "10.00",
                                "customer_margin_type": "percentage",
                                "partner_margin_type": "percentage",
                                "delivery_fee": null
                            },
                            "bulk_prices": []
                        }
                    ],
                    "first_page_url": "https://b2b.instinctfusionx.xyz/public/api/v1/products/category/1?page=1",
                    "from": 1,
                    "last_page": 4,
                    "last_page_url": "https://b2b.instinctfusionx.xyz/public/api/v1/products/category/1?page=4",
                    "links": [
                        {
                            "url": null,
                            "label": "&laquo; Previous",
                            "active": false
                        },
                        {
                            "url": "https://b2b.instinctfusionx.xyz/public/api/v1/products/category/1?page=1",
                            "label": "1",
                            "active": true
                        },
                        {
                            "url": "https://b2b.instinctfusionx.xyz/public/api/v1/products/category/1?page=2",
                            "label": "2",
                            "active": false
                        },
                        {
                            "url": "https://b2b.instinctfusionx.xyz/public/api/v1/products/category/1?page=3",
                            "label": "3",
                            "active": false
                        },
                        {
                            "url": "https://b2b.instinctfusionx.xyz/public/api/v1/products/category/1?page=4",
                            "label": "4",
                            "active": false
                        },
                        {
                            "url": "https://b2b.instinctfusionx.xyz/public/api/v1/products/category/1?page=2",
                            "label": "Next &raquo;",
                            "active": false
                        }
                    ],
                    "next_page_url": "https://b2b.instinctfusionx.xyz/public/api/v1/products/category/1?page=2",
                    "path": "https://b2b.instinctfusionx.xyz/public/api/v1/products/category/1",
                    "per_page": 12,
                    "prev_page_url": null,
                    "to": 12,
                    "total": 48
                }
            }
        }
    }
}

// SSR Page Component
export default async function CategoryProductsPage({ params, searchParams }) {
    console.log('🚀 SSR: Category Products Page Loading');
    console.log('📁 SSR: Route params:', params);
    console.log('🔍 SSR: Search params:', searchParams);

    // Extract category ID from route parameters
    const categoryId = params.id;

    // Validate category ID
    if (!categoryId) {
        console.error('❌ SSR: No category ID provided');
        notFound();
    }

    // Prepare query parameters for API call
    const queryParams = {
        page: searchParams.page || '1',
        per_page: searchParams.per_page || '12',
        search: searchParams.search || '',
        sort: searchParams.sort || 'name',
        order: searchParams.order || 'asc',
        brand: searchParams.brand || '',
        min_price: searchParams.min_price || '',
        max_price: searchParams.max_price || ''
    };

    console.log('🔧 SSR: Final query params:', queryParams);

    try {
        // Fetch both products and category info in parallel for better performance
        const [apiResponse, categoryInfo] = await Promise.all([
            GetAllProductsByCategoryId(categoryId, queryParams),
            getCategoryInfo(categoryId)
        ]);

        console.log('✅ SSR: API Response received');
        console.log('📊 SSR: Products count:', apiResponse?.data?.products?.data?.length || 0);
        console.log('📄 SSR: Current page:', apiResponse?.data?.products?.current_page || 1);
        console.log('📄 SSR: Total pages:', apiResponse?.data?.products?.last_page || 1);
        console.log('🔢 SSR: Total products:', apiResponse?.data?.products?.total || 0);
        console.log('🏷️ SSR: Category info:', categoryInfo.name);

        // Check if API call was successful
        if (!apiResponse.success) {
            console.error('❌ SSR: API call failed:', apiResponse);
            throw new Error('Failed to fetch category products');
        }

        return (
            <div className="min-h-screen bg-gray-50">
                {/* Category Header */}
                <div className="bg-white border-b border-gray-100">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                        {/* Category Image Banner */}
                        {categoryInfo.image && (
                            <div className="mb-8">
                                <div className="relative h-48 md:h-64 lg:h-80 rounded-lg overflow-hidden bg-gray-100">
                                    <img
                                        src={categoryInfo.image.startsWith('http')
                                            ? categoryInfo.image
                                            : `https://b2b.instinctfusionx.xyz${categoryInfo.image.startsWith('/') ? '' : '/'}${categoryInfo.image}`
                                        }
                                        alt={`${categoryInfo.name} category banner`}
                                        className="w-full h-full object-cover"
                                        loading="eager"
                                    />
                                    <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                                        <div className="text-center text-white">
                                            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-2">
                                                {categoryInfo.name}
                                            </h1>
                                            <p className="text-lg md:text-xl opacity-90">
                                                {categoryInfo.description}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Category Info */}
                        <div className="text-center">
                            {!categoryInfo.image && (
                                <>
                                    <h1 className="text-3xl font-bold text-gray-900 mb-2">
                                        {categoryInfo.name}
                                    </h1>
                                    <p className="text-gray-500 mb-4">
                                        {categoryInfo.description}
                                    </p>
                                </>
                            )}

                            <div className="flex flex-wrap justify-center items-center gap-4 text-sm text-gray-500">
                                <span className="bg-gray-100 px-3 py-1 rounded-full">
                                    Category ID: {categoryId}
                                </span>
                                <span className="bg-gray-100 px-3 py-1 rounded-full">
                                    Slug: {categoryInfo.slug}
                                </span>
                                <span className="bg-orange-100 text-orange-700 px-3 py-1 rounded-full font-medium">
                                    {apiResponse.data.products.total} products available
                                </span>
                                {categoryInfo.is_featured && (
                                    <span className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full font-medium">
                                        Featured Category
                                    </span>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Products Listing */}
                <CategoryProductsClient
                    initialData={apiResponse.data.products}
                    categoryId={categoryId}
                    categoryInfo={categoryInfo}
                    initialSearchParams={queryParams}
                />
            </div>
        );

    } catch (error) {
        console.error('❌ SSR: Error fetching category products:', error);

        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-red-600 mb-4">
                        Error Loading Category Products
                    </h1>
                    <p className="text-gray-600 mb-4">
                        Failed to load products for category {categoryId}
                    </p>
                    <p className="text-sm text-gray-500">
                        {error.message}
                    </p>
                </div>
            </div>
        );
    }
}
