// Server component for product page with metadata generation
import PublicSingleProductDetailsById from '@/app/components/Products/PublicSingleProductDetailsById'

// API function to get product by ID
async function getProductById(id) {
    try {
        console.log('🔍 Fetching product details for ID:', id);
        
        if (!id) {
            console.log('❌ No ID provided to getProductById');
            return {
                success: false,
                message: 'No product ID provided',
                data: null
            };
        }
        
        // Try real API call first
        try {
            console.log('🌐 Attempting real API call...');
            const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/products/${id}`, {
                cache: 'no-store', // Don't cache for debugging, change to 'force-cache' for production
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });
            
            console.log('📡 API Response status:', response.status);
            
            if (response.ok) {
                const data = await response.json();
                console.log('✅ Real API data received:', data);
                return data;
            } else {
                console.log('⚠️ API response not ok, falling back to mock data');
            }
        } catch (apiError) {
            console.log('⚠️ API call failed, falling back to mock data:', apiError.message);
        }
        
        // Fallback to mock data with real structure
        console.log('🎭 Using mock data for product ID:', id);
        return {
            "success": true,
            "message": "Product retrieved successfully (mock data)",
            "data": {
                "product": {
                    "id": parseInt(id) || 6,
                    "name": `Product ${id} - Premium Wireless Headphones`,
                    "slug": `product-${id}-premium-wireless-headphones`,
                    "sku": `WC-PREMIUM-${String(id).padStart(3, '0')}`,
                    "price": "129.99",
                    "special_price": "119.99",
                    "currency": "AUD",
                    "image": "https://example.com/images/headphones-main.jpg",
                    "brand": "Premium Audio Brand",
                    "rating": 4.5,
                    "review_count": 128,
                    "is_active": true,
                    "approval_status": "approved",
                    "pack_price": {
                        "number_of_products": 1,
                        "per_pack_price": "149.99",
                        "per_pack_special_price": "119.99",
                        "customer_margin": "15.00",
                        "partner_margin": "10.00",
                        "customer_margin_type": "percentage",
                        "partner_margin_type": "percentage",
                        "delivery_fee": null
                    },
                    "bulk_prices": [
                        {
                            "number_of_packs": 5,
                            "per_pack_price": "140.99",
                            "per_pack_special_price": "119.99",
                            "customer_margin": "12.00",
                            "partner_margin": "8.00",
                            "customer_margin_type": "percentage",
                            "partner_margin_type": "percentage",
                            "delivery_fee": null,
                            "sort_order": 0
                        },
                        {
                            "number_of_packs": 10,
                            "per_pack_price": "130.99",
                            "per_pack_special_price": "109.99",
                            "customer_margin": "10.00",
                            "partner_margin": "7.00",
                            "customer_margin_type": "percentage",
                            "partner_margin_type": "percentage",
                            "delivery_fee": null,
                            "sort_order": 1
                        },
                        {
                            "number_of_packs": 20,
                            "per_pack_price": "120.99",
                            "per_pack_special_price": "99.99",
                            "customer_margin": "8.00",
                            "partner_margin": "6.00",
                            "customer_margin_type": "percentage",
                            "partner_margin_type": "percentage",
                            "delivery_fee": null,
                            "sort_order": 2
                        }
                    ],
                    "description": `High-quality noise-cancelling wireless headphones with premium sound quality and 24-hour battery life. Perfect for professional use and entertainment. Product ID: ${id}`,
                    "specifications": {
                        "Battery Life": "24 hours",
                        "Connectivity": "Bluetooth 5.0",
                        "Noise Cancellation": "Active",
                        "Weight": "250g",
                        "Warranty": "2 years"
                    },
                    "images": [
                        {
                            "url": "https://example.com/images/headphones-main.jpg",
                            "alt": `Product ${id} - Premium Wireless Headphones`,
                            "is_main": true
                        },
                        {
                            "url": "https://example.com/images/headphones-angle1.jpg",
                            "alt": `Product ${id} - Premium Wireless Headphones - Angle 1`,
                            "is_main": false
                        },
                        {
                            "url": "https://example.com/images/headphones-angle2.jpg",
                            "alt": `Product ${id} - Premium Wireless Headphones - Angle 2`,
                            "is_main": false
                        }
                    ],
                    "categories": [
                        {
                            "id": 1,
                            "name": "Electronics",
                            "slug": "electronics"
                        },
                        {
                            "id": 2,
                            "name": "Audio Equipment",
                            "slug": "audio-equipment"
                        }
                    ],
                    "stock": {
                        "quantity": Math.floor(Math.random() * 100) + 10,
                        "is_in_stock": true
                    },
                    "meta": {
                        "title": `Product ${id} - Premium Wireless Headphones | Professional Audio`,
                        "description": `High-quality noise-cancelling wireless headphones with premium sound quality and 24-hour battery life. Perfect for professional use and entertainment.`,
                        "keywords": `["headphones", "wireless", "noise-cancelling", "premium audio", "product ${id}"]`
                    }
                }
            }
        };
    } catch (error) {
        console.error('❌ Error in getProductById:', error);
        return {
            success: false,
            message: `Failed to fetch product: ${error.message}`,
            data: null
        };
    }
}

// Export the API function for use in metadata generation
export { getProductById };

// Server component for the product page
export default async function ProductPageServer({ params }) {
    console.log('🔍 ProductPageServer component started');
    console.log('🔍 ProductPageServer received params:', params);
    
    // Handle both direct params and awaited params (Next.js 13+ app router)
    let resolvedParams = params;
    if (params && typeof params.then === 'function') {
        console.log('🔄 Awaiting params...');
        resolvedParams = await params;
    }
    
    console.log('🔍 Resolved params:', resolvedParams);
    
    const productId = resolvedParams?.id;
    console.log('🔍 Extracted productId:', productId);
    
    if (!productId) {
        console.log('❌ No productId found in params');
        return (
            <section className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
                    <p className="text-gray-600 mb-6">The product you're looking for doesn't exist.</p>
                    <p className="text-sm text-gray-500 mb-6">Debug: params = {JSON.stringify(resolvedParams)}</p>
                    <a href="/products" className="bg-orange-500 text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition-colors">
                        Browse Products
                    </a>
                </div>
            </section>
        );
    }
    
    const response = await getProductById(productId);
    
    if (!response.success || !response.data?.product) {
        return (
            <section className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Product</h1>
                    <p className="text-gray-600 mb-6">{response.message || 'Failed to load product details.'}</p>
                    <a href="/products" className="bg-orange-500 text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition-colors">
                        Browse Products
                    </a>
                </div>
            </section>
        );
    }
    
    const product = response.data.product;
    console.log('✅ Product data loaded:', product.name);
    
    return (
        <section>
            <PublicSingleProductDetailsById 
                productData={product}
                productId={productId}
            />
        </section>
    );
}
