// API function to get product by ID using real backend data
async function getProductById(id) {
    try {
        console.log('🔍 Fetching product details for ID:', id);

        if (!id) {
            console.log('❌ No ID provided to getProductById');
            return {
                success: false,
                message: 'No product ID provided',
                data: null
            };
        }

        // Try real API call first
        try {
            console.log('🌐 Attempting real API call...');

            // Create AbortController for timeout handling
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

            const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/products/${id}`, {
                cache: 'force-cache', // Cache for production
                next: { revalidate: 3600 }, // Revalidate every hour
                signal: controller.signal,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            clearTimeout(timeoutId); // Clear timeout if request completes

            console.log('📡 API Response status:', response.status);

            if (response.ok) {
                const data = await response.json();
                console.log('✅ Real API data received:', data);

                // Validate the response structure
                if (data && typeof data === 'object') {
                    return data;
                } else {
                    console.log('⚠️ Invalid API response format');
                    return {
                        success: false,
                        message: 'Invalid API response format',
                        data: null
                    };
                }
            } else {
                console.log(`⚠️ API response not ok (${response.status}): ${response.statusText}`);
                return {
                    success: false,
                    message: `API request failed with status ${response.status}: ${response.statusText}`,
                    data: null
                };
            }
        } catch (apiError) {
            console.log('⚠️ API call failed:', apiError.message);

            // Handle specific error types
            let errorMessage = 'API call failed';
            if (apiError.name === 'AbortError') {
                errorMessage = 'Request timeout - API took too long to respond';
            } else if (apiError.message.includes('fetch')) {
                errorMessage = 'Network error - Unable to connect to server';
            } else {
                errorMessage = `API call failed: ${apiError.message}`;
            }

            return {
                success: false,
                message: errorMessage,
                data: null
            };
        }
    } catch (error) {
        console.error('❌ Error in getProductById:', error);
        return {
            success: false,
            message: `Failed to fetch product: ${error.message}`,
            data: null
        };
    }
}

// SEO-optimized metadata generation using real backend data
export async function generateMetadata({ params }) {
    console.log('🔍 generateMetadata received params:', params);

    // Await params as required by Next.js 15+
    const resolvedParams = await params;
    const productId = resolvedParams?.id;
    console.log('🔍 generateMetadata productId:', productId);

    if (!productId) {
        return {
            title: 'Product Details - B2B Wholesale Marketplace',
            description: 'Browse our comprehensive product catalog in the B2B wholesale marketplace. Find quality products at competitive prices.',
            robots: {
                index: false,
                follow: true,
            }
        };
    }

    try {
        const response = await getProductById(productId);
        console.log('🔍 generateMetadata API response:', response);

        // Add comprehensive null/undefined checks
        if (!response) {
            console.log('❌ generateMetadata: No response received from API');
            throw new Error('No response received from API');
        }

        if (typeof response !== 'object') {
            console.log('❌ generateMetadata: Invalid response type:', typeof response);
            throw new Error('Invalid response type received from API');
        }

        if (response.success && response.data?.product) {
            const product = response.data.product;

            // Parse keywords from meta.keywords (JSON string from API)
            let keywords = [];
            try {
                if (product.meta?.keywords) {
                    keywords = typeof product.meta.keywords === 'string'
                        ? JSON.parse(product.meta.keywords)
                        : Array.isArray(product.meta.keywords)
                            ? product.meta.keywords
                            : [product.meta.keywords];
                }

                // Add additional SEO keywords based on product data
                const additionalKeywords = [
                    product.name,
                    product.brand,
                    product.sku,
                    `${product.brand} ${product.name}`,
                    `${product.name} wholesale`,
                    `${product.name} bulk`,
                    `${product.name} B2B`,
                    `${product.name} ${product.currency}`,
                    ...product.categories?.map(cat => cat.name) || [],
                    'wholesale marketplace',
                    'bulk pricing',
                    'B2B products'
                ];

                keywords = [...new Set([...keywords, ...additionalKeywords])]; // Remove duplicates
            } catch (e) {
                console.log('Error parsing keywords, using fallback');
                keywords = [product.name, product.brand, product.sku, 'wholesale', 'B2B', 'bulk pricing'];
            }

            // Calculate final price with customer margin
            const basePrice = parseFloat(product.pack_price?.per_pack_special_price || product.pack_price?.per_pack_price || product.special_price || product.price || 0);
            const customerMargin = parseFloat(product.pack_price?.customer_margin || 0);
            const finalPrice = customerMargin > 0
                ? (basePrice * (1 + customerMargin / 100)).toFixed(2)
                : basePrice.toFixed(2);

            // Calculate savings if special price exists
            const originalPrice = parseFloat(product.pack_price?.per_pack_price || product.price || 0);
            const specialPrice = parseFloat(product.pack_price?.per_pack_special_price || product.special_price || 0);
            const savings = specialPrice > 0 && originalPrice > specialPrice
                ? (originalPrice - specialPrice).toFixed(2)
                : null;

            // Create rich description for SEO
            const seoDescription = product.meta?.description ||
                `${product.description} | ${product.brand} ${product.name} - SKU: ${product.sku} | Starting from $${finalPrice} ${product.currency} | ${product.stock?.is_in_stock ? 'In Stock' : 'Out of Stock'} | ${product.pack_price?.number_of_products || 1} items per pack${savings ? ` | Save $${savings}` : ''} | Bulk pricing available | B2B Wholesale Marketplace`;

            // Create structured data for rich snippets
            const structuredData = {
                '@context': 'https://schema.org',
                '@type': 'Product',
                name: product.name,
                description: product.description,
                sku: product.sku,
                brand: {
                    '@type': 'Brand',
                    name: product.brand
                },
                offers: {
                    '@type': 'Offer',
                    price: finalPrice,
                    priceCurrency: product.currency,
                    availability: product.stock?.is_in_stock
                        ? 'https://schema.org/InStock'
                        : 'https://schema.org/OutOfStock',
                    condition: 'https://schema.org/NewCondition',
                    seller: {
                        '@type': 'Organization',
                        name: 'B2B Wholesale Marketplace'
                    },
                    priceValidUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 30 days from now
                },
                category: product.categories?.[0]?.name || 'General Products',
                image: product.images?.map(img => img.url) || [product.image],
                aggregateRating: product.rating ? {
                    '@type': 'AggregateRating',
                    ratingValue: product.rating,
                    reviewCount: product.review_count || 0,
                    bestRating: 5,
                    worstRating: 1
                } : undefined,
                manufacturer: {
                    '@type': 'Organization',
                    name: product.brand
                }
            };

            return {
                title: product.meta?.title || `${product.name} - ${product.brand} | SKU: ${product.sku} | B2B Wholesale`,
                description: seoDescription.substring(0, 160), // Limit to 160 characters for SEO
                keywords: keywords.slice(0, 20).join(', '), // Limit to 20 keywords
                robots: {
                    index: product.is_active && product.approval_status === 'approved',
                    follow: true,
                    googleBot: {
                        index: product.is_active && product.approval_status === 'approved',
                        follow: true,
                        'max-video-preview': -1,
                        'max-image-preview': 'large',
                        'max-snippet': -1,
                    },
                },
                openGraph: {
                    title: `${product.name} - ${product.brand}`,
                    description: product.description,
                    type: 'website', // Valid OpenGraph type
                    url: `/products/${productId}`,
                    siteName: 'B2B Wholesale Marketplace',
                    locale: 'en_AU',
                    images: product.images?.length > 0 ? product.images.map(img => ({
                        url: img.url,
                        width: 1200,
                        height: 630,
                        alt: img.alt || `${product.name} - ${product.brand}`,
                        type: 'image/jpeg',
                    })) : product.image ? [{
                        url: product.image,
                        width: 1200,
                        height: 630,
                        alt: `${product.name} - ${product.brand}`,
                        type: 'image/jpeg',
                    }] : [],
                    // Additional product-specific OpenGraph properties
                    productAvailability: product.stock?.is_in_stock ? 'in stock' : 'out of stock',
                    productCondition: 'new',
                    productPriceAmount: finalPrice,
                    productPriceCurrency: product.currency,
                    productBrand: product.brand,
                    productCategory: product.categories?.[0]?.name || 'General Products',
                },
                twitter: {
                    card: 'summary_large_image',
                    title: `${product.name} - ${product.brand}`,
                    description: product.description,
                    images: product.images?.length > 0 ? [product.images[0].url] : product.image ? [product.image] : [],
                    creator: '@B2BMarketplace',
                    site: '@B2BMarketplace',
                },
                alternates: {
                    canonical: `/products/${productId}`,
                },
                other: {
                    // Product-specific meta tags for rich snippets
                    'product:price:amount': finalPrice,
                    'product:price:currency': product.currency,
                    'product:availability': product.stock?.is_in_stock ? 'in stock' : 'out of stock',
                    'product:condition': 'new',
                    'product:brand': product.brand,
                    'product:category': product.categories?.[0]?.name || '',
                    'product:sku': product.sku,
                    'product:rating': product.rating || '',
                    'product:review_count': product.review_count || '',

                    // Business-specific meta tags
                    'business:contact_data:country_name': 'Australia',

                    // Additional SEO meta tags
                    'theme-color': '#fb923c', // Orange-400
                    'msapplication-TileColor': '#fb923c',
                    'apple-mobile-web-app-capable': 'yes',
                    'apple-mobile-web-app-status-bar-style': 'default',
                    'format-detection': 'telephone=no',

                    // Structured data as JSON-LD
                    'application-ld+json': JSON.stringify(structuredData),
                }
            };
        } else {
            console.log('❌ generateMetadata: API response indicates failure or missing product data');
            console.log('Response details:', {
                success: response?.success,
                hasData: !!response?.data,
                hasProduct: !!response?.data?.product,
                message: response?.message
            });
        }
    } catch (error) {
        console.error('❌ Error generating metadata:', error);
        console.error('Error details:', {
            message: error.message,
            stack: error.stack,
            productId: productId
        });
    }

    // Fallback metadata when product data is not available
    console.log('🔄 Using fallback metadata for product ID:', productId);
    return {
        title: 'Product Details - B2B Wholesale Marketplace',
        description: 'Browse our comprehensive product catalog in the B2B wholesale marketplace. Find quality products at competitive prices.',
        robots: {
            index: false,
            follow: true,
        }
    };
}

export default async function ProductPage({ params }) {
    console.log('🔍 ProductPage component started');
    console.log('🔍 ProductPage received params:', params);

    // Await params as required by Next.js 15+
    const resolvedParams = await params;
    const productId = resolvedParams?.id;
    console.log('🔍 Extracted productId:', productId);

    if (!productId) {
        console.log('❌ No productId found in params');
        return (
            <section className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
                    <p className="text-gray-600 mb-6">The product you're looking for doesn't exist.</p>
                    <p className="text-sm text-gray-500 mb-6">Debug: params = {JSON.stringify(resolvedParams)}</p>
                    <a href="/products" className="bg-orange-500 text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition-colors">
                        Browse Products
                    </a>
                </div>
            </section>
        );
    }

    const response = await getProductById(productId);
    console.log('🔍 ProductPage API response:', response);

    // Add comprehensive null/undefined checks
    if (!response) {
        console.log('❌ ProductPage: No response received from API');
        return (
            <section className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Product</h1>
                    <p className="text-gray-600 mb-6">No response received from the server. Please try again later.</p>
                    <a href="/products" className="bg-orange-500 text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition-colors">
                        Browse Products
                    </a>
                </div>
            </section>
        );
    }

    if (typeof response !== 'object') {
        console.log('❌ ProductPage: Invalid response type:', typeof response);
        return (
            <section className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Product</h1>
                    <p className="text-gray-600 mb-6">Invalid response format received from the server.</p>
                    <a href="/products" className="bg-orange-500 text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition-colors">
                        Browse Products
                    </a>
                </div>
            </section>
        );
    }

    if (!response.success || !response.data?.product) {
        console.log('❌ ProductPage: API response indicates failure or missing product data');
        console.log('Response details:', {
            success: response?.success,
            hasData: !!response?.data,
            hasProduct: !!response?.data?.product,
            message: response?.message
        });

        return (
            <section className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-red-600 mb-4">Product Not Found</h1>
                    <p className="text-gray-600 mb-6">
                        {response?.message || 'The product you\'re looking for could not be found or is no longer available.'}
                    </p>
                    <div className="text-sm text-gray-500 mb-6">
                        <p>Product ID: {productId}</p>
                        {process.env.NODE_ENV === 'development' && (
                            <p>Debug: {JSON.stringify({
                                success: response?.success,
                                hasData: !!response?.data,
                                hasProduct: !!response?.data?.product
                            })}</p>
                        )}
                    </div>
                    <a href="/products" className="bg-orange-500 text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition-colors">
                        Browse Products
                    </a>
                </div>
            </section>
        );
    }

    const product = response.data.product;
    console.log('✅ Product data loaded:', product.name);

    // Import the component dynamically to avoid client component issues
    const { default: PublicSingleProductDetailsById } = await import('@/app/components/Products/PublicSingleProductDetailsById');

    return (
        <section>
            <PublicSingleProductDetailsById
                productData={product}
                productId={productId}
            />
        </section>
    );
}
