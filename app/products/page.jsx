import React from 'react'
import AllProducts from '../components/Products/AllProducts'
import UniversalQuery from '../components/Products/UniversalQuery'
import ModularNavbar from '../components/Navigation/ModularNavbar'
import Footer from '../components/LandingPage/Footer'

// Server-side function to fetch search data
async function fetchSearchData(searchQuery) {
    if (!searchQuery) {
        return {
            success: false,
            data: {
                products: { data: [], total: 0 },
                categories: { data: [], total: 0 },
                brands: { data: [], total: 0 },
                total: 0
            }
        };
    }

    try {
        const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/search?query=${searchQuery}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            cache: 'no-store' // Ensure fresh data on each request
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Search API Response:', data);
        return data;
    } catch (error) {
        console.error('Error fetching search data:', error);
        return {
            success: false,
            data: {
                products: { data: [], total: 0 },
                categories: { data: [], total: 0 },
                brands: { data: [], total: 0 },
                total: 0
            }
        };
    }
}

export default async function ProductsPage({ searchParams }) {
    // Await searchParams for Next.js 15 compatibility
    const resolvedSearchParams = await searchParams;

    // Extract search query
    const searchQuery = resolvedSearchParams?.search;
    const hasSearchQuery = searchQuery && searchQuery.trim().length > 0;

    // Fetch search data server-side if search query exists
    const responseQuery = hasSearchQuery ? await fetchSearchData(searchQuery) : null;

    console.log('Server-side Products Page:', {
        searchQuery,
        hasSearchQuery,
        responseQuery: responseQuery?.success
    });

    return (
        <div className="min-h-screen bg-blue-50">
            <ModularNavbar />

            <section className='text-gray-600 bg-blue-50'>
                <UniversalQuery
                    searchParams={resolvedSearchParams}
                    responseQuery={responseQuery}
                    hasSearchQuery={hasSearchQuery}
                />

                <AllProducts />
            </section>

            <Footer />
        </div>
    )
}
