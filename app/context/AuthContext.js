'use client';

import { createContext, useContext, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

const AuthContext = createContext();

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [initialized, setInitialized] = useState(false);
  const router = useRouter();

  // Check authentication status on mount and handle hydration
  useEffect(() => {
    let isMounted = true;

    const initializeAuth = async () => {
      if (typeof window === 'undefined') return;

      // Small delay to ensure localStorage is ready after hydration
      await new Promise(resolve => setTimeout(resolve, 50));

      if (isMounted) {
        checkAuthStatus();
        setInitialized(true);
      }
    };

    initializeAuth();

    return () => {
      isMounted = false;
    };
  }, []);

  // Listen for storage changes and custom events after initialization
  useEffect(() => {
    if (!initialized || typeof window === 'undefined') return;

    let debounceTimer = null;

    const debouncedAuthCheck = () => {
      if (debounceTimer) clearTimeout(debounceTimer);
      debounceTimer = setTimeout(() => {
        checkAuthStatus();
      }, 100);
    };

    const handleStorageChange = (e) => {
      // React to auth token changes (both additions and removals)
      if (e.key && e.key.includes('AuthToken')) {
        console.log('🔄 Auth token changed:', e.key, e.newValue ? 'added/updated' : 'removed');
        debouncedAuthCheck();
      }
      // Also react to localStorage.clear() events
      if (e.key === null) {
        console.log('🔄 LocalStorage cleared, updating auth...');
        debouncedAuthCheck();
      }
    };

    // Listen for custom auth events
    const handleCustomAuthRefresh = () => {
      console.log('🔄 Custom auth refresh event received');
      debouncedAuthCheck();
    };

    const handleAuthLogin = (e) => {
      console.log('🔄 Auth login event received');
      debouncedAuthCheck();
    };

    const handleAuthLogout = () => {
      console.log('🔄 Auth logout event received');
      setUser(null);
      setLoading(false);
    };

    // Add event listeners
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('authRefresh', handleCustomAuthRefresh);
    window.addEventListener('authLogin', handleAuthLogin);
    window.addEventListener('authLogout', handleAuthLogout);

    // Also listen for focus events to refresh auth when user returns to tab
    const handleFocus = () => {
      console.log('🔄 Window focus - checking auth status');
      debouncedAuthCheck();
    };
    window.addEventListener('focus', handleFocus);

    return () => {
      if (debounceTimer) clearTimeout(debounceTimer);
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('authRefresh', handleCustomAuthRefresh);
      window.removeEventListener('authLogin', handleAuthLogin);
      window.removeEventListener('authLogout', handleAuthLogout);
      window.removeEventListener('focus', handleFocus);
    };
  }, [initialized]);

  const checkAuthStatus = () => {
    try {
      // Skip if running on server-side
      if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
        setLoading(false);
        return;
      }

      console.log('🔍 Checking auth status...');

      // Check for tokens in localStorage with error handling
      let superAdminToken, partnerToken, userToken, seoAdminToken;

      try {
        superAdminToken = localStorage.getItem('superAdminAuthToken');
        partnerToken = localStorage.getItem('partnerAuthToken');
        userToken = localStorage.getItem('userAuthToken');
        seoAdminToken = localStorage.getItem('seoAdminAuthToken');
      } catch (storageError) {
        console.error('Error accessing localStorage:', storageError);
        setLoading(false);
        return;
      }

      let userInfo = null;

      // Priority order: superadmin > seo > partner > user
      if (superAdminToken && superAdminToken.trim() !== '') {
        userInfo = {
          role: 'superadmin',
          displayName: 'Super Admin',
          token: superAdminToken,
          tokenType: 'superAdminAuthToken'
        };
        console.log('✅ Super Admin authenticated');
      } else if (seoAdminToken && seoAdminToken.trim() !== '') {
        userInfo = {
          role: 'seo',
          displayName: 'SEO Expert',
          token: seoAdminToken,
          tokenType: 'seoAdminAuthToken'
        };
        console.log('✅ SEO Expert authenticated');
      } else if (partnerToken && partnerToken.trim() !== '') {
        userInfo = {
          role: 'vendor',
          displayName: 'Partner/Vendor',
          token: partnerToken,
          tokenType: 'partnerAuthToken'
        };
        console.log('✅ Partner/Vendor authenticated');
      } else if (userToken && userToken.trim() !== '') {
        userInfo = {
          role: 'buyer',
          displayName: 'Customer/Buyer',
          token: userToken,
          tokenType: 'userAuthToken'
        };
        console.log('✅ Customer/Buyer authenticated');
      } else {
        console.log('❌ No authentication found');
      }

      // Update state with proper comparison
      setUser(prevUser => {
        // More robust comparison
        const prevUserStr = prevUser ? JSON.stringify({
          role: prevUser.role,
          displayName: prevUser.displayName,
          tokenType: prevUser.tokenType
        }) : null;

        const newUserStr = userInfo ? JSON.stringify({
          role: userInfo.role,
          displayName: userInfo.displayName,
          tokenType: userInfo.tokenType
        }) : null;

        const hasChanged = prevUserStr !== newUserStr;

        if (hasChanged) {
          console.log('🔄 User state updated:', userInfo?.role || 'logged out');
          console.log('Previous:', prevUser?.role || 'none', '→ New:', userInfo?.role || 'none');
        }

        return userInfo;
      });
    } catch (error) {
      console.error('Error checking auth status:', error);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    try {
      console.log('🚪 Logging out...');

      // Skip if running on server-side
      if (typeof window === 'undefined') {
        return;
      }

      // Clear user state immediately to prevent UI flicker
      setUser(null);
      setLoading(true);

      // Clear localStorage with error handling
      try {
        localStorage.removeItem('superAdminAuthToken');
        localStorage.removeItem('partnerAuthToken');
        localStorage.removeItem('userAuthToken');
        localStorage.removeItem('seoAdminAuthToken');
      } catch (storageError) {
        console.error('Error clearing localStorage:', storageError);
      }

      // Clear cookies (only if document is available)
      if (typeof document !== 'undefined') {
        try {
          document.cookie = 'superAdminAuthToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
          document.cookie = 'partnerAuthToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
          document.cookie = 'userAuthToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
          document.cookie = 'seoAdminAuthToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
        } catch (cookieError) {
          console.error('Error clearing cookies:', cookieError);
        }
      }

      // Dispatch custom event for cross-component synchronization
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('authLogout'));
      }

      // Small delay to ensure state is cleared, then redirect
      setTimeout(() => {
        setLoading(false);
        router.push('/');
        // Force a page reload to ensure clean state
        setTimeout(() => {
          window.location.reload();
        }, 100);
      }, 50);

      console.log('✅ Successfully logged out');
    } catch (error) {
      console.error('❌ Error during logout:', error);
      setLoading(false);
    }
  };

  const login = (userInfo) => {
    console.log('🔑 Login called with:', userInfo);

    // Set loading state during login process
    setLoading(true);

    // Update user state immediately
    setUser(userInfo);

    // Dispatch custom event for cross-component synchronization
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('authLogin', { detail: userInfo }));
    }

    // Trigger a fresh auth check to ensure state is current and consistent
    setTimeout(() => {
      checkAuthStatus();
      setLoading(false);
    }, 100);
  };

  // Method to manually refresh auth status
  const refreshAuth = () => {
    console.log('🔄 Manually refreshing auth status...');

    // Set loading state during refresh
    setLoading(true);

    // Small delay to ensure any pending localStorage operations complete
    setTimeout(() => {
      checkAuthStatus();

      // Dispatch custom event for other components
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('authRefresh'));
      }
    }, 50);
  };

  // Force refresh auth (for use after login/logout operations)
  const forceRefreshAuth = () => {
    console.log('🔄 Force refreshing auth status...');
    setLoading(true);
    setUser(null);

    setTimeout(() => {
      checkAuthStatus();
    }, 100);
  };

  const value = {
    user,
    setUser,
    loading,
    initialized,
    login,
    logout,
    checkAuthStatus,
    refreshAuth,
    forceRefreshAuth,
    isAuthenticated: !!user,
    isRole: (role) => user?.role === role
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
