'use client';

import { createContext, useContext, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

const AuthContext = createContext();

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [initialized, setInitialized] = useState(false);
  const router = useRouter();

  // Check authentication status only once on initial mount
  useEffect(() => {
    if (!initialized) {
      checkAuthStatus();
      setInitialized(true);
    }
  }, [initialized]);

  // Listen for storage changes (for cross-tab logout) only after initialization
  useEffect(() => {
    if (!initialized || typeof window === 'undefined') return;

    const handleStorageChange = (e) => {
      // React to auth token changes (both additions and removals)
      if (e.key && e.key.includes('AuthToken')) {
        console.log('🔄 Auth token changed:', e.key, e.newValue ? 'added/updated' : 'removed');
        checkAuthStatus();
      }
      // Also react to localStorage.clear() events
      if (e.key === null) {
        console.log('🔄 LocalStorage cleared, updating auth...');
        checkAuthStatus();
      }
    };

    // Listen for custom auth refresh events
    const handleCustomAuthRefresh = () => {
      console.log('🔄 Custom auth refresh event received');
      checkAuthStatus();
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('authRefresh', handleCustomAuthRefresh);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('authRefresh', handleCustomAuthRefresh);
    };
  }, [initialized]);

  const checkAuthStatus = () => {
    try {
      // Skip if running on server-side
      if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
        setLoading(false);
        return;
      }

      console.log('🔍 Checking auth status...');

      // Check for tokens in localStorage only (more reliable than cookies for client-side)
      const superAdminToken = localStorage.getItem('superAdminAuthToken');
      const partnerToken = localStorage.getItem('partnerAuthToken');
      const userToken = localStorage.getItem('userAuthToken');
      const seoAdminToken = localStorage.getItem('seoAdminAuthToken');

      let userInfo = null;

      if (superAdminToken) {
        userInfo = {
          role: 'superadmin',
          displayName: 'Super Admin',
          token: superAdminToken,
          tokenType: 'superAdminAuthToken'
        };
        console.log('✅ Super Admin authenticated');
      } else if (seoAdminToken) {
        userInfo = {
          role: 'seo',
          displayName: 'SEO Expert',
          token: seoAdminToken,
          tokenType: 'seoAdminAuthToken'
        };
        console.log('✅ SEO Expert authenticated');
      } else if (partnerToken) {
        userInfo = {
          role: 'vendor',
          displayName: 'Partner/Vendor',
          token: partnerToken,
          tokenType: 'partnerAuthToken'
        };
        console.log('✅ Partner/Vendor authenticated');
      } else if (userToken) {
        userInfo = {
          role: 'buyer',
          displayName: 'Customer/Buyer',
          token: userToken,
          tokenType: 'userAuthToken'
        };
        console.log('✅ Customer/Buyer authenticated');
      } else {
        console.log('❌ No authentication found');
      }

      // Only update state if user info has actually changed
      setUser(prevUser => {
        const hasChanged = JSON.stringify(prevUser) !== JSON.stringify(userInfo);
        if (hasChanged) {
          console.log('🔄 User state updated:', userInfo?.role || 'logged out');
        }
        return userInfo;
      });
    } catch (error) {
      console.error('Error checking auth status:', error);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    try {
      console.log('🚪 Logging out...');

      // Skip if running on server-side
      if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
        return;
      }

      // Clear localStorage
      localStorage.removeItem('superAdminAuthToken');
      localStorage.removeItem('partnerAuthToken');
      localStorage.removeItem('userAuthToken');
      localStorage.removeItem('seoAdminAuthToken');

      // Clear cookies (only if document is available)
      if (typeof document !== 'undefined') {
        document.cookie = 'superAdminAuthToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
        document.cookie = 'partnerAuthToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
        document.cookie = 'userAuthToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
        document.cookie = 'seoAdminAuthToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      }

      // Clear user state immediately
      setUser(null);

      // Redirect to home page
      router.push('/');

      console.log('✅ Successfully logged out');
    } catch (error) {
      console.error('❌ Error during logout:', error);
    }
  };

  const login = (userInfo) => {
    console.log('🔑 Login called with:', userInfo);
    setUser(userInfo);
    // Also trigger a fresh auth check to ensure state is current
    setTimeout(() => checkAuthStatus(), 100);
  };

  // Method to manually refresh auth status
  const refreshAuth = () => {
    console.log('🔄 Manually refreshing auth status...');
    checkAuthStatus();

    // Also dispatch custom event for other components
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('authRefresh'));
    }
  };

  const value = {
    user,
    setUser,
    loading,
    initialized,
    login,
    logout,
    checkAuthStatus,
    refreshAuth,
    isAuthenticated: !!user,
    isRole: (role) => user?.role === role
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
