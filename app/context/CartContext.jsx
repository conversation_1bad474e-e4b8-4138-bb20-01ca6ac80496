'use client'

import { createContext, useContext, useState, useEffect } from 'react'

const CartContext = createContext({})

// Cart Context Provider
export const CartProvider = ({ children }) => {
    const [cartCount, setCartCount] = useState(0)
    const [cartItems, setCartItems] = useState([])
    const [loading, setLoading] = useState(false)

    // Function to fetch cart data and update count
    const fetchCartCount = async () => {
        try {
            setLoading(true)
            const cartId = localStorage.getItem('cartId')
            const token = localStorage.getItem('userAuthToken') ||
                         localStorage.getItem('partnerAuthToken') ||
                         localStorage.getItem('superAdminAuthToken')

            if (!cartId || !token) {
                setCartCount(0)
                setCartItems([])
                return
            }

            const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/carts/${cartId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            })

            const data = await response.json()
            if (data.success && data.data?.cart?.items) {
                const items = data.data.cart.items
                setCartItems(items)
                // Calculate total quantity of all items
                const totalCount = items.reduce((total, item) => total + (item.quantity || 0), 0)
                setCartCount(totalCount)
            } else {
                setCartCount(0)
                setCartItems([])
            }
        } catch (error) {
            console.error('Error fetching cart count:', error)
            setCartCount(0)
            setCartItems([])
        } finally {
            setLoading(false)
        }
    }

    // Function to increment cart count (called when item is added)
    const incrementCartCount = (quantity = 1) => {
        setCartCount(prevCount => prevCount + quantity)
        // Also refresh from server to ensure accuracy
        setTimeout(fetchCartCount, 1000)
    }

    // Function to refresh cart count from server
    const refreshCartCount = () => {
        fetchCartCount()
    }

    // Load cart count on mount
    useEffect(() => {
        fetchCartCount()
    }, [])

    const value = {
        cartCount,
        cartItems,
        loading,
        fetchCartCount,
        incrementCartCount,
        refreshCartCount
    }

    return (
        <CartContext.Provider value={value}>
            {children}
        </CartContext.Provider>
    )
}

export const useCart = () => {
    const context = useContext(CartContext)
    if (!context) {
        throw new Error('useCart must be used within a CartProvider')
    }
    return context
}
