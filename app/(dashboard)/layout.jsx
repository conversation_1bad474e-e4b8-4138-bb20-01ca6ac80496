'use client';

import { useState } from 'react';
import Sidebar from '../components/Layout/Sidebar';
import Navbar from '../components/Layout/Navbar';

export default function DashboardLayout({ children }) {
    const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
    const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);

    const toggleSidebar = () => {
        setSidebarCollapsed(!sidebarCollapsed);
    };

    return (
        <div className="flex h-screen bg-gray-100">
            {/* Mobile sidebar - absolute positioned when open */}
            <div className={`md:hidden fixed inset-0 z-20 transition-opacity ${mobileSidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}>
                <div className="absolute inset-0 bg-gray-600 opacity-75" onClick={() => setMobileSidebarOpen(false)}></div>
                <div className={`absolute inset-y-0 left-0 transform transition duration-300 ${mobileSidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
                    <Sidebar collapsed={false} />
                </div>
            </div>

            {/* Desktop sidebar */}
            <div className="hidden md:block">
                <Sidebar collapsed={sidebarCollapsed} />
            </div>

            {/* Main content */}
            <div className="flex-1 flex flex-col overflow-hidden">
                <Navbar onMenuClick={() => setMobileSidebarOpen(true)} toggleSidebar={toggleSidebar} />

                {/* Main content area */}
                <main className="flex-1 overflow-auto bg-gray-100">
                    {children}
                </main>
            </div>
        </div>
    );
}

