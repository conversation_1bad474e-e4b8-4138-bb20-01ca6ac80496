"use client";

import React, { useState, useEffect } from 'react';
import {
    FaSearch, FaFilter, FaSort, FaEye, FaShoppingCart,
    FaTruck, FaCreditCard, FaUser, FaCalendarAlt,
    FaMoneyBillWave, FaBox, FaBoxOpen, FaShippingFast,
    FaCheckCircle, FaTimesCircle, FaExclamationCircle,
    FaInfoCircle, FaAngleDown, FaAngleUp, FaEnvelope, FaPhone
} from 'react-icons/fa';
import { MdPayment, MdLocalShipping, MdReceipt, MdAttachMoney } from 'react-icons/md';
import { BiDetail, BiPackage } from 'react-icons/bi';

export default function OrdersPage() {
    const [orders, setOrders] = useState([]);
    const [expandedOrder, setExpandedOrder] = useState(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [filters, setFilters] = useState({
        status: '',
        payment_status: '',
        shipping_status: ''
    });
    const [sortConfig, setSortConfig] = useState({
        key: 'created_at',
        direction: 'desc'
    });
    const [loading, setLoading] = useState(true);

    async function getAllOrders() {
        const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/vendor/orders`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + localStorage.getItem('partnerAuthToken')
            }
        });
        const result = await response.json();
        console.log(result);
        console.log(result.data.orders.data);

        setOrders(result.data.orders.data);
        setLoading(false);
    }

    useEffect(() => {
        getAllOrders();
        setLoading(false);
    }, []);

    // Handle search functionality
    const handleSearch = (e) => {
        setSearchQuery(e.target.value);
    };

    // Handle filter changes
    const handleFilterChange = (filterName, value) => {
        setFilters(prev => ({
            ...prev,
            [filterName]: value
        }));
    };

    // Handle sorting
    const requestSort = (key) => {
        let direction = 'asc';
        if (sortConfig.key === key && sortConfig.direction === 'asc') {
            direction = 'desc';
        }
        setSortConfig({ key, direction });
    };

    // Toggle expanded order
    const toggleOrderDetails = (orderId) => {
        setExpandedOrder(expandedOrder === orderId ? null : orderId);
    };

    // Format date
    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    // Format currency
    const formatCurrency = (amount, currencyCode = 'AUD') => {
        return new Intl.NumberFormat('en-AU', {
            style: 'currency',
            currency: currencyCode
        }).format(amount);
    };

    // Filter and sort orders
    const getFilteredAndSortedOrders = () => {
        // First apply search
        let filteredOrders = orders.filter(order => {
            const searchLower = searchQuery.toLowerCase();
            return (
                order.order_number.toLowerCase().includes(searchLower) ||
                order.user?.name?.toLowerCase().includes(searchLower) ||
                order.user?.email?.toLowerCase().includes(searchLower) ||
                order.billing_email?.toLowerCase().includes(searchLower)
            );
        });

        // Then apply filters
        if (filters.status) {
            filteredOrders = filteredOrders.filter(order => order.status === filters.status);
        }
        if (filters.payment_status) {
            filteredOrders = filteredOrders.filter(order => order.payment_status === filters.payment_status);
        }
        if (filters.shipping_status) {
            filteredOrders = filteredOrders.filter(order => order.shipping_status === filters.shipping_status);
        }

        // Then sort
        if (sortConfig.key) {
            filteredOrders.sort((a, b) => {
                // Handle nested properties like 'user.name'
                const keyParts = sortConfig.key.split('.');
                let aValue = a;
                let bValue = b;

                for (const part of keyParts) {
                    aValue = aValue?.[part];
                    bValue = bValue?.[part];
                }

                // Handle string comparison
                if (typeof aValue === 'string' && typeof bValue === 'string') {
                    if (sortConfig.direction === 'asc') {
                        return aValue.localeCompare(bValue);
                    } else {
                        return bValue.localeCompare(aValue);
                    }
                }

                // Handle date comparison
                if (sortConfig.key === 'created_at' || sortConfig.key === 'updated_at') {
                    const dateA = new Date(a[sortConfig.key]);
                    const dateB = new Date(b[sortConfig.key]);
                    return sortConfig.direction === 'asc' ? dateA - dateB : dateB - dateA;
                }

                // Handle numeric comparison
                if (!isNaN(aValue) && !isNaN(bValue)) {
                    return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;
                }

                // Fallback
                return 0;
            });
        }

        return filteredOrders;
    };

    // Status Badge Component
    const StatusBadge = ({ status, type }) => {
        let bgColor, textColor, icon;

        switch (type) {
            case 'order':
                if (status === 'completed') {
                    bgColor = 'bg-green-100';
                    textColor = 'text-green-800';
                    icon = <FaCheckCircle className="mr-1" />;
                } else if (status === 'cancelled') {
                    bgColor = 'bg-red-100';
                    textColor = 'text-red-800';
                    icon = <FaTimesCircle className="mr-1" />;
                } else if (status === 'processing') {
                    bgColor = 'bg-blue-100';
                    textColor = 'text-blue-800';
                    icon = <FaShoppingCart className="mr-1" />;
                } else {
                    bgColor = 'bg-yellow-100';
                    textColor = 'text-yellow-800';
                    icon = <FaExclamationCircle className="mr-1" />;
                }
                break;
            case 'payment':
                if (status === 'paid') {
                    bgColor = 'bg-green-100';
                    textColor = 'text-green-800';
                    icon = <MdPayment className="mr-1" />;
                } else if (status === 'refunded') {
                    bgColor = 'bg-purple-100';
                    textColor = 'text-purple-800';
                    icon = <FaMoneyBillWave className="mr-1" />;
                } else {
                    bgColor = 'bg-red-100';
                    textColor = 'text-red-800';
                    icon = <FaCreditCard className="mr-1" />;
                }
                break;
            case 'shipping':
                if (status === 'delivered') {
                    bgColor = 'bg-green-100';
                    textColor = 'text-green-800';
                    icon = <FaBoxOpen className="mr-1" />;
                } else if (status === 'shipped') {
                    bgColor = 'bg-blue-100';
                    textColor = 'text-blue-800';
                    icon = <FaShippingFast className="mr-1" />;
                } else {
                    bgColor = 'bg-yellow-100';
                    textColor = 'text-yellow-800';
                    icon = <FaBox className="mr-1" />;
                }
                break;
            default:
                bgColor = 'bg-gray-100';
                textColor = 'text-gray-800';
                icon = <FaInfoCircle className="mr-1" />;
        }

        return (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${bgColor} ${textColor}`}>
                {icon}
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </span>
        );
    };

    // Render the order details when expanded
    const renderOrderDetails = (order) => {
        if (!order) return null;

        return (
            <div className="bg-gray-50 p-4 rounded-lg shadow-inner mt-2 mb-4 text-sm text-gray-600">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Order Information */}
                    <div className="bg-white p-4 rounded-lg shadow-sm">
                        <h3 className="font-semibold text-lg mb-3 text-indigo-700 flex items-center">
                            <FaShoppingCart className="mr-2" /> Order Information
                        </h3>
                        <div className="space-y-2">
                            <p className="flex items-center">
                                <span className="font-medium mr-2 text-gray-600">Order ID:</span> {order.id}
                            </p>
                            <p className="flex items-center">
                                <span className="font-medium mr-2 text-gray-600">Order Number:</span> {order.order_number}
                            </p>
                            <p className="flex items-center">
                                <FaCalendarAlt className="mr-2 text-gray-500" />
                                <span className="font-medium mr-2 text-gray-600">Created:</span> {formatDate(order.created_at)}
                            </p>
                            <p className="flex items-center">
                                <FaCalendarAlt className="mr-2 text-gray-500" />
                                <span className="font-medium mr-2 text-gray-600">Updated:</span> {formatDate(order.updated_at)}
                            </p>
                            <p className="flex items-center">
                                <span className="font-medium mr-2 text-gray-600">Payment Method:</span> {order.payment_method.replace('_', ' ')}
                            </p>
                            <p className="flex items-center">
                                <span className="font-medium mr-2 text-gray-600">Transaction ID:</span> {order.payment_transaction_id}
                            </p>
                            {order.customer_notes && (
                                <div className="mt-2 p-2 bg-yellow-50 rounded border border-yellow-200">
                                    <p className="font-medium text-gray-700">Customer Notes:</p>
                                    <p className="text-gray-600">{order.customer_notes}</p>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Customer Information */}
                    <div className="bg-white p-4 rounded-lg shadow-sm">
                        <h3 className="font-semibold text-lg mb-3 text-indigo-700 flex items-center">
                            <FaUser className="mr-2" /> Customer Information
                        </h3>
                        <div className="space-y-2">
                            <p className="flex items-center">
                                <FaUser className="mr-2 text-gray-500" />
                                <span className="font-medium mr-2 text-gray-600">Name:</span> {order.user?.name}
                            </p>
                            <p className="flex items-center">
                                <FaEnvelope className="mr-2 text-gray-500" />
                                <span className="font-medium mr-2 text-gray-600">Email:</span> {order.user?.email}
                            </p>
                            <p className="flex items-center">
                                <FaPhone className="mr-2 text-gray-500" />
                                <span className="font-medium mr-2 text-gray-600">Billing Phone:</span> {order.billing_phone}
                            </p>
                            <p className="flex items-center">
                                <FaPhone className="mr-2 text-gray-500" />
                                <span className="font-medium mr-2 text-gray-600">Shipping Phone:</span> {order.shipping_phone}
                            </p>
                        </div>
                    </div>

                    {/* Billing Address */}
                    <div className="bg-white p-4 rounded-lg shadow-sm">
                        <h3 className="font-semibold text-lg mb-3 text-indigo-700 flex items-center">
                            <MdReceipt className="mr-2" /> Billing Address
                        </h3>
                        <div className="whitespace-pre-line">
                            {order.billing_address}
                        </div>
                        <p className="mt-2">
                            <FaEnvelope className="inline mr-1 text-gray-500" /> {order.billing_email}
                        </p>
                    </div>

                    {/* Shipping Address */}
                    <div className="bg-white p-4 rounded-lg shadow-sm">
                        <h3 className="font-semibold text-lg mb-3 text-indigo-700 flex items-center">
                            <MdLocalShipping className="mr-2" /> Shipping Address
                        </h3>
                        <div className="whitespace-pre-line">
                            {order.shipping_address}
                        </div>
                    </div>

                    {/* Order Items */}
                    <div className="bg-white p-4 rounded-lg shadow-sm md:col-span-2">
                        <h3 className="font-semibold text-lg mb-3 text-indigo-700 flex items-center">
                            <BiPackage className="mr-2" /> Order Items
                        </h3>
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SKU</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subtotal</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tax</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {order.items.map((item) => (
                                        <tr key={item.id} className="hover:bg-gray-50">
                                            <td className="px-4 py-2 whitespace-nowrap">{item.product_name}</td>
                                            <td className="px-4 py-2 whitespace-nowrap">{item.product_sku}</td>
                                            <td className="px-4 py-2 whitespace-nowrap">{item.quantity}</td>
                                            <td className="px-4 py-2 whitespace-nowrap">{formatCurrency(item.unit_price, order.currency_code)}</td>
                                            <td className="px-4 py-2 whitespace-nowrap">{formatCurrency(item.subtotal, order.currency_code)}</td>
                                            <td className="px-4 py-2 whitespace-nowrap">{formatCurrency(item.tax_amount, order.currency_code)}</td>
                                            <td className="px-4 py-2 whitespace-nowrap">{formatCurrency(item.total_amount, order.currency_code)}</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    {/* Order Totals */}
                    <div className="bg-white p-4 rounded-lg shadow-sm md:col-span-2">
                        <h3 className="font-semibold text-lg mb-3 text-indigo-700 flex items-center">
                            <MdAttachMoney className="mr-2" /> Order Summary
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <h4 className="font-medium text-gray-700 mb-2">Customer Totals</h4>
                                <div className="space-y-1 text-sm">
                                    <p className="flex justify-between">
                                        <span className="text-gray-600">Subtotal:</span>
                                        <span>{formatCurrency(order.subtotal, order.currency_code)}</span>
                                    </p>
                                    <p className="flex justify-between">
                                        <span className="text-gray-600">Discount:</span>
                                        <span>{formatCurrency(order.discount_amount, order.currency_code)}</span>
                                    </p>
                                    <p className="flex justify-between">
                                        <span className="text-gray-600">Shipping:</span>
                                        <span>{formatCurrency(order.shipping_amount, order.currency_code)}</span>
                                    </p>
                                    <p className="flex justify-between">
                                        <span className="text-gray-600">Tax:</span>
                                        <span>{formatCurrency(order.tax_amount, order.currency_code)}</span>
                                    </p>
                                    <p className="flex justify-between font-bold pt-1 border-t">
                                        <span>Total:</span>
                                        <span>{formatCurrency(order.total_amount, order.currency_code)}</span>
                                    </p>
                                </div>
                            </div>
                            {/* vendor total don't show it */}
                            {/* <div>
                                <h4 className="font-medium text-gray-700 mb-2">Vendor Totals</h4>
                                <div className="space-y-1 text-sm">
                                    <p className="flex justify-between">
                                        <span className="text-gray-600">Subtotal:</span>
                                        <span>{formatCurrency(order.vendor_subtotal, order.currency_code)}</span>
                                    </p>
                                    <p className="flex justify-between">
                                        <span className="text-gray-600">Discount:</span>
                                        <span>{formatCurrency(order.vendor_discount, order.currency_code)}</span>
                                    </p>
                                    <p className="flex justify-between">
                                        <span className="text-gray-600">Tax:</span>
                                        <span>{formatCurrency(order.vendor_tax, order.currency_code)}</span>
                                    </p>
                                    <p className="flex justify-between font-bold pt-1 border-t">
                                        <span>Total:</span>
                                        <span>{formatCurrency(order.vendor_total, order.currency_code)}</span>
                                    </p>
                                </div>
                            </div> */}
                        </div>
                    </div>

                    {/* Order History */}
                    {order.history && order.history.length > 0 && (
                        <div className="bg-white p-4 rounded-lg shadow-sm md:col-span-2">
                            <h3 className="font-semibold text-lg mb-3 text-indigo-700 flex items-center">
                                <FaCalendarAlt className="mr-2" /> Order History
                            </h3>
                            <div className="space-y-2">
                                {order.history.map((historyItem) => (
                                    <div key={historyItem.id} className="flex items-start p-2 border-b border-gray-100 last:border-0">
                                        <div className="flex-shrink-0 mr-3">
                                            <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-500">
                                                <FaInfoCircle />
                                            </div>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium">{historyItem.comment}</p>
                                            <p className="text-xs text-gray-500">{formatDate(historyItem.created_at)}</p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        );
    };

    return (
        <div className="p-4 md:p-6 bg-gray-50 min-h-screen">
            <div className="max-w-7xl mx-auto">
                <h1 className="text-2xl font-bold text-gray-800 mb-6">Orders Management</h1>

                {/* Search and Filters */}
                <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
                        {/* Search */}
                        <div className="relative flex-grow max-w-md">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <FaSearch className="text-gray-400" />
                            </div>
                            <input
                                type="text"
                                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                placeholder="Search by order #, customer name or email"
                                value={searchQuery}
                                onChange={handleSearch}
                            />
                        </div>

                        {/* Filters */}
                        <div className="flex flex-wrap gap-2">
                            <div className="relative inline-block text-left">
                                <select
                                    className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                                    value={filters.status}
                                    onChange={(e) => handleFilterChange('status', e.target.value)}
                                >
                                    <option value="">Order Status: All</option>
                                    <option value="pending">Pending</option>
                                    <option value="processing">Processing</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>

                            <div className="relative inline-block text-left">
                                <select
                                    className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                                    value={filters.payment_status}
                                    onChange={(e) => handleFilterChange('payment_status', e.target.value)}
                                >
                                    <option value="">Payment Status: All</option>
                                    <option value="paid">Paid</option>
                                    <option value="pending">Pending</option>
                                    <option value="refunded">Refunded</option>
                                    <option value="failed">Failed</option>
                                </select>
                            </div>

                            <div className="relative inline-block text-left">
                                <select
                                    className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                                    value={filters.shipping_status}
                                    onChange={(e) => handleFilterChange('shipping_status', e.target.value)}
                                >
                                    <option value="">Shipping Status: All</option>
                                    <option value="pending">Pending</option>
                                    <option value="shipped">Shipped</option>
                                    <option value="delivered">Delivered</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Orders Table */}
                <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                        onClick={() => requestSort('order_number')}
                                    >
                                        <div className="flex items-center">
                                            Order #
                                            {sortConfig.key === 'order_number' && (
                                                sortConfig.direction === 'asc' ?
                                                    <FaAngleUp className="ml-1" /> :
                                                    <FaAngleDown className="ml-1" />
                                            )}
                                        </div>
                                    </th>
                                    <th
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                        onClick={() => requestSort('user.name')}
                                    >
                                        <div className="flex items-center">
                                            Customer
                                            {sortConfig.key === 'user.name' && (
                                                sortConfig.direction === 'asc' ?
                                                    <FaAngleUp className="ml-1" /> :
                                                    <FaAngleDown className="ml-1" />
                                            )}
                                        </div>
                                    </th>
                                    <th
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                        onClick={() => requestSort('created_at')}
                                    >
                                        <div className="flex items-center">
                                            Date
                                            {sortConfig.key === 'created_at' && (
                                                sortConfig.direction === 'asc' ?
                                                    <FaAngleUp className="ml-1" /> :
                                                    <FaAngleDown className="ml-1" />
                                            )}
                                        </div>
                                    </th>
                                    <th
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                        onClick={() => requestSort('total_amount')}
                                    >
                                        <div className="flex items-center">
                                            Total
                                            {sortConfig.key === 'total_amount' && (
                                                sortConfig.direction === 'asc' ?
                                                    <FaAngleUp className="ml-1" /> :
                                                    <FaAngleDown className="ml-1" />
                                            )}
                                        </div>
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Payment
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Shipping
                                    </th>
                                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {loading ? (
                                    <tr>
                                        <td colSpan="8" className="px-6 py-4 text-center text-sm text-gray-500">
                                            Loading orders...
                                        </td>
                                    </tr>
                                ) : getFilteredAndSortedOrders().length === 0 ? (
                                    <tr>
                                        <td colSpan="8" className="px-6 py-4 text-center text-sm text-gray-500">
                                            No orders found matching your criteria
                                        </td>
                                    </tr>
                                ) : (
                                    getFilteredAndSortedOrders().map((order) => (
                                        <React.Fragment key={order.id}>
                                            <tr className={`hover:bg-gray-50 ${expandedOrder === order.id ? 'bg-gray-50' : ''}`}>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    {order.order_number}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    <div className="flex items-center">
                                                        <FaUser className="mr-2 text-gray-400" />
                                                        <div>
                                                            <div>{order.user?.name}</div>
                                                            <div className="text-xs text-gray-400">{order.user?.email}</div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    <div className="flex items-center">
                                                        <FaCalendarAlt className="mr-2 text-gray-400" />
                                                        {formatDate(order.created_at)}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                                                    {formatCurrency(order.total_amount, order.currency_code)}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    <StatusBadge status={order.status} type="order" />
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    <StatusBadge status={order.payment_status} type="payment" />
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    <StatusBadge status={order.shipping_status} type="shipping" />
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                    <button
                                                        onClick={() => toggleOrderDetails(order.id)}
                                                        className="text-indigo-600 hover:text-indigo-900 flex items-center justify-end w-full"
                                                    >
                                                        <span className="mr-1">{expandedOrder === order.id ? 'Hide' : 'View'}</span>
                                                        {expandedOrder === order.id ? <FaAngleUp /> : <FaAngleDown />}
                                                    </button>
                                                </td>
                                            </tr>
                                            {expandedOrder === order.id && (
                                                <tr>
                                                    <td colSpan="8" className="px-6 py-0">
                                                        {renderOrderDetails(order)}
                                                    </td>
                                                </tr>
                                            )}
                                        </React.Fragment>
                                    ))
                                )}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    );
}
