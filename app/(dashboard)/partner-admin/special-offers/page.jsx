"use client";

import React, { useState, useEffect } from 'react'
import Link from 'next/link';
import Image from 'next/image';
import PrimaryGradientBtn from '@/app/components/Buttons/PrimaryGradientBtn';
// import PrimaryCartBtn from '@/app/components/Buttons/PrimaryCartBtn';

export default function OffersPage() {

  const initialProducts = [
    {
      id: 1,
      name: 'Smartphone X',
      category: 'Electronics',
      price: 999,
      stock: 50,
      sku: 'SPX00123',
      status: 'active',
      imageUrl: 'https://placehold.co/100x100/667eea/ffffff?text=PhoneX',
      featured: true,
      created: '2023-05-15'
    },
    {
      id: 2,
      name: 'Laptop Pro',
      category: 'Electronics',
      price: 1499,
      stock: 30,
      sku: 'LP00456',
      status: 'active',
      imageUrl: 'https://placehold.co/100x100/667eea/ffffff?text=Laptop',
      featured: false,
      created: '2023-06-22'
    },
    {
      id: 3,
      name: 'Casual T-Shirt',
      category: 'Clothing',
      price: 25,
      stock: 200,
      sku: 'CTS789',
      status: 'active',
      imageUrl: 'https://placehold.co/100x100/667eea/ffffff?text=T-Shirt',
      featured: false,
      created: '2023-04-10'
    },
    {
      id: 4,
      name: 'Coffee Maker',
      category: 'Home & Kitchen',
      price: 89,
      stock: 45,
      sku: 'CM00321',
      status: 'inactive',
      imageUrl: 'https://placehold.co/100x100/667eea/ffffff?text=Coffee',
      featured: true,
      created: '2023-07-01'
    },
    {
      id: 5,
      name: 'Wireless Headphones',
      category: 'Electronics',
      price: 199,
      stock: 75,
      sku: 'WH00654',
      status: 'active',
      imageUrl: 'https://placehold.co/100x100/667eea/ffffff?text=Audio',
      featured: true,
      created: '2023-03-18'
    },
    {
      id: 6,
      name: 'Designer Watch',
      category: 'Accessories',
      price: 299,
      stock: 25,
      sku: 'DW00789',
      status: 'active',
      imageUrl: 'https://placehold.co/100x100/667eea/ffffff?text=Watch',
      featured: false,
      created: '2023-08-05'
    },
    {
      id: 7,
      name: 'Smart Home Hub',
      category: 'Electronics',
      price: 129,
      stock: 60,
      sku: 'SHH00432',
      status: 'draft',
      imageUrl: 'https://placehold.co/100x100/667eea/ffffff?text=Smart',
      featured: false,
      created: '2023-07-22'
    },
    {
      id: 8,
      name: 'Yoga Mat',
      category: 'Sports & Outdoors',
      price: 45,
      stock: 100,
      sku: 'YM00876',
      status: 'active',
      imageUrl: 'https://placehold.co/100x100/667eea/ffffff?text=Yoga',
      featured: false,
      created: '2023-06-10'
    },
  ];

  /* a
    { id: 1, name: 'Electronics', image: 'https://images.unsplash.com/photo-**********-9ebf69173e03?q=80&w=300&auto=format&fit=crop', slug: 'electronics', count: 1249, featured: true },
    { id: 2, name: 'Apparel', image: 'https://images.unsplash.com/photo-1523381210434-271e8be1f52b?q=80&w=300&auto=format&fit=crop', slug: 'apparel', count: 875, new: true },
    { id: 3, name: 'Food & Beverages', image: 'https://images.unsplash.com/photo-1606787366850-de6330128bfc?q=80&w=300&auto=format&fit=crop', slug: 'food-beverages', count: 582 },
    { id: 4, name: 'Home & Garden', image: 'https://images.unsplash.com/photo-1518051870910-a46e30d9db16?q=80&w=300&auto=format&fit=crop', slug: 'home-garden', count: 946, popular: true },
    { id: 5, name: 'Health & Beauty', image: 'https://images.unsplash.com/photo-1571875257727-256c39da42af?q=80&w=300&auto=format&fit=crop', slug: 'health-beauty', count: 723 },
    { id: 6, name: 'Toys & Games', image: 'https://images.unsplash.com/photo-**********-da3b142c6e3d?q=80&w=300&auto=format&fit=crop', slug: 'toys-games', count: 431, new: true },
    { id: 7, name: 'Sports Equipment', image: 'https://images.unsplash.com/photo-1517649763962-0c623066013b?q=80&w=300&auto=format&fit=crop', slug: 'sports-equipment', count: 368 },
    { id: 8, name: 'Automotive', image: 'https://images.unsplash.com/photo-**********-da3b142c6e3d?q=80&w=300&auto=format&fit=crop', slug: 'automotive', count: 512, featured: true },
    { id: 9, name: 'Electronics', image: 'https://images.unsplash.com/photo-**********-9ebf69173e03?q=80&w=300&auto=format&fit=crop', slug: 'electronics', count: 1249, featured: true }
  ]; */

  const mainCategories = [
    {
      name: 'Drinks',
      link: '/categories/drinks',
      image: '/images/productListImage/drinks.png',
      subCategories: [
        'Soft Drinks',
        'Energy Drinks',
        'Water',
        'Sparkling Water',
        'Imported Beverages',
        'Juices',
        'Iced Coffee & Milk Drinks',
        'Sparkling & Flavored Water',
        'Tea & Ready-to-Drink (RTD) Coffee'
      ]
    },
    {
      name: 'Snacks & Confectionery',
      link: '/categories/snacks',
      image: '/images/productListImage/snacks-coffee.png',
      subCategories: [
        'Chocolates',
        'Candy & Lollies',
        'Chips & Savoury Snacks',
        'Spicy Snacks',
        'Chips',
        'Imported Snacks',
        'Biscuits & Cookies',
        'Gums & Mints'
      ]
    },
    {
      name: 'Groceries & Pantry',
      link: '/categories/groceries',
      image: '/images/productListImage/groceries.png',
      subCategories: [
        'Instant Noodles & Pasta',
        'Canned Foods',
        'Sauces & Condiments',
        'Grains, Rice & Flour',
        'Cooking Oils'
      ]
    },
    {
      name: 'International Products',
      link: '/categories/international',
      image: '/images/productListImage/international.png',
      subCategories: [
        'Asian Grocery',
        'Middle Eastern Grocery',
        'Indian Grocery',
        'African Grocery',
        'USA/European Imports'
      ]
    },
    {
      name: 'Frozen & Chilled',
      link: '/categories/frozen',
      image: '/images/productListImage/frozen.png',
      subCategories: [
        'Frozen Meals',
        'Packaged Meats',
        'Ice Cream',
        'Dairy, Cheese & Yogurt'
      ]
    },
    {
      name: 'Personal Care',
      link: '/categories/personal-care',
      image: '/images/productListImage/personal-care.png',
      subCategories: [
        'Soaps, Body Wash',
        'Hair Care',
        'Deodorants',
        'Sanitary & Feminine Care',
        'Shaving & Grooming'
      ]
    },
    {
      name: 'Household & Cleaning',
      link: '/categories/household',
      image: '/images/productListImage/household.png',
      subCategories: [
        'Laundry Detergents',
        'Dishwashing Liquids',
        'Multi-purpose Cleaners',
        'Air Fresheners',
        'Paper Towels & Tissues'
      ]
    },
    {
      name: 'Specials',
      link: '/categories/specials',
      image: '/images/productListImage/special-offer.jpg',
      subCategories: [
        'Weekly Deals',
        'Bundle Offers',
        'Clearance Items',
        'New Price Drops',
        'Limited Time Promotions'
      ]
    },
  ];

  const brandCategories = [
    { name: 'USA Import Products', link: '/brands/usa-imports', image: '/images/brands/usa.webp' },
    { name: 'Coca-Cola Products', link: '/brands/coca-cola', image: '/images/brands/cola.avif' },
    { name: 'Diptouch Products', link: '/brands/diptouch', image: '/images/brands/diptouch.avif' },
    { name: 'Red Bull Products', link: '/brands/red-bull', image: '/images/brands/redbull.avif' },
    { name: 'Suntory Products', link: '/brands/suntory', image: '/images/brands/suntory.avif' },
    { name: 'Schweppes Products', link: '/brands/schweppes', image: '/images/brands/schweppes.avif' },
    { name: 'PepsiCo Products', link: '/brands/pepsico', image: '/images/brands/cola.avif' },
    { name: 'Nestlé Products', link: '/brands/nestle', image: '/images/brands/suntory.avif' },
    { name: 'Unilever Products', link: '/brands/unilever', image: '/images/brands/redbull.avif' },
    { name: 'Local Aussie Brands', link: '/brands/aussie', image: '/images/brands/diptouch.avif' },
  ];


  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState({
    categories: [],
    priceRange: [0, 1000],
    rating: null,
    availability: 'all',
    distributor: [],
    discount: false
  });

  const [activeFiltersCount, setActiveFiltersCount] = useState(0);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);

    // Count active filters
    let count = 0;
    if (selectedFilters.categories.length > 0) count++;
    if (selectedFilters.priceRange[0] > 0 || selectedFilters.priceRange[1] < 1000) count++;
    if (selectedFilters.rating !== null) count++;
    if (selectedFilters.availability !== 'all') count++;
    if (selectedFilters.distributor.length > 0) count++;
    if (selectedFilters.discount) count++;

    setActiveFiltersCount(count);
  }, [selectedFilters]);

  const categories = [
    { id: 'electronics', name: 'Electronics', icon: '🖥️' },
    { id: 'apparel', name: 'Apparel', icon: '👕' },
    { id: 'food', name: 'Food & Beverages', icon: '🍽️' },
    { id: 'home', name: 'Home & Garden', icon: '🏡' },
    { id: 'health', name: 'Health & Beauty', icon: '💆' },
    { id: 'toys', name: 'Toys & Games', icon: '🎮' }
  ];

  const distributors = [
    { id: 'tech-elite', name: 'TechElite Inc.', rating: 4.8 },
    { id: 'eco-apparel', name: 'EcoApparel', rating: 4.5 },
    { id: 'home-goods', name: 'HomeGoods Direct', rating: 4.7 },
    { id: 'global-foods', name: 'Global Foods', rating: 4.9 },
    { id: 'fitness-supplies', name: 'FitLife Supplies', rating: 4.6 }
  ];

  const handleCategoryChange = (categoryId) => {
    setSelectedFilters(prev => {
      const isSelected = prev.categories.includes(categoryId);
      return {
        ...prev,
        categories: isSelected
          ? prev.categories.filter(id => id !== categoryId)
          : [...prev.categories, categoryId]
      };
    });
  };

  const handleDistributorChange = (distributorId) => {
    setSelectedFilters(prev => {
      const isSelected = prev.distributor.includes(distributorId);
      return {
        ...prev,
        distributor: isSelected
          ? prev.distributor.filter(id => id !== distributorId)
          : [...prev.distributor, distributorId]
      };
    });
  };

  const handleRatingChange = (rating) => {
    setSelectedFilters(prev => ({
      ...prev,
      rating: prev.rating === rating ? null : rating
    }));
  };

  const handlePriceChange = (e, index) => {
    const value = parseInt(e.target.value);
    setSelectedFilters(prev => {
      const newPriceRange = [...prev.priceRange];
      newPriceRange[index] = value;
      return { ...prev, priceRange: newPriceRange };
    });
  };

  const clearAllFilters = () => {
    setSelectedFilters({
      categories: [],
      priceRange: [0, 1000],
      rating: null,
      availability: 'all',
      distributor: [],
      discount: false
    });
  };

  // Handle filter visibility without AnimatePresence
  const filterVisible = isFilterOpen || (isMounted && typeof window !== 'undefined' && !window.matchMedia('(max-width: 768px)').matches);


  return (
    <section className='m-5'>
      <h1 className='text-3xl font-semibold text-orange-500'>Special Offers</h1>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-2xl font-bold my-2 text-orange-500">Explore Brand Offers</h2>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
          {brandCategories.map((brand, index) => (
            <Link key={index} href={brand.link} className="block group">
              <div className="bg-white rounded-lg overflow-hidden shadow hover:shadow-md transition p-2 text-center">
                <div className="relative w-full h-24 mb-2">
                  <Image
                    src={brand.image}
                    alt={brand.name}
                    fill
                    className="object-contain"
                  />
                </div>
                <p className="text-sm font-medium text-gray-700 group-hover:text-blue-600">{brand.name}</p>
              </div>
            </Link>
          ))}
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-2xl font-bold my-5 text-orange-500">Top Distributors</h2>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
          {distributors.map((distributor) => (
            <Link
              key={distributor.id}
              href={`/distributors/${distributor.id}`}
              className="block group"
            >
              <div className="bg-white rounded-lg overflow-hidden shadow hover:shadow-md transition p-4 text-center min-h-30">
                <h3 className="text-base font-semibold text-gray-800 group-hover:text-blue-600">
                  {distributor.name}
                </h3>
                <p className="text-sm text-gray-500 mt-1">
                  ⭐ {distributor.rating} Rating
                </p>
              </div>
            </Link>
          ))}
        </div>
      </div>


      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-2xl font-bold my-5 text-orange-500" >Shop by Category</h2>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {categories.map((category) => (
            <Link
              key={category.id}
              href={`/categories/${category.id}`}
              className="block group"
            >
              <div className="bg-white rounded-lg overflow-hidden shadow hover:shadow-md transition p-4 text-center min-h-40">
                <div className="text-4xl mb-2">{category.icon}</div>
                <h3 className="text-base font-semibold text-gray-800 group-hover:text-blue-600">
                  {category.name}
                </h3>
              </div>
            </Link>
          ))}
        </div>
      </div>

      <section className="py-8 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold my-5 text-orange-500">Our Products</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5">
            {initialProducts.map((product) => (
              <div key={product.id} className="bg-white rounded-lg overflow-hidden shadow hover:shadow-lg transition p-4 flex flex-col items-center text-center">
                <img src={product.imageUrl} alt={product.name} className="min-w-24 min-h-24 object-cover mb-4 rounded-xl" />
                <h3 className="text-lg font-semibold text-gray-800">{product.name}</h3>
                <p className="text-gray-500 text-sm mb-1">{product.category}</p>
                <p className="text-blue-600 font-bold">${product.price}</p>

                {/* <PrimaryCartBtn cartPosition="left" /> */}

                <Link href={`/products/${product.id}`} className="mt-3 text-md font-semibold text-blue-500 w-full bg-gray-100 p-3 rounded-lg hover:underline">
                  View Details
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

    </section>
  )
}
