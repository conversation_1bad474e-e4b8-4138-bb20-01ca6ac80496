import ProductForm from '@/app/components/forms/ProductForm';
import AddProduct from '@/app/components/Products/AddProduct'
import config from '@/config';
import React from 'react'

const getBrands = async function () {
    const response = await fetch(`${config.API_CONFIG.BASE_URL}/api/brands`);
    // console.log(response);

    const data = await response.json();
    return data;
}

export const getCategories = async function () {
    const response = await fetch(`${config.API_CONFIG.BASE_URL}/api/categories`);
    // console.log(response);

    const data = await response.json();
    return data;
};

export default async function AddPage() {
    // task!! api under review for brand and category data
    // const { categoriesName } = await getCategories();
    // const { brandsName } = await getBrands();

    return (
        <div>
            {/* <AddProduct categories={categoriesName} brands={brandsName} /> */}

            <ProductForm isAddMode={true} isUpdateMode={false} />
        </div>
    )
}
