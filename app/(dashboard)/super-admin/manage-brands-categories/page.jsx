import Link from "next/link";
import { MdCategory } from "react-icons/md";
import { FaTrademark } from "react-icons/fa";

export default function CategoryBrandManager() {
    return (
        <div className="p-4 sm:p-6 lg:p-8 grid grid-cols-1 lg:grid-cols-2 gap-5 text-gray-700">
            <Link href="/super-admin/manage-brands-categories/categories">
                <div className="p-6 rounded-2xl shadow-md bg-orange-400 text-white hover:bg-orange-500 transition-colors duration-300">
                    <div className="flex items-center gap-3 mb-4">
                        <MdCategory className="w-8 h-8 text-indigo-400" />
                        <h2 className="text-2xl font-semibold">Categories</h2>
                    </div>
                    <p className="text-lg">Manage your product categories.</p>
                </div>
            </Link>
            <Link href="/super-admin/manage-brands-categories/brands">
                <div className="p-6 rounded-2xl shadow-md bg-orange-400 text-white hover:bg-orange-500 transition-colors duration-300">
                    <div className="flex items-center gap-3 mb-4">
                        <FaTrademark className="w-8 h-8 text-indigo-400" />
                        <h2 className="text-2xl font-semibold">Brands</h2>
                    </div>
                    <p className="text-lg">Manage your product brands.</p>
                </div>
            </Link>

            {/* task!! create a subCategory */}

            {/* task!! assign category to a product */}
        </div>
    );
}
