'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
    MagnifyingGlassIcon,
    BuildingOfficeIcon,
    UserIcon,
    PhoneIcon,
    EnvelopeIcon,
    GlobeAltIcon,
    CurrencyDollarIcon,
    UsersIcon,
    CalendarIcon,
    CheckCircleIcon,
    XCircleIcon,
    ExclamationTriangleIcon,
    FunnelIcon,
    EyeIcon
} from '@heroicons/react/24/outline';



export default function CustomersPage() {
    const [customers, setCustomers] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [filterStatus, setFilterStatus] = useState('all');


    // Fetch customers data
    const fetchCustomers = async () => {
        try {
            setLoading(true);
            setError(null);

            const token = localStorage.getItem('superAdminAuthToken');
            if (!token) {
                throw new Error('No authentication token found');
            }

            const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/superadmin/customers`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success && data.data && data.data.customers) {
                setCustomers(data.data.customers);
            } else {
                setError('Invalid response format from server');
            }
        } catch (err) {
            console.error('Error fetching customers:', err);
            setError(err.message || 'Failed to fetch customers');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchCustomers();
    }, []);

    // Filter customers based on search term and status
    const filteredCustomers = customers.filter(customer => {
        const matchesSearch =
            customer.company_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            customer.user?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            customer.user?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            customer.industry?.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesStatus =
            filterStatus === 'all' ||
            (filterStatus === 'verified' && customer.is_verified) ||
            (filterStatus === 'unverified' && !customer.is_verified) ||
            (filterStatus === 'active' && customer.user?.is_active) ||
            (filterStatus === 'inactive' && !customer.user?.is_active);

        return matchesSearch && matchesStatus;
    });

    // Format currency
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(amount);
    };

    // Format date
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    // Loading component
    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 flex items-center justify-center">
                <motion.div
                    animate={{
                        scale: [1, 1.2, 1],
                        rotate: [0, 360]
                    }}
                    transition={{
                        duration: 1.5,
                        repeat: Infinity,
                        ease: "linear"
                    }}
                    className="w-16 h-16 border-4 border-orange-500 border-t-transparent rounded-full"
                />
                <span className="ml-4 text-orange-600 font-medium text-lg">Loading customers...</span>
            </div>
        );
    }

    // Error component
    if (error) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 flex items-center justify-center p-6">
                <div className="bg-white rounded-xl shadow-lg p-8 max-w-md w-full border border-red-200">
                    <div className="text-center">
                        <ExclamationTriangleIcon className="h-16 w-16 text-red-500 mx-auto mb-4" />
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">Authentication Error</h3>
                        <p className="text-gray-600 mb-6">
                            Your super admin token is not valid. Please logout and login again.
                        </p>
                        <button
                            onClick={fetchCustomers}
                            className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200"
                        >
                            Try Again
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-orange-50 to-amber-50 p-6">
            <div className="max-w-7xl mx-auto">
                {/* Header Section */}
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mb-8"
                >
                    <div className="bg-white rounded-xl shadow-lg p-6 border border-orange-200">
                        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                                    Customer Management
                                </h1>
                                <p className="text-gray-600">
                                    Manage and view all registered customers
                                </p>
                                <div className="flex items-center mt-2 text-sm text-orange-600">
                                    <UsersIcon className="h-4 w-4 mr-1" />
                                    <span>{filteredCustomers.length} customers found</span>
                                </div>
                            </div>

                            {/* Search and Filter Controls */}
                            <div className="flex flex-col sm:flex-row gap-3">
                                {/* Search Input */}
                                <div className="relative">
                                    <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                                    <input
                                        type="text"
                                        placeholder="Search customers..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 w-full sm:w-64 text-gray-900"
                                    />
                                </div>

                                {/* Filter Dropdown */}
                                <div className="relative">
                                    <FunnelIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                                    <select
                                        value={filterStatus}
                                        onChange={(e) => setFilterStatus(e.target.value)}
                                        className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 appearance-none bg-white text-gray-900"
                                    >
                                        <option value="all">All Customers</option>
                                        <option value="verified">Verified</option>
                                        <option value="unverified">Unverified</option>
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </motion.div>

                {/* Customers Grid */}
                {filteredCustomers.length === 0 ? (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="bg-white rounded-xl shadow-lg p-12 text-center border border-orange-200"
                    >
                        <UsersIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">No Customers Found</h3>
                        <p className="text-gray-600">
                            {searchTerm || filterStatus !== 'all'
                                ? 'Try adjusting your search or filter criteria.'
                                : 'No customers have been registered yet.'}
                        </p>
                    </motion.div>
                ) : (
                    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                        {filteredCustomers.map((customer, index) => (
                            <motion.div
                                key={customer.id}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: index * 0.1 }}
                                className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-orange-200 overflow-hidden group hover:scale-105"
                            >
                                {/* Card Header */}
                                <div className="bg-gradient-to-r from-orange-500 to-amber-500 p-4">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <div className="bg-white/20 p-2 rounded-lg">
                                                <BuildingOfficeIcon className="h-6 w-6 text-white" />
                                            </div>
                                            <div>
                                                <h3 className="font-semibold text-white text-lg truncate">
                                                    {customer.company_name}
                                                </h3>
                                                <p className="text-orange-100 text-sm">
                                                    {customer.business_type}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="flex flex-col items-end space-y-1">
                                            {customer.is_verified ? (
                                                <CheckCircleIcon className="h-5 w-5 text-green-300" />
                                            ) : (
                                                <XCircleIcon className="h-5 w-5 text-red-300" />
                                            )}
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                customer.user?.is_active
                                                    ? 'bg-green-100 text-green-800'
                                                    : 'bg-red-100 text-red-800'
                                            }`}>
                                                {customer.user?.is_active ? 'Active' : 'Inactive'}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {/* Card Body */}
                                <div className="p-6 space-y-4">
                                    {/* User Information */}
                                    <div className="space-y-3">
                                        <div className="flex items-center space-x-3">
                                            <UserIcon className="h-5 w-5 text-orange-500" />
                                            <div>
                                                <p className="font-medium text-gray-900">{customer.user?.name}</p>
                                                <p className="text-sm text-gray-600">{customer.user?.email}</p>
                                            </div>
                                        </div>

                                        <div className="flex items-center space-x-3">
                                            <PhoneIcon className="h-5 w-5 text-orange-500" />
                                            <p className="text-sm text-gray-600">{customer.user?.phone || 'N/A'}</p>
                                        </div>

                                        {customer.company_website && (
                                            <div className="flex items-center space-x-3">
                                                <GlobeAltIcon className="h-5 w-5 text-orange-500" />
                                                <a
                                                    href={customer.company_website}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-sm text-orange-600 hover:text-orange-700 truncate"
                                                >
                                                    {customer.company_website}
                                                </a>
                                            </div>
                                        )}
                                    </div>

                                    {/* Business Information */}
                                    <div className="border-t border-gray-200 pt-4 space-y-3">
                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <p className="text-xs text-gray-500 uppercase tracking-wide">Industry</p>
                                                <p className="text-sm font-medium text-gray-900">{customer.industry}</p>
                                            </div>
                                            <div>
                                                <p className="text-xs text-gray-500 uppercase tracking-wide">Employees</p>
                                                <p className="text-sm font-medium text-gray-900">{customer.number_of_employees}</p>
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <p className="text-xs text-gray-500 uppercase tracking-wide">Revenue</p>
                                                <p className="text-sm font-medium text-gray-900">
                                                    {formatCurrency(customer.annual_revenue)}
                                                </p>
                                            </div>
                                            <div>
                                                <p className="text-xs text-gray-500 uppercase tracking-wide">Established</p>
                                                <p className="text-sm font-medium text-gray-900">{customer.established_year}</p>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Footer */}
                                    <div className="border-t border-gray-200 pt-4">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center space-x-2">
                                                <CalendarIcon className="h-4 w-4 text-gray-400" />
                                                <span className="text-xs text-gray-500">
                                                    Joined {formatDate(customer.created_at)}
                                                </span>
                                            </div>
                                            <div className="flex items-center space-x-2">
                                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                    customer.discount_tier === 'premium'
                                                        ? 'bg-purple-100 text-purple-800'
                                                        : customer.discount_tier === 'gold'
                                                        ? 'bg-yellow-100 text-yellow-800'
                                                        : 'bg-gray-100 text-gray-800'
                                                }`}>
                                                    {customer.discount_tier}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </motion.div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
}
