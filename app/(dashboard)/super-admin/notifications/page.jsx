import NotificationForm from '@/app/components/Dashboard/NotificationForm';
import React from 'react'

import { IoTime } from "react-icons/io5";
import { MdDateRange } from "react-icons/md";

export default function NotificationsPage() {
    const notifications = [
        { id: 1, title: "New Order", message: "You have a new order from <PERSON>.", time: 1745564433769 },
        { id: 2, title: "Product Approved", message: "Your product has been approved.", time: 1745564433769 },
        { id: 3, title: "New Customer", message: "A new customer has signed up.", time: 1745564433769 },
        { id: 4, title: "Order Shipped", message: "Your order has been shipped.", time: 1745564433769 },
        { id: 5, title: "Product Out of Stock", message: "Your product is out of stock.", time: 1745564433769 },
        { id: 6, title: "New Review", message: "You have a new review from <PERSON>.", time: 1745564433769 },
        { id: 7, title: "Password Changed", message: "Your password has been changed successfully.", time: 1745564433769 },
        { id: 8, title: "New Message", message: "You have a new message from customer support.", time: 1745564433769 },
        { id: 9, title: "System Update", message: "A new system update is available.", time: 1745564433769 },
        { id: 10, title: "Maintenance Reminder", message: "Don't forget to perform your weekly maintenance.", time: 1745564433769 },
    ];

    return (
        <section className="p-4">
            <h1 className="text-2xl font-bold text-center text-orange-400 p-5">Notifications</h1>

            <NotificationForm />

            <ul className="my-4 space-y-2">
                {notifications.map((notification) => (
                    <li key={notification.id}
                        className="max-w-xl mx-auto p-4 bg-gray-800 rounded-md shadow-md flex flex-col items-start">
                        <div className="flex justify-between w-full">
                            <p className="font-bold">
                                {notification.title}
                            </p>
                            <p className="text-sm text-gray-400 flex">
                                <MdDateRange />{new Date(notification.time).toDateString()}
                            </p>
                        </div>
                        <p className='text-gray-300 my-3 flex'>
                            {notification.message} <span className="text-sm text-gray-400 flex items-center mx-1">
                                <IoTime />1 mins ago</span>
                        </p>
                    </li>
                ))}
            </ul>
        </section>
    )
}
