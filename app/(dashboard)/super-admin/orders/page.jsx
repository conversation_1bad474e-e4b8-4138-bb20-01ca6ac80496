"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON>aS<PERSON>ch, FaFilter, FaSort, FaEye, FaShoppingCart, FaTruck, FaCreditCard } from 'react-icons/fa';
import { MdPayment, MdLocalShipping } from 'react-icons/md';
import { BiDetail } from 'react-icons/bi';

export default function OrdersPage() {
    const [orders, setOrders] = useState([]);
    const [expandedOrder, setExpandedOrder] = useState(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [filters, setFilters] = useState({
        status: '',
        payment_status: '',
        shipping_status: ''
    });
    const [sortConfig, setSortConfig] = useState({
        key: 'created_at',
        direction: 'desc'
    });

    async function getOrders() {
        try {
            const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/admin/orders`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('superAdminAuthToken')}`
                }
            });
            const data = await response.json();
            console.log(data);

            setOrders(data.data.orders.data);
        } catch (error) {
            console.error('Error fetching orders:', error);
        }
    }

    // Fetch orders data
    useEffect(() => {
        getOrders();
    }, []);

    // Status badge component with dynamic colors
    const StatusBadge = ({ type, status }) => {
        const getStatusColor = () => {
            switch (status) {
                case 'pending': return 'bg-yellow-100 text-yellow-800';
                case 'paid': return 'bg-green-100 text-green-800';
                case 'completed': return 'bg-blue-100 text-blue-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        };

        const getStatusIcon = () => {
            switch (type) {
                case 'order': return <FaShoppingCart className="w-4 h-4" />;
                case 'payment': return <FaCreditCard className="w-4 h-4" />;
                case 'shipping': return <FaTruck className="w-4 h-4" />;
                default: return null;
            }
        };

        return (
            <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ${getStatusColor()}`}>
                {getStatusIcon()}
                {status}
            </span>
        );
    };

    // Search functionality
    const handleSearch = (query) => {
        setSearchQuery(query);
    };

    // Filter functionality
    const handleFilter = (filterType, value) => {
        setFilters(prev => ({ ...prev, [filterType]: value }));
    };

    // Sort functionality
    const handleSort = (key) => {
        setSortConfig(prev => ({
            key,
            direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
        }));
    };

    // Expanded view toggle
    const toggleOrderDetails = (orderId) => {
        setExpandedOrder(expandedOrder === orderId ? null : orderId);
    };

    // Filter and sort orders
    const getFilteredAndSortedOrders = () => {
        console.log("ORDRES ___________________", orders);

        return orders
            .filter(order => {
                const matchesSearch = (
                    order.order_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    order.user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    order.user.name.toLowerCase().includes(searchQuery.toLowerCase())
                );

                const matchesFilters = (
                    (!filters.status || order.status === filters.status) &&
                    (!filters.payment_status || order.payment_status === filters.payment_status) &&
                    (!filters.shipping_status || order.shipping_status === filters.shipping_status)
                );

                return matchesSearch && matchesFilters;
            })
            .sort((a, b) => {
                const aValue = a[sortConfig.key];
                const bValue = b[sortConfig.key];
                return sortConfig.direction === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
            });
    };

    return (
        <div className="p-6 bg-white rounded-lg shadow-lg text-gray-600">
            {/* Search and Filters */}
            <div className="mb-6 flex flex-wrap gap-4">
                <div className="flex-1 min-w-[300px]">
                    <div className="relative">
                        <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <input
                            type="text"
                            placeholder="Search orders..."
                            className="w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                            onChange={(e) => handleSearch(e.target.value)}
                        />
                    </div>
                </div>

                {/* Filter Dropdowns */}
                <div className="flex gap-4 flex-wrap">
                    <select
                        className="px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                        onChange={(e) => handleFilter('status', e.target.value)}
                    >
                        <option value="">Order Status</option>
                        <option value="pending">Pending</option>
                        <option value="completed">Completed</option>
                    </select>

                    <select
                        className="px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                        onChange={(e) => handleFilter('payment_status', e.target.value)}
                    >
                        <option value="">Payment Status</option>
                        <option value="paid">Paid</option>
                        <option value="pending">Pending</option>
                    </select>

                    <select
                        className="px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                        onChange={(e) => handleFilter('shipping_status', e.target.value)}
                    >
                        <option value="">Shipping Status</option>
                        <option value="pending">Pending</option>
                        <option value="shipped">Shipped</option>
                        <option value="delivered">Delivered</option>
                    </select>
                </div>
            </div>

            {/* Orders Table */}
            <div className="overflow-x-auto">
                <table className="w-full">
                    <thead className="bg-gray-50">
                        <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onClick={() => handleSort('order_number')}>
                                <div className="flex items-center gap-2">
                                    Order Number
                                    <FaSort className="text-gray-400" />
                                </div>
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Customer
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onClick={() => handleSort('total_amount')}>
                                <div className="flex items-center gap-2">
                                    Amount
                                    <FaSort className="text-gray-400" />
                                </div>
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                onClick={() => handleSort('created_at')}>
                                <div className="flex items-center gap-2">
                                    Date
                                    <FaSort className="text-gray-400" />
                                </div>
                            </th>
                            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                        {getFilteredAndSortedOrders().map((order, index) => (
                            <React.Fragment key={order.id}>
                                <tr key={order.id} className="hover:bg-gray-50">
                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {order.order_number}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <div>
                                            <div className="font-medium text-gray-900">{order.user.name}</div>
                                            <div className="text-gray-500">{order.user.email}</div>
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex flex-col gap-2">
                                            <StatusBadge type="order" status={order.status} />
                                            <StatusBadge type="payment" status={order.payment_status} />
                                            <StatusBadge type="shipping" status={order.shipping_status} />
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <div className="font-medium text-gray-900">
                                            {order.currency_code} {parseFloat(order.total_amount).toFixed(2)}
                                        </div>
                                        <div className="text-xs text-gray-500">
                                            Tax: {order.currency_code} {parseFloat(order.tax_amount).toFixed(2)}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {new Date(order.created_at).toLocaleDateString()}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <button
                                            onClick={() => toggleOrderDetails(order.id)}
                                            className="text-blue-600 hover:text-blue-900 flex items-center gap-2 ml-auto"
                                        >
                                            <BiDetail className="w-5 h-5" />
                                            View Details
                                        </button>
                                    </td>
                                </tr>

                                {/* Expanded Order Details */}
                                {expandedOrder === order.id && (
                                    <tr>
                                        <td colSpan="6" className="px-6 py-4">
                                            <div className="bg-gray-50 p-4 rounded-lg">
                                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                                    {/* Order Information */}
                                                    <div className="bg-white p-4 rounded-lg shadow">
                                                        <h3 className="text-lg font-semibold mb-4">Order Information</h3>
                                                        <div className="space-y-2">
                                                            <p><span className="font-medium">Invoice ID:</span> {order.invoice_id || 'N/A'}</p>
                                                            <p><span className="font-medium">Payment Method:</span> {order.payment_method}</p>
                                                            <p><span className="font-medium">Transaction ID:</span> {order.payment_transaction_id}</p>
                                                            <p><span className="font-medium">Customer Notes:</span> {order.customer_notes || 'N/A'}</p>
                                                        </div>
                                                    </div>

                                                    {/* Billing Information */}
                                                    <div className="bg-white p-4 rounded-lg shadow">
                                                        <h3 className="text-lg font-semibold mb-4">Billing Information</h3>
                                                        <div className="space-y-2">
                                                            <p className="whitespace-pre-line">{order.billing_address}</p>
                                                            <p><span className="font-medium">Phone:</span> {order.billing_phone}</p>
                                                            <p><span className="font-medium">Email:</span> {order.billing_email}</p>
                                                        </div>
                                                    </div>

                                                    {/* Shipping Information */}
                                                    <div className="bg-white p-4 rounded-lg shadow">
                                                        <h3 className="text-lg font-semibold mb-4">Shipping Information</h3>
                                                        <div className="space-y-2">
                                                            <p className="whitespace-pre-line">{order.shipping_address}</p>
                                                            <p><span className="font-medium">Phone:</span> {order.shipping_phone}</p>
                                                        </div>
                                                    </div>

                                                    {/* Order Items */}
                                                    <div className="col-span-full bg-white p-4 rounded-lg shadow">
                                                        <h3 className="text-lg font-semibold mb-4">Order Items</h3>
                                                        <div className="overflow-x-auto">
                                                            <table className="w-full">
                                                                <thead className="bg-gray-50">
                                                                    <tr>
                                                                        <th className="px-4 py-2 text-left">Product</th>
                                                                        <th className="px-4 py-2 text-left">SKU</th>
                                                                        <th className="px-4 py-2 text-right">Quantity</th>
                                                                        <th className="px-4 py-2 text-right">Unit Price</th>
                                                                        <th className="px-4 py-2 text-right">Total</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody className="divide-y divide-gray-200">
                                                                    {order.items.map(item => (
                                                                        <tr key={item.id}>
                                                                            <td className="px-4 py-2">{item.product_name}</td>
                                                                            <td className="px-4 py-2">{item.product_sku}</td>
                                                                            <td className="px-4 py-2 text-right">{item.quantity}</td>
                                                                            <td className="px-4 py-2 text-right">
                                                                                {order.currency_code} {parseFloat(item.unit_price).toFixed(2)}
                                                                            </td>
                                                                            <td className="px-4 py-2 text-right">
                                                                                {order.currency_code} {parseFloat(item.total_amount).toFixed(2)}
                                                                            </td>
                                                                        </tr>
                                                                    ))}
                                                                </tbody>
                                                                <tfoot className="bg-gray-50">
                                                                    <tr>
                                                                        <td colSpan="4" className="px-4 py-2 text-right font-medium">Subtotal:</td>
                                                                        <td className="px-4 py-2 text-right">
                                                                            {order.currency_code} {parseFloat(order.subtotal).toFixed(2)}
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td colSpan="4" className="px-4 py-2 text-right font-medium">Tax:</td>
                                                                        <td className="px-4 py-2 text-right">
                                                                            {order.currency_code} {parseFloat(order.tax_amount).toFixed(2)}
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td colSpan="4" className="px-4 py-2 text-right font-medium">Shipping:</td>
                                                                        <td className="px-4 py-2 text-right">
                                                                            {order.currency_code} {parseFloat(order.shipping_amount).toFixed(2)}
                                                                        </td>
                                                                    </tr>
                                                                    <tr className="font-semibold">
                                                                        <td colSpan="4" className="px-4 py-2 text-right">Total:</td>
                                                                        <td className="px-4 py-2 text-right">
                                                                            {order.currency_code} {parseFloat(order.total_amount).toFixed(2)}
                                                                        </td>
                                                                    </tr>
                                                                </tfoot>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                )}
                            </React.Fragment>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
}
