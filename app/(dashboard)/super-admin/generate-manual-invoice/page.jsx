'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
    PlusIcon,
    TrashIcon,
    DocumentArrowDownIcon,
    CalculatorIcon,
    BuildingOfficeIcon,
    UserIcon,
    CalendarIcon
} from '@heroicons/react/24/outline';
import { downloadManualInvoicePDF } from '@/app/utils/manualInvoicePdfGenerator';
import toast from 'react-hot-toast';

export default function GenerateManualInvoicePage() {
    // Company Information (Editable with Diptouch defaults)
    const [companyInfo, setCompanyInfo] = useState({
        name: 'Diptouch Pty Ltd',
        abn: '***********',
        email: '<EMAIL>',
        address: 'Australia'
    });

    // Customer Information
    const [customerInfo, setCustomerInfo] = useState({
        name: '',
        abn: '',
        address: ''
    });

    // Invoice Details
    const [invoiceDetails, setInvoiceDetails] = useState({
        invoiceNumber: `DIP-INV-${Date.now().toString().slice(-6)}`,
        invoiceDate: new Date().toISOString().split('T')[0],
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
        status: 'Due' // Default status
    });

    // Payment Details
    const [customPayment, setCustomPayment] = useState(false);
    const [amountPaid, setAmountPaid] = useState('');

    // Products
    const [products, setProducts] = useState([
        { id: 1, description: '', quantity: 1, finalPrice: '', lineTotal: 0 }
    ]);

    // Charges
    const [gst, setGst] = useState(10); // Editable GST (default 10%)
    const [deliveryCharge, setDeliveryCharge] = useState(0);

    // Payment Information (Editable with Diptouch defaults)
    const [paymentInfo, setPaymentInfo] = useState({
        bankName: 'WESTPAC',
        accountName: 'DIPTOUCH PTY LTD',
        bsb: '732454',
        accountNumber: '836606',
        reference: 'DIPTOUCHREF1004'
    });

    const addProduct = () => {
        const newProduct = {
            id: Date.now(),
            description: '',
            quantity: 1,
            finalPrice: '',
            lineTotal: 0
        };
        setProducts([...products, newProduct]);
    };

    const removeProduct = (id) => {
        if (products.length > 1) {
            setProducts(products.filter(product => product.id !== id));
        }
    };

    const updateProduct = (id, field, value) => {
        setProducts(products.map(product => {
            if (product.id === id) {
                const updatedProduct = { ...product, [field]: value };
                // Calculate line total when quantity or price changes
                if (field === 'quantity' || field === 'finalPrice') {
                    const quantity = field === 'quantity' ? parseInt(value) || 0 : parseInt(updatedProduct.quantity) || 0;
                    const price = field === 'finalPrice' ? parseFloat(value) || 0 : parseFloat(updatedProduct.finalPrice) || 0;
                    updatedProduct.lineTotal = quantity * price;
                }
                return updatedProduct;
            }
            return product;
        }));
    };

    const updateCompanyInfo = (field, value) => {
        setCompanyInfo(prev => ({ ...prev, [field]: value }));
    };

    const updateCustomerInfo = (field, value) => {
        setCustomerInfo(prev => ({ ...prev, [field]: value }));
    };

    const updateInvoiceDetails = (field, value) => {
        setInvoiceDetails(prev => ({ ...prev, [field]: value }));
    };

    const updatePaymentInfo = (field, value) => {
        setPaymentInfo(prev => ({ ...prev, [field]: value }));
    };

    // Calculate remaining balance
    const calculateRemainingBalance = () => {
        const totalAmount = calculateTotal();
        const paidAmount = parseFloat(amountPaid || 0);
        return Math.max(0, totalAmount - paidAmount);
    };

    // Validate payment amount
    const validatePaymentAmount = (value) => {
        const numValue = parseFloat(value);
        const totalAmount = calculateTotal();

        if (isNaN(numValue) || numValue < 0) {
            return false;
        }

        if (numValue > totalAmount) {
            return false;
        }

        return true;
    };

    // Handle payment amount change
    const handleAmountPaidChange = (value) => {
        if (value === '' || validatePaymentAmount(value)) {
            setAmountPaid(value);
        }
    };

    const calculateSubtotal = () => {
        return products.reduce((total, product) => {
            return total + (product.lineTotal || 0);
        }, 0);
    };

    const calculateGST = () => {
        return calculateSubtotal() * (parseFloat(gst) / 100);
    };

    const calculateTotal = () => {
        return calculateSubtotal() + calculateGST() + parseFloat(deliveryCharge || 0);
    };

    const handleGenerateInvoice = () => {
        // Validate form
        const hasEmptyProductFields = products.some(product =>
            !product.description.trim() || !product.finalPrice || !product.quantity
        );

        const hasEmptyCustomerFields = !customerInfo.name.trim();

        if (hasEmptyProductFields) {
            toast.error('Please fill in all product fields');
            return;
        }

        if (hasEmptyCustomerFields) {
            toast.error('Please fill in customer information');
            return;
        }

        const invoiceData = {
            companyInfo,
            customerInfo,
            invoiceDetails,
            products,
            gst,
            deliveryCharge: parseFloat(deliveryCharge || 0),
            subtotal: calculateSubtotal(),
            gstAmount: calculateGST(),
            total: calculateTotal(),
            paymentInfo,
            customPayment,
            amountPaid: parseFloat(amountPaid || 0),
            remainingBalance: calculateRemainingBalance()
        };

        try {
            const fileName = downloadManualInvoicePDF(invoiceData);
            toast.success(`Invoice generated successfully! Downloaded as ${fileName}`);
        } catch (error) {
            console.error('Error generating PDF:', error);
            toast.error('Failed to generate invoice PDF');
        }
    };

    return (
        <div className="min-h-screen bg-gray-100 p-6">
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="max-w-6xl mx-auto"
            >
                {/* Header */}
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">Generate Manual Invoice</h1>
                    <p className="text-gray-600">Create professional invoices for Diptouch Pty Ltd</p>
                </div>

                {/* Main Form */}
                <div className="bg-white rounded-lg shadow-lg p-8">
                    {/* Invoice Header Information */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                        {/* Company Information (Editable) */}
                        <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                            <div className="flex items-center mb-4">
                                <BuildingOfficeIcon className="h-6 w-6 text-orange-600 mr-2" />
                                <h3 className="text-lg font-semibold text-gray-800">Company Information</h3>
                            </div>
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Company Name
                                    </label>
                                    <input
                                        type="text"
                                        value={companyInfo.name}
                                        onChange={(e) => updateCompanyInfo('name', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        ABN
                                    </label>
                                    <input
                                        type="text"
                                        value={companyInfo.abn}
                                        onChange={(e) => updateCompanyInfo('abn', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Email
                                    </label>
                                    <input
                                        type="email"
                                        value={companyInfo.email}
                                        onChange={(e) => updateCompanyInfo('email', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Address
                                    </label>
                                    <input
                                        type="text"
                                        value={companyInfo.address}
                                        onChange={(e) => updateCompanyInfo('address', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black"
                                    />
                                </div>
                            </div>
                        </div>

                        {/* Invoice Details */}
                        <div className="space-y-4">
                            <div className="flex items-center mb-4">
                                <CalendarIcon className="h-6 w-6 text-orange-600 mr-2" />
                                <h3 className="text-lg font-semibold text-gray-800">Invoice Details</h3>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Invoice Number
                                </label>
                                <input
                                    type="text"
                                    value={invoiceDetails.invoiceNumber}
                                    onChange={(e) => updateInvoiceDetails('invoiceNumber', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black"
                                />
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Invoice Date
                                    </label>
                                    <input
                                        type="date"
                                        value={invoiceDetails.invoiceDate}
                                        onChange={(e) => updateInvoiceDetails('invoiceDate', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Due Date
                                    </label>
                                    <input
                                        type="date"
                                        value={invoiceDetails.dueDate}
                                        onChange={(e) => updateInvoiceDetails('dueDate', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black"
                                    />
                                </div>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Invoice Status *
                                </label>
                                <select
                                    value={invoiceDetails.status}
                                    onChange={(e) => updateInvoiceDetails('status', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black"
                                    required
                                >
                                    <option value="Paid">Paid</option>
                                    <option value="Due">Due</option>
                                    <option value="Unpaid">Unpaid</option>
                                </select>
                            </div>

                            <div>
                                <div className="flex items-center mb-2">
                                    <input
                                        type="checkbox"
                                        id="customPayment"
                                        checked={customPayment}
                                        onChange={(e) => setCustomPayment(e.target.checked)}
                                        className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                                    />
                                    <label htmlFor="customPayment" className="ml-2 text-sm font-medium text-gray-700">
                                        Custom Payment Amount
                                    </label>
                                </div>

                                {customPayment && (
                                    <div className="space-y-3">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Amount Paid (AUD)
                                            </label>
                                            <input
                                                type="number"
                                                step="0.01"
                                                min="0"
                                                max={calculateTotal()}
                                                value={amountPaid}
                                                onChange={(e) => handleAmountPaidChange(e.target.value)}
                                                placeholder="0.00"
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black"
                                            />
                                        </div>

                                        {amountPaid && (
                                            <div className="bg-blue-50 p-3 rounded-md border border-blue-200">
                                                <div className="text-sm text-gray-700">
                                                    <div className="flex justify-between">
                                                        <span>Total Amount:</span>
                                                        <span className="font-medium text-black">${calculateTotal().toFixed(2)}</span>
                                                    </div>
                                                    <div className="flex justify-between">
                                                        <span>Amount Paid:</span>
                                                        <span className="font-medium text-black">${parseFloat(amountPaid || 0).toFixed(2)}</span>
                                                    </div>
                                                    <div className="flex justify-between border-t border-blue-300 pt-2 mt-2">
                                                        <span className="font-semibold">Remaining Balance:</span>
                                                        <span className="font-semibold text-black">${calculateRemainingBalance().toFixed(2)}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Customer Information */}
                    <div className="mb-8">
                        <div className="flex items-center mb-4">
                            <UserIcon className="h-6 w-6 text-orange-600 mr-2" />
                            <h3 className="text-lg font-semibold text-gray-800">Invoice To</h3>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Customer Name *
                                </label>
                                <input
                                    type="text"
                                    value={customerInfo.name}
                                    onChange={(e) => updateCustomerInfo('name', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black"
                                    placeholder="Enter customer name"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    ABN (Optional)
                                </label>
                                <input
                                    type="text"
                                    value={customerInfo.abn}
                                    onChange={(e) => updateCustomerInfo('abn', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black"
                                    placeholder="Enter ABN"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Address (Optional)
                                </label>
                                <input
                                    type="text"
                                    value={customerInfo.address}
                                    onChange={(e) => updateCustomerInfo('address', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black"
                                    placeholder="Enter address"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Products Section */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-semibold text-gray-800">Products Purchased</h3>
                            <button
                                onClick={addProduct}
                                className="flex items-center px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors duration-200"
                            >
                                <PlusIcon className="h-5 w-5 mr-2" />
                                Add Product
                            </button>
                        </div>

                        {/* Products Table Header */}
                        <div className="bg-gray-50 border border-gray-200 rounded-t-lg">
                            <div className="grid grid-cols-12 gap-4 p-4 font-medium text-gray-700 text-sm">
                                <div className="col-span-1 text-center">#</div>
                                <div className="col-span-5">Product Description</div>
                                <div className="col-span-2 text-center">Quantity</div>
                                <div className="col-span-2 text-center">Final Price (AUD)</div>
                                <div className="col-span-2 text-center">Line Total (AUD)</div>
                            </div>
                        </div>

                        {/* Products Table Body */}
                        <div className="border-l border-r border-gray-200">
                            {products.map((product, index) => (
                                <motion.div
                                    key={product.id}
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: index * 0.1 }}
                                    className="grid grid-cols-12 gap-4 p-4 border-b border-gray-200 items-center"
                                >
                                    <div className="col-span-1 text-center font-medium text-gray-600">
                                        {index + 1}
                                    </div>

                                    <div className="col-span-5">
                                        <input
                                            type="text"
                                            value={product.description}
                                            onChange={(e) => updateProduct(product.id, 'description', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black"
                                            placeholder="Enter product description"
                                        />
                                    </div>

                                    <div className="col-span-2">
                                        <input
                                            type="number"
                                            min="1"
                                            value={product.quantity}
                                            onChange={(e) => updateProduct(product.id, 'quantity', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black text-center"
                                        />
                                    </div>

                                    <div className="col-span-2">
                                        <input
                                            type="number"
                                            step="0.01"
                                            value={product.finalPrice}
                                            onChange={(e) => updateProduct(product.id, 'finalPrice', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black text-center"
                                            placeholder="0.00"
                                        />
                                    </div>

                                    <div className="col-span-2 flex items-center justify-between">
                                        <span className="font-medium text-gray-800 text-center flex-1">
                                            ${product.lineTotal.toFixed(2)}
                                        </span>
                                        <button
                                            onClick={() => removeProduct(product.id)}
                                            disabled={products.length === 1}
                                            className={`ml-2 p-1 rounded transition-colors duration-200 ${
                                                products.length === 1
                                                    ? 'text-gray-400 cursor-not-allowed'
                                                    : 'text-red-500 hover:text-red-700 hover:bg-red-50'
                                            }`}
                                        >
                                            <TrashIcon className="h-4 w-4" />
                                        </button>
                                    </div>
                                </motion.div>
                            ))}
                        </div>

                        {/* Add Product Row */}
                        <div className="border border-gray-200 rounded-b-lg bg-gray-50 p-4">
                            <button
                                onClick={addProduct}
                                className="flex items-center text-orange-600 hover:text-orange-700 font-medium"
                            >
                                <PlusIcon className="h-4 w-4 mr-1" />
                                Add Another Product
                            </button>
                        </div>
                    </div>

                    {/* Additional Charges Section */}
                    <div className="mb-8">
                        <h3 className="text-lg font-semibold text-gray-800 mb-4">Additional Charges</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    GST (%)
                                </label>
                                <input
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    max="100"
                                    value={gst}
                                    onChange={(e) => setGst(e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black"
                                    placeholder="10.00"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Delivery Charge (AUD)
                                </label>
                                <input
                                    type="number"
                                    step="0.01"
                                    value={deliveryCharge}
                                    onChange={(e) => setDeliveryCharge(e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black"
                                    placeholder="0.00"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Invoice Summary */}
                    <div className="mb-8">
                        <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                            <div className="flex items-center mb-6">
                                <CalculatorIcon className="h-6 w-6 text-orange-500 mr-2" />
                                <h3 className="text-lg font-semibold text-gray-800">Invoice Summary</h3>
                            </div>

                            <div className="space-y-3">
                                <div className="flex justify-between items-center">
                                    <span className="text-gray-600">Total GST ({gst}%):</span>
                                    <span className="font-medium text-black">${calculateGST().toFixed(2)}</span>
                                </div>
                                <div className="flex justify-between items-center">
                                    <span className="text-gray-600">Delivery Charge:</span>
                                    <span className="font-medium text-black">${parseFloat(deliveryCharge || 0).toFixed(2)}</span>
                                </div>
                                <div className="border-t border-gray-300 pt-3">
                                    <div className="flex justify-between items-center">
                                        <span className="text-lg font-semibold text-gray-800">
                                            {invoiceDetails.status === 'Paid' ? 'Total Amount Paid:' :
                                             invoiceDetails.status === 'Unpaid' ? 'Total Amount Unpaid:' :
                                             'Total Amount Due:'}
                                        </span>
                                        <span className="text-xl font-bold text-orange-600">${calculateTotal().toFixed(2)}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Payment Instructions (Editable) */}
                    <div className="mb-8 bg-gray-50 p-6 rounded-lg border border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-800 mb-4">Payment Instructions</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Bank Name
                                </label>
                                <input
                                    type="text"
                                    value={paymentInfo.bankName}
                                    onChange={(e) => updatePaymentInfo('bankName', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Account Name
                                </label>
                                <input
                                    type="text"
                                    value={paymentInfo.accountName}
                                    onChange={(e) => updatePaymentInfo('accountName', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    BSB
                                </label>
                                <input
                                    type="text"
                                    value={paymentInfo.bsb}
                                    onChange={(e) => updatePaymentInfo('bsb', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Account Number
                                </label>
                                <input
                                    type="text"
                                    value={paymentInfo.accountNumber}
                                    onChange={(e) => updatePaymentInfo('accountNumber', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black"
                                />
                            </div>

                            <div className="md:col-span-2">
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Reference
                                </label>
                                <input
                                    type="text"
                                    value={paymentInfo.reference}
                                    onChange={(e) => updatePaymentInfo('reference', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-black"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Generate Button */}
                    <div className="flex justify-center">
                        <button
                            onClick={handleGenerateInvoice}
                            className="flex items-center px-8 py-4 bg-orange-500 hover:bg-orange-600 text-white font-semibold rounded-lg transition-colors duration-200 shadow-lg text-lg"
                        >
                            <DocumentArrowDownIcon className="h-6 w-6 mr-3" />
                            Generate & Download Tax Invoice PDF
                        </button>
                    </div>
                </div>
            </motion.div>
        </div>
    );
}
