'use client';

import Link from 'next/link';
import React, { useState } from 'react';
import { <PERSON>a<PERSON><PERSON>s, FaUserSecret, FaUsersGear, FaUserAstronaut, FaUserPlus } from 'react-icons/fa6';
import { motion, AnimatePresence } from 'framer-motion';

export default function ManageUsers() {
    const [hoveredCard, setHoveredCard] = useState(null);

    const container = {
        hidden: { opacity: 0 },
        show: {
            opacity: 1,
            transition: {
                staggerChildren: 0.2,
                delayChildren: 0.3
            }
        }
    };

    const item = {
        hidden: { opacity: 0, scale: 0.8, y: 50 },
        show: {
            opacity: 1,
            scale: 1,
            y: 0,
            transition: {
                type: "spring",
                stiffness: 100,
                damping: 10
            }
        }
    };

    const userTypes = [
        {
            title: 'Super Admins',
            href: '/super-admin/manage-users/super-admins',
            icon: FaUserSecret,
            color: 'text-rose-500',  // Changed from text-red-500 to text-rose-500
            bgColor: 'bg-gradient-to-br from-rose-100 via-pink-100 to-rose-50',
            borderColor: 'border-rose-200',
            description: 'Manage system-wide administrators with full access control',
            manageButtonBg: 'bg-gradient-to-r from-rose-300 to-rose-500'
        },
        {
            title: 'Admins',
            href: '/super-admin/manage-users/admins',
            icon: FaUserAstronaut,
            color: 'text-indigo-500',
            bgColor: 'bg-gradient-to-br from-indigo-100 via-blue-100 to-indigo-50',
            borderColor: 'border-indigo-200',
            description: 'Oversee administrative staff and their permissions',
            manageButtonBg: 'bg-gradient-to-r from-indigo-300 to-indigo-500'
        },
        {
            title: 'Partners',
            href: '/super-admin/manage-users/partners',
            icon: FaUsersGear,
            color: 'text-green-500',
            bgColor: 'bg-gradient-to-br from-green-100 via-emerald-100 to-green-50',
            borderColor: 'border-green-200',
            description: 'Collaborate with business partners and manage relationships',
            manageButtonBg: 'bg-gradient-to-r from-green-400 to-green-600'
        },
        {
            title: 'Users',
            href: '/super-admin/manage-users/users',
            icon: FaUsers,
            color: 'text-purple-500',
            bgColor: 'bg-gradient-to-br from-purple-100 via-indigo-100 to-purple-50',
            borderColor: 'border-purple-200',
            description: 'Handle end-user accounts and access privileges',
            manageButtonBg: 'bg-gradient-to-r from-purple-300 to-purple-500'
        }
    ];

    return (
        <section className='p-6 text-gray-700 min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 relative'>
            {/* Add New Users Button */}
            <motion.div
                className="fixed right-1 bottom-1 z-20"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 }}
            >
                <Link href="/super-admin/manage-users/add">
                    <motion.button
                        className="flex items-center px-6 py-3 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full font-semibold shadow-lg hover:shadow-xl transform transition-all duration-300"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                    >
                        <FaUserPlus className="mr-2 text-xl" />
                        Add
                    </motion.button>
                </Link>
            </motion.div>

            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, ease: "easeOut" }}
                className="mb-12 text-center"
            >
                <h1 className="text-4xl font-bold mb-3 bg-clip-text text-transparent bg-gradient-to-r from-orange-600 via-red-500 to-pink-600">
                    User Management Hub
                </h1>
                <p className="text-gray-600 text-lg">Your central command for user administration</p>

            </motion.div>

            <motion.div
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
                variants={container}
                initial="hidden"
                animate="show"
            >
                {userTypes.map((type, index) => (
                    <motion.div
                        key={type.title}
                        variants={item}
                        onHoverStart={() => setHoveredCard(index)}
                        onHoverEnd={() => setHoveredCard(null)}
                    >
                        <Link href={type.href} className="block">
                            <motion.div
                                className={`p-8 rounded-2xl ${type.bgColor} border-2 ${type.borderColor} cursor-pointer relative overflow-hidden group`}
                                whileHover={{
                                    scale: 1.05,
                                    y: -10,
                                    transition: { duration: 0.3 }
                                }}
                                whileTap={{ scale: 0.95 }}
                            >
                                {/* Animated background gradient */}
                                <motion.div
                                    className="absolute inset-0 opacity-20"
                                    animate={{
                                        background: [
                                            'radial-gradient(circle at 0% 0%, rgba(255,255,255,0.8) 0%, transparent 50%)',
                                            'radial-gradient(circle at 100% 100%, rgba(255,255,255,0.8) 0%, transparent 50%)'
                                        ],
                                    }}
                                    transition={{
                                        duration: 5,
                                        repeat: Infinity,
                                        repeatType: "reverse"
                                    }}
                                />

                                <div className="flex flex-col items-center space-y-6 relative z-10">
                                    <motion.div
                                        animate={hoveredCard === index ? {
                                            rotate: 360,
                                            scale: [1, 1.2, 1],
                                        } : {}}
                                        transition={{ duration: 0.5 }}
                                        className={`p-4 rounded-full ${type.bgColor} shadow-xl group-hover:shadow-2xl transform transition-all duration-300`}
                                    >
                                        <type.icon className={`w-16 h-16 ${type.color}`} />
                                    </motion.div>

                                    <div className="text-center">
                                        <motion.h2
                                            className={`text-2xl font-bold ${type.color}`}
                                            animate={hoveredCard === index ? {
                                                scale: [1, 1.1, 1],
                                            } : {}}
                                            transition={{ duration: 0.3 }}
                                        >
                                            {type.title}
                                        </motion.h2>
                                        {/* <AnimatePresence>
                                            {hoveredCard === index && (
                                                <motion.p
                                                    initial={{ opacity: 0, y: 10 }}
                                                    animate={{ opacity: 1, y: 0 }}
                                                    exit={{ opacity: 0, y: -10 }}
                                                    className="mt-3 text-md text-gray-600"
                                                >
                                                    {type.description}
                                                </motion.p>
                                            )}
                                        </AnimatePresence> */}
                                    </div>

                                    <motion.button
                                        className={`px-6 py-2 rounded-full text-white 
                                            font-semibold ${type.manageButtonBg} shadow-xl hover:shadow-2xl transform transition-all duration-300`}
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.95 }}
                                    >
                                        {type.title}
                                    </motion.button>
                                </div>
                            </motion.div>
                        </Link>
                    </motion.div>
                ))}
            </motion.div>
        </section>
    );
}
