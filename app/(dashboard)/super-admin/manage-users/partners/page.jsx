'use client';

import React, { useEffect, useState, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaEye, FaEdit, FaTrash } from 'react-icons/fa';
import { FaUserPlus } from 'react-icons/fa6';
import { useRouter } from 'next/navigation';
import Swal from 'sweetalert2';
import Link from 'next/link';

export default function page() {
    const [partners, setPartners] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [viewMode, setViewMode] = useState('table'); // 'table' or 'card'
    const abortControllerRef = useRef(null);
    const [pagination, setPagination] = useState({
        currentPage: 1,
        lastPage: 1,
        total: 0,
        perPage: 15,
        from: 1,
        to: 1
    });
    const router = useRouter();

    // Check viewport width on mount and window resize
    useEffect(() => {
        const handleResize = () => {
            if (window.innerWidth < 768) {
                setViewMode('card');
            } else {
                setViewMode('table');
            }
        };

        // Set initial view mode
        handleResize();

        // Add event listener
        window.addEventListener('resize', handleResize);

        // Clean up
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    const fetchAllPartners = useCallback(async (page = 1, perPage = pagination.perPage) => {
        // Cancel previous request if any
        if (abortControllerRef.current) {
            abortControllerRef.current.abort();
        }

        // Create new abort controller for this request
        abortControllerRef.current = new AbortController();

        setLoading(true);
        setError(null);

        try {
            console.log("Fetching with perPage:", perPage);
            const response = await fetch(
                `${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/admin/users?role_type=vendor&page=${page}&per_page=${perPage}`,
                {
                    headers: {
                        "Authorization": `Bearer ${localStorage.getItem("superAdminAuthToken")}`,
                        "Content-Type": "application/json"
                    },
                    signal: abortControllerRef.current.signal
                }
            );

            if (!response.ok) {
                throw new Error(`API error: ${response.status}`);
            }

            const data = await response.json();
            console.log(data);

            if (data.success) {
                const usersData = data.data.users;
                setPartners(usersData.data);
                setPagination(prev => ({
                    currentPage: usersData.current_page,
                    lastPage: usersData.last_page,
                    total: usersData.total,
                    perPage: perPage,
                    from: usersData.from,
                    to: usersData.to
                }));
            } else {
                throw new Error(data.message || 'Failed to fetch partners');
            }
        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('Error fetching partners:', error);
                setError(error.message || 'Failed to load partners');
            }
        } finally {
            setLoading(false);
        }
    }, [pagination.perPage]);

    useEffect(() => {
        console.log("------------------------ data fetch ------------------------");
        fetchAllPartners(pagination.currentPage, pagination.perPage);

        // Cleanup function to abort any in-flight requests when component unmounts
        return () => {
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }
        };
    }, [fetchAllPartners]);

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const truncateText = (text, length = 10) => {
        if (!text) return '';
        return text.length > length ? `${text.substring(0, length)}...` : text;
    };

    // Function to handle page display for pagination
    const getPaginationButtons = () => {
        const maxButtonsToShow = 5;
        let startPage, endPage;

        if (pagination.lastPage <= maxButtonsToShow) {
            // Show all pages if less than maxButtonsToShow
            startPage = 1;
            endPage = pagination.lastPage;
        } else {
            // Calculate start and end pages for large pagination
            const middlePosition = Math.floor(maxButtonsToShow / 2);

            if (pagination.currentPage <= middlePosition) {
                startPage = 1;
                endPage = maxButtonsToShow;
            } else if (pagination.currentPage + middlePosition >= pagination.lastPage) {
                startPage = pagination.lastPage - maxButtonsToShow + 1;
                endPage = pagination.lastPage;
            } else {
                startPage = pagination.currentPage - middlePosition;
                endPage = pagination.currentPage + middlePosition;
            }
        }

        return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i);
    };

    // Animation variants
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.05
            }
        }
    };

    const itemVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                type: "spring",
                stiffness: 100,
                damping: 12
            }
        }
    };

    const cardVariants = {
        hidden: { opacity: 0, scale: 0.95 },
        visible: {
            opacity: 1,
            scale: 1,
            transition: { duration: 0.3 }
        },
        exit: {
            opacity: 0,
            scale: 0.95,
            transition: { duration: 0.2 }
        }
    };

    async function handleApprove(partnerId) {
        // data is not in admin but vendor. but this is not created.
        console.log(`Approve partner with ID: ${partnerId}`);
        // Implement approve logic
        const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/admin/vendors/${partnerId}/approve`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem("superAdminAuthToken")}`
            }
        });
        const data = await response.json();
        console.log(data);

        if (data.success === true && data.data.vendor.id) {
            Swal.fire({
                title: 'Success',
                text: data.message || 'Vendor approved successfully',
                icon: 'success'
            });

            // Refresh the partner list (re-consider this)
            fetchAllPartners(pagination.currentPage, pagination.perPage);
        } else {
            Swal.fire({
                title: data.message || "Error",
                text: data.details || 'Failed to approve vendor',
                icon: 'error'
            });
        }
    };

    // Add this new function to handle action buttons
    async function handleAction(action, partnerId) {
        switch (action) {
            case 'view':
                console.log(`View partner with ID: ${partnerId}`);
                // Implement view logic
                router.push(`/super-admin/manage-users/partners/${partnerId}`);
                break;
            case 'edit':
                console.log(`Edit partner with ID: ${partnerId}`);
                // Implement edit logic
                router.push(`/super-admin/manage-users/partners/${partnerId}/update`);
                break;
            case 'delete':
                console.log(`Delete partner with ID: ${partnerId}`);

                const deleteConfirme = await Swal.fire({
                    title: 'Are you sure?',
                    text: "Are you sure you want to delete this partner?",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, delete it!'
                });
                console.log(deleteConfirme);


                if (deleteConfirme.isConfirmed === true && deleteConfirme.value === true && deleteConfirme.value === true && partnerId === Number(partnerId)) {
                    const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/admin/users/${partnerId}`, {
                        method: 'DELETE',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${localStorage.getItem("superAdminAuthToken")}`
                        }
                    });

                    const data = await response.json();
                    console.log(data);

                    if (data.success === true) {
                        Swal.fire({
                            title: 'Success',
                            text: data.message || 'Partner deleted successfully',
                            icon: 'success'
                        });

                        // Refresh the partner list (re-consider this)
                        fetchAllPartners(pagination.currentPage, pagination.perPage);
                    } else {
                        Swal.fire({
                            title: data.message || "Error",
                            text: data.details || 'Failed to delete partner',
                            icon: 'error'
                        });
                    }
                }
                break;
            default:
                break;
        }
    };

    // task!!! approve a vendor

    const renderCardView = () => {
        if (partners.length === 0) {
            return (
                <motion.div
                    variants={cardVariants}
                    initial="hidden"
                    animate="visible"
                    className="flex flex-col items-center justify-center py-10"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-gray-300 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                    <p className="text-base text-gray-500 font-medium">No partners found</p>
                    <p className="text-sm text-gray-400 mt-1">Try refreshing or adjusting your filters</p>
                </motion.div>
            );
        }

        return (
            <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4"
            >
                {partners.map((partner, index) => (
                    <motion.div
                        key={partner.id}
                        variants={itemVariants}
                        custom={index}
                        className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-100 hover:shadow-md transition-shadow"
                    >
                        <div className="p-4 border-b border-gray-100 flex items-center space-x-3">
                            {partner.profile_image ? (
                                <motion.img
                                    whileHover={{ scale: 1.1 }}
                                    src={partner.profile_image}
                                    alt={`${partner.name}'s profile`}
                                    className="w-12 h-12 rounded-full object-cover shadow-sm"
                                />
                            ) : (
                                <div className="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center text-blue-600 font-semibold text-lg shadow-sm">
                                    {partner.name.charAt(0).toUpperCase()}
                                </div>
                            )}
                            <div>
                                <h3 className="font-semibold text-gray-800 text-base">{partner.name}</h3>
                                <p className="text-sm text-gray-500">{partner.email}</p>
                            </div>
                        </div>

                        <div className="p-4 space-y-3 text-sm">
                            <div className="flex justify-between">
                                <span className="text-gray-500 font-medium">ID:</span>
                                <span className="text-gray-800">{partner.id}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-gray-500 font-medium">Username:</span>
                                <span className="text-gray-800">{partner.username || 'N/A'}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-gray-500 font-medium">Phone:</span>
                                <span className="text-gray-800">{partner.phone || 'N/A'}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-gray-500 font-medium">Status:</span>
                                <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${partner.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                    {partner.is_active ? 'Active' : 'Inactive'}
                                </span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-gray-500 font-medium">Is Approved:</span>
                                <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${partner.is_approved ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}
                                    onClick={() => handleApprove(partner.id)}>
                                    {partner.is_approved ? 'false' : 'true'}
                                </span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-gray-500 font-medium">Email Status:</span>
                                <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${partner.email_verified_at ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                                    {partner.email_verified_at ? 'Verified' : 'Not Verified'}
                                </span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-gray-500 font-medium">Role:</span>
                                <span className="text-gray-800">{partner.role.name}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-gray-500 font-medium">Created:</span>
                                <span className="text-gray-800 text-xs">{formatDate(partner.created_at)}</span>
                            </div>
                        </div>

                        <div className="p-4 bg-gray-50 border-t border-gray-100 flex justify-center space-x-4">
                            <motion.button
                                whileHover={{ scale: 1.1, color: '#3B82F6' }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => handleAction('view', partner.id)}
                                className="text-blue-600 hover:text-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded-full p-2"
                                title="View details"
                                aria-label={`View details for ${partner.name}`}
                            >
                                <FaEye className='text-3xl' />
                            </motion.button>

                            <motion.button
                                whileHover={{ scale: 1.1, color: '#10B981' }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => handleAction('edit', partner.id)}
                                className="text-green-600 hover:text-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 rounded-full p-2"
                                title="Edit"
                                aria-label={`Edit ${partner.name}`}
                            >
                                <FaEdit className='text-3xl' />
                            </motion.button>

                            <motion.button
                                whileHover={{ scale: 1.1, color: '#EF4444' }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => handleAction('delete', partner.id)}
                                className="text-red-600 hover:text-red-800 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 rounded-full p-2"
                                title="Delete"
                                aria-label={`Delete ${partner.name}`}
                            >
                                <FaTrash className='text-3xl' />
                            </motion.button>
                        </div>
                    </motion.div>
                ))}
            </motion.div>
        );
    };

    const renderTableView = () => {
        return (
            <div className="overflow-x-auto rounded-lg shadow">
                <table className="min-w-full bg-white border-collapse">
                    <thead className="bg-gray-100">
                        <tr>
                            <th className="sticky top-0 px-4 py-3 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">ID</th>
                            <th className="sticky top-0 px-4 py-3 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Name</th>
                            <th className="sticky top-0 px-4 py-3 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Email</th>
                            <th className="sticky top-0 px-4 py-3 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Username</th>
                            <th className="sticky top-0 px-4 py-3 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Email Verified</th>
                            <th className="sticky top-0 px-4 py-3 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Phone</th>
                            <th className="sticky top-0 px-4 py-3 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Address</th>
                            <th className="sticky top-0 px-4 py-3 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Profile Image</th>
                            <th className="sticky top-0 px-4 py-3 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Role ID</th>
                            <th className="sticky top-0 px-4 py-3 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Role Info</th>
                            <th className="sticky top-0 px-4 py-3 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                            <th className="sticky top-0 px-4 py-3 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Is Approved</th>
                            <th className="sticky top-0 px-4 py-3 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Created At</th>
                            <th className="sticky top-0 px-4 py-3 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Updated At</th>
                            <th className="sticky top-0 px-4 py-3 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Action</th>
                        </tr>
                    </thead>
                    <motion.tbody
                        variants={containerVariants}
                        initial="hidden"
                        animate="visible"
                    >
                        {partners.length > 0 ? partners.map((partner, index) => (
                            <motion.tr
                                key={partner.id}
                                variants={itemVariants}
                                custom={index}
                                className="hover:bg-blue-50 transition-colors"
                            >
                                <td className="px-4 py-3 border-b border-gray-200 text-sm">{partner.id}</td>
                                <td className="px-4 py-3 border-b border-gray-200 text-sm font-medium">{partner.name}</td>
                                <td className="px-4 py-3 border-b border-gray-200 text-sm">{partner.email}</td>
                                <td className="px-4 py-3 border-b border-gray-200 text-sm">{partner.username || 'N/A'}</td>
                                <td className="px-4 py-3 border-b border-gray-200 text-sm">
                                    {partner.email_verified_at ? (
                                        <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            Verified
                                        </span>
                                    ) : (
                                        <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            Not Verified
                                        </span>
                                    )}
                                </td>
                                <td className="px-4 py-3 border-b border-gray-200 text-sm">{partner.phone || 'N/A'}</td>
                                <td className="px-4 py-3 border-b border-gray-200 text-sm">{partner.address || 'N/A'}</td>
                                <td className="px-4 py-3 border-b border-gray-200 text-sm">
                                    {partner.profile_image ? (
                                        <motion.img
                                            whileHover={{ scale: 1.2 }}
                                            src={partner.profile_image}
                                            alt={`${partner.name}'s profile`}
                                            className="w-10 h-10 rounded-full object-cover shadow"
                                        />
                                    ) : (
                                        <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-500">
                                            {partner.name.charAt(0)}
                                        </div>
                                    )}
                                </td>
                                <td className="px-4 py-3 border-b border-gray-200 text-sm">{partner.role_id}</td>
                                <td className="px-4 py-3 border-b border-gray-200 text-sm">
                                    <div>
                                        <p className="text-sm text-gray-600 group relative cursor-help">
                                            <span>{truncateText(partner.role.slug)}</span>
                                            <motion.span
                                                initial={{ opacity: 0, scale: 0.95 }}
                                                whileHover={{ opacity: 1, scale: 1 }}
                                                className="absolute left-0 top-full bg-black text-white p-2 rounded text-sm z-10 min-w-[200px] whitespace-normal shadow-lg"
                                            >
                                                {partner.role.slug} ({partner.role.name})
                                            </motion.span>
                                        </p>
                                        <p className="text-xs text-gray-400 group relative cursor-help mt-1">
                                            <span>{truncateText(partner.role.description)}</span>
                                            <motion.span
                                                initial={{ opacity: 0, scale: 0.95 }}
                                                whileHover={{ opacity: 1, scale: 1 }}
                                                className="absolute left-0 top-full bg-black text-white p-2 rounded text-sm z-10 min-w-[200px] whitespace-normal shadow-lg"
                                            >
                                                {partner.role.description}
                                            </motion.span>
                                        </p>
                                    </div>
                                </td>
                                <td className="px-4 py-3 border-b border-gray-200 text-sm">
                                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${partner.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                        {partner.is_active ? 'Active' : 'Inactive'}
                                    </span>
                                </td>
                                <td className="px-4 py-3 border-b border-gray-200 text-sm">
                                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${partner.is_approved ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`} onClick={() => handleApprove(partner.id)}>
                                        {partner.is_approved ? 'true' : 'false'}
                                    </span>
                                </td>
                                <td className="px-4 py-3 border-b border-gray-200 text-sm">{formatDate(partner.created_at)}</td>
                                <td className="px-4 py-3 border-b border-gray-200 text-sm">{formatDate(partner.updated_at)}</td>

                                <td className="px-4 py-3 border-b border-gray-200 text-sm">
                                    <div className="flex items-center space-x-3">
                                        <motion.button
                                            whileHover={{ scale: 1.15, color: '#3B82F6' }}
                                            whileTap={{ scale: 0.95 }}
                                            onClick={() => handleAction('view', partner.id)}
                                            className="text-blue-600 hover:text-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded-full p-1"
                                            title="View details"
                                            aria-label={`View details for ${partner.name}`}
                                        >
                                            <FaEye className='text-3xl' />
                                        </motion.button>

                                        <motion.button
                                            whileHover={{ scale: 1.15, color: '#10B981' }}
                                            whileTap={{ scale: 0.95 }}
                                            onClick={() => handleAction('edit', partner.id)}
                                            className="text-green-600 hover:text-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 rounded-full p-1"
                                            title="Edit"
                                            aria-label={`Edit ${partner.name}`}
                                        >
                                            <FaEdit className='text-3xl' />
                                        </motion.button>

                                        <motion.button
                                            whileHover={{ scale: 1.15, color: '#EF4444' }}
                                            whileTap={{ scale: 0.95 }}
                                            onClick={() => handleAction('delete', partner.id)}
                                            className="text-red-600 hover:text-red-800 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 rounded-full p-1"
                                            title="Delete"
                                            aria-label={`Delete ${partner.name}`}
                                        >
                                            <FaTrash className='text-3xl' />
                                        </motion.button>
                                    </div>
                                </td>
                            </motion.tr>
                        )) : (
                            <motion.tr
                                variants={cardVariants}
                                initial="hidden"
                                animate="visible"
                            >
                                <td colSpan="14" className="px-4 py-10 text-center">
                                    <div className="flex flex-col items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                                        </svg>
                                        <p className="text-gray-500 text-base font-medium">{loading ? 'Loading partners...' : 'No partners found'}</p>
                                        {!loading && (
                                            <p className="text-gray-400 text-sm mt-1">Try refreshing or changing your search parameters</p>
                                        )}
                                    </div>
                                </td>
                            </motion.tr>
                        )}
                    </motion.tbody>
                </table>
            </div>
        );
    };

    return (
        <motion.section
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="p-3 sm:p-4 md:p-6 text-gray-600 bg-gray-50 min-h-screen font-sans relative"
        >
            <div className="bg-white rounded-xl shadow-sm p-4 md:p-6 mb-4 md:mb-6">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
                    <motion.h1
                        initial={{ x: -20, opacity: 0 }}
                        animate={{ x: 0, opacity: 1 }}
                        transition={{ duration: 0.5 }}
                        className="text-xl sm:text-2xl font-bold text-gray-800"
                    >
                        Partners Management
                    </motion.h1>

                    <div className="flex items-center gap-2 w-full sm:w-auto">
                        {viewMode === 'table' && (
                            <button
                                onClick={() => setViewMode('card')}
                                className="text-gray-500 hover:text-gray-700 transition-colors p-2 rounded-md hover:bg-gray-100 md:hidden"
                                aria-label="Switch to card view"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                                </svg>
                            </button>
                        )}

                        {viewMode === 'card' && (
                            <button
                                onClick={() => setViewMode('table')}
                                className="text-gray-500 hover:text-gray-700 transition-colors p-2 rounded-md hover:bg-gray-100 hidden md:block"
                                aria-label="Switch to table view"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                </svg>
                            </button>
                        )}

                        <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => fetchAllPartners(pagination.currentPage, pagination.perPage)}
                            disabled={loading}
                            className="px-3 sm:px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 flex items-center gap-2 disabled:opacity-70 disabled:cursor-not-allowed transition-colors flex-1 sm:flex-none justify-center"
                        >
                            {loading ? (
                                <>
                                    <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <span className="sm:inline">Refreshing...</span>
                                </>
                            ) : (
                                <>
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                    </svg>
                                    <span className="sm:inline">Refresh</span>
                                </>
                            )}
                        </motion.button>
                    </div>
                </div>

                <AnimatePresence>
                    {error && (
                        <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 text-red-600 rounded-md shadow-sm"
                        >
                            <p className="flex items-center text-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                </svg>
                                {error}
                            </p>
                            <button
                                onClick={() => fetchAllPartners(pagination.currentPage, pagination.perPage)}
                                className="mt-2 text-sm text-red-700 underline"
                            >
                                Try again
                            </button>
                        </motion.div>
                    )}
                </AnimatePresence>

                {loading && !partners.length ? (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="flex flex-col justify-center items-center py-16 sm:py-20"
                    >
                        <motion.div
                            animate={{
                                rotate: 360,
                                transition: {
                                    duration: 1,
                                    repeat: Infinity,
                                    ease: "linear"
                                }
                            }}
                            className="rounded-full h-12 w-12 sm:h-16 sm:w-16 border-t-4 border-b-4 border-blue-500"
                        ></motion.div>
                        <motion.p
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.2 }}
                            className="mt-4 text-base sm:text-lg text-gray-600 font-medium"
                        >
                            Loading partners...
                        </motion.p>
                    </motion.div>
                ) : (
                    <>
                        {viewMode === 'table' ? renderTableView() : renderCardView()}
                    </>
                )}
            </div>

            {/* Pagination */}
            <AnimatePresence>
                {partners.length > 0 && (
                    <motion.div
                        variants={cardVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                        className="bg-white rounded-xl shadow-sm p-3 sm:p-4 mt-4 flex flex-col sm:flex-row items-center justify-between gap-4"
                    >
                        <div className="text-xs sm:text-sm text-gray-500 text-center sm:text-left">
                            Showing <span className="font-medium">{pagination.from}</span> to <span className="font-medium">{pagination.to}</span> of <span className="font-medium">{pagination.total}</span> results
                        </div>
                        <div className="flex flex-wrap justify-center gap-1">
                            <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => fetchAllPartners(1, pagination.perPage)}
                                disabled={pagination.currentPage === 1 || loading}
                                className={`px-2 sm:px-3 py-1 text-xs sm:text-sm rounded-md ${pagination.currentPage === 1 || loading
                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 transition-colors'}`}
                                aria-label="Go to first page"
                            >
                                «
                            </motion.button>
                            <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => fetchAllPartners(pagination.currentPage - 1, pagination.perPage)}
                                disabled={pagination.currentPage === 1 || loading}
                                className={`px-2 sm:px-3 py-1 text-xs sm:text-sm rounded-md ${pagination.currentPage === 1 || loading
                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 transition-colors'}`}
                                aria-label="Go to previous page"
                            >
                                ‹
                            </motion.button>

                            {getPaginationButtons().map(pageNum => (
                                <motion.button
                                    key={pageNum}
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                    onClick={() => fetchAllPartners(pageNum, pagination.perPage)}
                                    disabled={loading}
                                    className={`px-2 sm:px-3 py-1 text-xs sm:text-sm rounded-md ${pagination.currentPage === pageNum
                                        ? 'bg-blue-600 text-white'
                                        : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 transition-colors'}`}
                                    aria-current={pagination.currentPage === pageNum ? "page" : undefined}
                                    aria-label={`Go to page ${pageNum}`}
                                >
                                    {pageNum}
                                </motion.button>
                            ))}

                            <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => fetchAllPartners(pagination.currentPage + 1, pagination.perPage)}
                                disabled={pagination.currentPage === pagination.lastPage || loading}
                                className={`px-2 sm:px-3 py-1 text-xs sm:text-sm rounded-md ${pagination.currentPage === pagination.lastPage || loading
                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 transition-colors'}`}
                                aria-label="Go to next page"
                            >
                                ›
                            </motion.button>
                            <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => fetchAllPartners(pagination.lastPage, pagination.perPage)}
                                disabled={pagination.currentPage === pagination.lastPage || loading}
                                className={`px-2 sm:px-3 py-1 text-xs sm:text-sm rounded-md ${pagination.currentPage === pagination.lastPage || loading
                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 transition-colors'}`}
                                aria-label="Go to last page"
                            >
                                »
                            </motion.button>
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>
        </motion.section>
    )
}
