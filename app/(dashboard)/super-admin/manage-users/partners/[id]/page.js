"use client"

import React, { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'

export default function Page() {
    const params = useParams();
    const userId = params.id;
    const [userData, setUserData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [formData, setFormData] = useState({
        // User data
        name: '',
        email: '',
        username: '',
        phone: '',
        address: '',
        is_active: false,

        // Vendor profile data
        company_name: '',
        company_description: '',
        business_registration_number: '',
        is_approved: false,
        rejection_reason: ''
    });

    useEffect(() => {
        async function fetchPartner() {
            try {
                const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/admin/users/${userId}`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem("superAdminAuthToken")}`
                    }
                })
                const data = await response.json();
                console.log(data);

                const user = data.data.user;
                setUserData(user);

                // Initialize form data with user values
                setFormData({
                    // User data
                    name: user.name || '',
                    email: user.email || '',
                    username: user.username || '',
                    phone: user.phone || '',
                    address: user.address || '',
                    is_active: user.is_active || false,

                    // Vendor profile data
                    company_name: user.vendor_profile?.company_name || '',
                    company_description: user.vendor_profile?.company_description || '',
                    business_registration_number: user.vendor_profile?.business_registration_number || '',
                    is_approved: user.vendor_profile?.is_approved || false,
                    rejection_reason: user.vendor_profile?.rejection_reason || ''
                });

                setLoading(false);
            } catch (error) {
                console.error("Error fetching partner:", error);
                setLoading(false);
            }
        }

        if (userId) {
            fetchPartner();
        }
    }, [userId]);

    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
            if (!token) {
                alert('Authentication token not found. Please log in again.');
                return;
            }

            console.log('Submitting form data:', formData);
            const sanitizedFormData = {
                name: "", // update admin name
                email: formData.email,
                username: formData.username,

                phone: formData.phone,
                address: formData.address,
                is_active: formData.is_active,
                role_id: 9,
                is_active
            };

            const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/admin/users/${userId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(sanitizedFormData)
            });
            console.log(response);


            if (!response.ok) {
                throw new Error(`API responded with status: ${response.status}`);
            }

            const result = await response.json();
            if (result.success) {
                alert('Partner information updated successfully');
            } else {
                alert(`Failed to update: ${result.message || 'Unknown error'}`);
            }
        } catch (error) {
            console.error('Error updating partner:', error);
            alert(`Error updating partner: ${error.message}`);
        }
    };

    if (loading) {
        return <div className="container mx-auto p-6 text-center">Loading...</div>;
    }

    return (
        <div className='container mx-auto p-6 text-gray-600'>
            <h1 className="text-2xl font-bold mb-6">Edit Partner Details</h1>

            {userData ? (
                <form onSubmit={handleSubmit} className="space-y-8">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        {/* User Information Section */}
                        <div className="bg-white rounded-lg shadow-md p-6">
                            <h2 className="text-xl font-semibold mb-4 border-b pb-2">User Information</h2>

                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium mb-1">ID</label>
                                    <input
                                        type="text"
                                        value={userData.id}
                                        disabled
                                        className="w-full border border-gray-300 rounded-md p-2 bg-gray-100"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium mb-1">Name</label>
                                    <input
                                        type="text"
                                        name="name"
                                        value={formData.name}
                                        onChange={handleChange}
                                        className="w-full border border-gray-300 rounded-md p-2"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium mb-1">Email</label>
                                    <input
                                        type="email"
                                        name="email"
                                        value={formData.email}
                                        onChange={handleChange}
                                        className="w-full border border-gray-300 rounded-md p-2"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium mb-1">Username</label>
                                    <input
                                        type="text"
                                        name="username"
                                        value={formData.username}
                                        onChange={handleChange}
                                        className="w-full border border-gray-300 rounded-md p-2"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium mb-1">Email Verified At</label>
                                    <input
                                        type="text"
                                        value={userData.email_verified_at ? new Date(userData.email_verified_at).toLocaleString() : "Not verified"}
                                        disabled
                                        className="w-full border border-gray-300 rounded-md p-2 bg-gray-100"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium mb-1">Phone</label>
                                    <input
                                        type="text"
                                        name="phone"
                                        value={formData.phone}
                                        onChange={handleChange}
                                        className="w-full border border-gray-300 rounded-md p-2"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium mb-1">Address</label>
                                    <textarea
                                        name="address"
                                        value={formData.address}
                                        onChange={handleChange}
                                        rows="3"
                                        className="w-full border border-gray-300 rounded-md p-2"
                                    ></textarea>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium mb-1">Profile Image</label>
                                    <div className="flex items-center mt-2">
                                        {userData.profile_image && (
                                            <img src={userData.profile_image} alt="Profile" className="w-16 h-16 rounded-full mr-4" />
                                        )}
                                        <input
                                            type="file"
                                            name="profile_image"
                                            className="w-full border border-gray-300 rounded-md p-2"
                                        />
                                    </div>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium mb-1">Role ID</label>
                                    <input
                                        type="text"
                                        value={userData.role_id}
                                        disabled
                                        className="w-full border border-gray-300 rounded-md p-2 bg-gray-100"
                                    />
                                </div>

                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        id="is_active"
                                        name="is_active"
                                        checked={formData.is_active}
                                        onChange={handleChange}
                                        className="h-4 w-4 mr-2"
                                    />
                                    <label htmlFor="is_active" className="text-sm font-medium">Active Status</label>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium mb-1">Created At</label>
                                    <input
                                        type="text"
                                        value={new Date(userData.created_at).toLocaleString()}
                                        disabled
                                        className="w-full border border-gray-300 rounded-md p-2 bg-gray-100"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium mb-1">Updated At</label>
                                    <input
                                        type="text"
                                        value={new Date(userData.updated_at).toLocaleString()}
                                        disabled
                                        className="w-full border border-gray-300 rounded-md p-2 bg-gray-100"
                                    />
                                </div>
                            </div>
                        </div>

                        {/* Role Information Section */}
                        {userData.role && (
                            <div className="bg-white rounded-lg shadow-md p-6">
                                <h2 className="text-xl font-semibold mb-4 border-b pb-2">Role Information</h2>

                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium mb-1">Role ID</label>
                                        <input
                                            type="text"
                                            value={userData.role.id}
                                            disabled
                                            className="w-full border border-gray-300 rounded-md p-2 bg-gray-100"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium mb-1">Role Name</label>
                                        <input
                                            type="text"
                                            value={userData.role.name}
                                            disabled
                                            className="w-full border border-gray-300 rounded-md p-2 bg-gray-100"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium mb-1">Slug</label>
                                        <input
                                            type="text"
                                            value={userData.role.slug}
                                            disabled
                                            className="w-full border border-gray-300 rounded-md p-2 bg-gray-100"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium mb-1">Description</label>
                                        <textarea
                                            value={userData.role.description}
                                            disabled
                                            rows="3"
                                            className="w-full border border-gray-300 rounded-md p-2 bg-gray-100"
                                        ></textarea>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium mb-1">Created At</label>
                                        <input
                                            type="text"
                                            value={new Date(userData.role.created_at).toLocaleString()}
                                            disabled
                                            className="w-full border border-gray-300 rounded-md p-2 bg-gray-100"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium mb-1">Updated At</label>
                                        <input
                                            type="text"
                                            value={new Date(userData.role.updated_at).toLocaleString()}
                                            disabled
                                            className="w-full border border-gray-300 rounded-md p-2 bg-gray-100"
                                        />
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Vendor Profile Section */}
                    {userData.vendor_profile && (
                        <div className="bg-white rounded-lg shadow-md p-6">
                            <h2 className="text-xl font-semibold mb-4 border-b pb-2">Vendor Profile</h2>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium mb-1">Vendor ID</label>
                                        <input
                                            type="text"
                                            value={userData.vendor_profile.id}
                                            disabled
                                            className="w-full border border-gray-300 rounded-md p-2 bg-gray-100"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium mb-1">User ID</label>
                                        <input
                                            type="text"
                                            value={userData.vendor_profile.user_id}
                                            disabled
                                            className="w-full border border-gray-300 rounded-md p-2 bg-gray-100"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium mb-1">Company Name</label>
                                        <input
                                            type="text"
                                            name="company_name"
                                            value={formData.company_name}
                                            onChange={handleChange}
                                            className="w-full border border-gray-300 rounded-md p-2"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium mb-1">Company Logo</label>
                                        <div className="flex items-center mt-2">
                                            {userData.vendor_profile.company_logo && (
                                                <img
                                                    src={userData.vendor_profile.company_logo}
                                                    alt="Company Logo"
                                                    className="w-24 h-24 object-contain mr-4"
                                                />
                                            )}
                                            <input
                                                type="file"
                                                name="company_logo"
                                                className="w-full border border-gray-300 rounded-md p-2"
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium mb-1">Business Registration Number</label>
                                        <input
                                            type="text"
                                            name="business_registration_number"
                                            value={formData.business_registration_number}
                                            onChange={handleChange}
                                            className="w-full border border-gray-300 rounded-md p-2"
                                        />
                                    </div>

                                    <div className="flex items-center">
                                        <input
                                            type="checkbox"
                                            id="is_approved"
                                            name="is_approved"
                                            checked={formData.is_approved}
                                            onChange={handleChange}
                                            className="h-4 w-4 mr-2"
                                        />
                                        <label htmlFor="is_approved" className="text-sm font-medium">Approval Status</label>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium mb-1">Rejection Reason</label>
                                        <textarea
                                            name="rejection_reason"
                                            value={formData.rejection_reason}
                                            onChange={handleChange}
                                            rows="3"
                                            className="w-full border border-gray-300 rounded-md p-2"
                                        ></textarea>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium mb-1">Created At</label>
                                        <input
                                            type="text"
                                            value={new Date(userData.vendor_profile.created_at).toLocaleString()}
                                            disabled
                                            className="w-full border border-gray-300 rounded-md p-2 bg-gray-100"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium mb-1">Updated At</label>
                                        <input
                                            type="text"
                                            value={new Date(userData.vendor_profile.updated_at).toLocaleString()}
                                            disabled
                                            className="w-full border border-gray-300 rounded-md p-2 bg-gray-100"
                                        />
                                    </div>
                                </div>
                            </div>

                            <div className="mt-4">
                                <label className="block text-sm font-medium mb-1">Company Description</label>
                                <textarea
                                    name="company_description"
                                    value={formData.company_description}
                                    onChange={handleChange}
                                    rows="4"
                                    className="w-full border border-gray-300 rounded-md p-2"
                                ></textarea>
                            </div>
                        </div>
                    )}

                    <div className="flex justify-end">
                        <button
                            type="submit"
                            className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            Update Partner
                        </button>
                    </div>
                </form>
            ) : (
                <div className="text-center py-10">
                    <p>No partner data found. Please try again.</p>
                </div>
            )}
        </div>
    );
}
