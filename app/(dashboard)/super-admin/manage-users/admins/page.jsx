"use client";

import Link from 'next/link';
import React, { useEffect, useState } from 'react'
import { FaUserCircle, FaCalendarAlt, FaClock, FaMapMarkerAlt, FaPhone, FaEnvelope, FaUserTag, FaCheckCircle, FaTimesCircle, FaEdit, FaTrashAlt, FaIdCardAlt } from 'react-icons/fa';
import { FaEye, FaIdBadge, FaIdCard, FaUserPlus } from 'react-icons/fa6';
import { motion } from "framer-motion";
import Swal from 'sweetalert2';

export default function AdminsPage() {
    const [fetchAdminsData, setFetchAdminsData] = useState([]);
    const [adminsData, setAdminsData] = useState([]);

    const getAllAdmins = async function () {
        const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/admin/users?role_type=admin`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('superAdminAuthToken')}`
            }
        });

        const data = await response.json();

        if (data.success === true) {
            setFetchAdminsData(data.data.users.data);
            setAdminsData(data.data.users.data);
        }

        return data;
    };

    useEffect(() => {
        getAllAdmins();
    }, []);

    // Format date function
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    async function handleDeleteAdmin(adminId) {
        // show alert
        const isDelete = await Swal.fire({
            title: 'Are you sure?',
            text: 'You will not be able to recover this admin!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
        });

        // if confirmed true then delete the admin
        if (isDelete.isConfirmed === true && isDelete.value === true && adminId === Number(adminId)) {
            // console.log(adminId);
            const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/admin/users/${adminId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('superAdminAuthToken')}`
                }
            });

            const data = await response.json();
            // console.log(data);

            if (data.success === true) {
                Swal.fire({
                    title: 'Deleted!',
                    text: data.message || 'Admin deleted successfully',
                    icon: 'success',
                    confirmButtonText: 'OK'
                });
                getAllAdmins();
            } else {
                Swal.fire({
                    title: data.message || 'Error',
                    text: data.details || 'Error deleting admin',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        }
    }

    // task! get a list of all admins (get ?role_type=admin)

    // task!!! approve a admin (patch)
    return (
        <section className='p-6 text-gray-600 relative'>
            <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-800 mb-2">Manage Admins</h1>
                <p className="text-gray-600">View and manage system administrators</p>
            </div>

            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
                <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Admin Info
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Contact Details
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Role & Dates
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Status
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {adminsData.map((admin) => (
                                <tr key={admin.id} className="hover:bg-gray-50">
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex items-center">
                                            {admin.profile_image ? (
                                                <img
                                                    className="h-10 w-10 rounded-full"
                                                    src={admin.profile_image}
                                                    alt={admin.name}
                                                />
                                            ) : (
                                                <FaUserCircle className="h-10 w-10 text-gray-400" />
                                            )}
                                            <div className="ml-4">
                                                <div className="text-sm font-medium text-gray-900">
                                                    {admin.name}
                                                </div>
                                                <div className="text-sm text-gray-500 flex items-center">
                                                    <FaUserTag className="mr-1" />
                                                    {admin.username || 'No username'}
                                                </div>
                                                <div className="text-sm text-gray-500 flex items-center">
                                                    <FaIdCardAlt className="mr-1" />
                                                    ID: {admin.id || 'No id'}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-900 flex items-center mb-1">
                                            <FaEnvelope className="mr-2" />
                                            {admin.email}
                                        </div>
                                        <div className="text-sm text-gray-500 flex items-center mb-1">
                                            <FaPhone className="mr-2" />
                                            {admin.phone || 'No phone'}
                                        </div>
                                        <div className="text-sm text-gray-500 flex items-center">
                                            <FaMapMarkerAlt className="mr-2" />
                                            {admin.address || 'No address'}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-900 mb-1">{admin.role.name}</div>
                                        <div className="text-sm text-gray-500 flex items-center mb-1">
                                            <FaCalendarAlt className="mr-2" />
                                            Created: {formatDate(admin.created_at)}
                                        </div>
                                        <div className="text-sm text-gray-500 flex items-center">
                                            <FaClock className="mr-2" />
                                            Updated: {formatDate(admin.updated_at)}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <span className={`px-3 py-1 inline-flex items-center text-sm leading-5 font-semibold rounded-full ${admin.is_active
                                            ? 'bg-green-100 text-green-800'
                                            : 'bg-red-100 text-red-800'
                                            }`}>
                                            {admin.is_active ? (
                                                <><FaCheckCircle className="mr-1" /> Active</>
                                            ) : (
                                                <><FaTimesCircle className="mr-1" /> Inactive</>
                                            )}
                                        </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium gap-2">
                                        <Link href={`admins/${admin.id}`}><button className="text-orange-600 hover:text-orange-900 mr-3">
                                            <FaEye className='text-2xl' />
                                        </button>
                                        </Link>
                                        <Link href={`admins/${admin.id}/update`}><button className="text-blue-600 hover:text-blue-900 mr-3">
                                            <FaEdit className='text-2xl' />
                                        </button>
                                        </Link>
                                        <button
                                            className="text-red-600 hover:text-red-900"
                                            onClick={() => handleDeleteAdmin(admin.id)}
                                        >
                                            <FaTrashAlt className='text-2xl' />
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    )
}
