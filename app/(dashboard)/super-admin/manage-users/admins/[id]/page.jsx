"use client";

import { usePathname } from 'next/navigation'
import React, { useEffect, useState } from 'react'
import { FaUser, FaEnvelope, FaUserTag, FaPhone, FaMapMarkerAlt, FaCalendarAlt, FaUserShield, FaToggleOn, FaClock, FaCheckCircle } from 'react-icons/fa';

export default function page() {
    const [userData, setUserData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    const pathname = usePathname();
    const [, userType, , , id] = pathname.split('/');

    useEffect(() => {
        if (id) {
            const fetchUserData = async () => {
                try {
                    setLoading(true);
                    const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/admin/users/${id}`, {
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('superAdminAuthToken')}`,
                        },
                    });

                    const data = await response.json();
                    console.log(data);

                    if (data.success === true && data.data.user.id) {
                        setUserData(data);
                        setError(null);
                    }


                } catch (err) {
                    console.error("Error fetching user data:", err);
                    setError(err.message);
                } finally {
                    setLoading(false);
                }
            };

            fetchUserData();
        }
    }, [id]); // Added id as dependency

    if (loading) {
        return (
            <section className="flex items-center justify-center min-h-screen">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
            </section>
        );
    }

    if (error) {
        return (
            <section className="flex items-center justify-center min-h-screen text-red-600">
                <div className="text-center">
                    <h2 className="text-2xl font-bold mb-2">Error Loading Data</h2>
                    <p>{error}</p>
                </div>
            </section>
        );
    }

    return (
        <section className='p-6 text-gray-600'>
            {userData ? (
                <div className="max-w-4xl mx-auto space-y-8">
                    <div className="flex items-center justify-between">
                        <h1 className="text-3xl font-bold text-gray-800">User Details</h1>
                        <span className={`px-4 py-2 rounded-full ${userData.data.user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                            {userData.data.user.is_active ? 'Active' : 'Inactive'}
                        </span>
                    </div>

                    {/* Basic Information */}
                    <div className="bg-white rounded-xl shadow-lg p-6 space-y-6">
                        <h2 className="text-xl font-semibold text-gray-800 border-b pb-2">Basic Information</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="flex items-center space-x-3">
                                <FaUser className="text-blue-500 w-5 h-5" />
                                <div>
                                    <p className="text-sm text-gray-500">Name</p>
                                    <p className="font-medium">{userData.data.user.name}</p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-3">
                                <FaEnvelope className="text-purple-500 w-5 h-5" />
                                <div>
                                    <p className="text-sm text-gray-500">Email</p>
                                    <p className="font-medium">{userData.data.user.email}</p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-3">
                                <FaUserTag className="text-green-500 w-5 h-5" />
                                <div>
                                    <p className="text-sm text-gray-500">Username</p>
                                    <p className="font-medium">{userData.data.user.username || 'Not set'}</p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-3">
                                <FaUserShield className="text-indigo-500 w-5 h-5" />
                                <div>
                                    <p className="text-sm text-gray-500">Role</p>
                                    <p className="font-medium">{userData.data.user.role.name}</p>
                                    <p className="text-xs text-gray-400">{userData.data.user.role.description}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Contact Information */}
                    <div className="bg-white rounded-xl shadow-lg p-6 space-y-6">
                        <h2 className="text-xl font-semibold text-gray-800 border-b pb-2">Contact Information</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="flex items-center space-x-3">
                                <FaPhone className="text-emerald-500 w-5 h-5" />
                                <div>
                                    <p className="text-sm text-gray-500">Phone</p>
                                    <p className="font-medium">{userData.data.user.phone || 'Not provided'}</p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-3">
                                <FaMapMarkerAlt className="text-red-500 w-5 h-5" />
                                <div>
                                    <p className="text-sm text-gray-500">Address</p>
                                    <p className="font-medium">{userData.data.user.address || 'Not provided'}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* System Information */}
                    <div className="bg-white rounded-xl shadow-lg p-6 space-y-6">
                        <h2 className="text-xl font-semibold text-gray-800 border-b pb-2">System Information</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="flex items-center space-x-3">
                                <FaCalendarAlt className="text-amber-500 w-5 h-5" />
                                <div>
                                    <p className="text-sm text-gray-500">Created At</p>
                                    <p className="font-medium">{new Date(userData.data.user.created_at).toLocaleDateString()}</p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-3">
                                <FaClock className="text-cyan-500 w-5 h-5" />
                                <div>
                                    <p className="text-sm text-gray-500">Last Updated</p>
                                    <p className="font-medium">{new Date(userData.data.user.updated_at).toLocaleDateString()}</p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-3">
                                <FaCheckCircle className="text-teal-500 w-5 h-5" />
                                <div>
                                    <p className="text-sm text-gray-500">Email Verification</p>
                                    <p className="font-medium">{userData.data.user.email_verified_at ? 'Verified' : 'Not verified'}</p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-3">
                                <FaUserShield className="text-pink-500 w-5 h-5" />
                                <div>
                                    <p className="text-sm text-gray-500">Role ID</p>
                                    <p className="font-medium">{userData.data.user.role_id}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            ) : (
                <div className="text-center text-gray-500">
                    No user data available
                </div>
            )}
        </section>
    );
}
