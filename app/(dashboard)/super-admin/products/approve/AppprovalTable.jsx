"use client";

import React, { useEffect, useState } from 'react'
import {
    PencilIcon, TrashIcon, EyeIcon,
    ChevronUpIcon, ChevronDownIcon,
    MagnifyingGlassIcon, AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import Swal from 'sweetalert2';

export default function AppprovalTable({ data }) {
    // console.log(data); // add it for ssr


    const [products, setProducts] = useState([]);
    // const [products, setProducts] = useState(data?.products.data);
    const [filteredProducts, setFilteredProducts] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [sortConfig, setSortConfig] = useState({ key: 'name', direction: 'ascending' });
    const [selectedCategory, setSelectedCategory] = useState('');
    const [selectedStatus, setSelectedStatus] = useState('');
    const [selectedVerify, setSelectedVerify] = useState('');
    const [showFilters, setShowFilters] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [selectedProducts, setSelectedProducts] = useState([]);
    const productsPerPage = 5;


    const getProdcuts = async function () {
        fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/superadmin/products`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('superAdminAuthToken')}`
            },
        })
            .then(response => response.json())
            .then(data => {
                console.log(data);
                setProducts(data?.data.products);
            })
            .catch(error => {
                console.error('Error fetching products:', error);
            });
    }

    useEffect(() => {
        getProdcuts();
    }, []); // Empty dependency array for initial load only

    useEffect(() => {
        let results = [...products];

        // Filter by search term
        if (searchTerm) {
            results = results.filter(product =>
                product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
                product.category.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        // Filter by category
        if (selectedCategory) {
            results = results.filter(product => product.category === selectedCategory);
        }

        // Filter by status
        if (selectedStatus) {
            results = results.filter(product => product.status === selectedStatus);
        }

        // Filter by verify
        if (selectedVerify) {
            results = results.filter(product => product.verify === selectedVerify);
        }

        // Sort the products
        if (sortConfig.key) {
            results.sort((a, b) => {
                if (a[sortConfig.key] < b[sortConfig.key]) {
                    return sortConfig.direction === 'ascending' ? -1 : 1;
                }
                if (a[sortConfig.key] > b[sortConfig.key]) {
                    return sortConfig.direction === 'ascending' ? 1 : -1;
                }
                return 0;
            });
        }

        setFilteredProducts(results);
    }, [products, searchTerm, sortConfig, selectedCategory, selectedStatus, selectedVerify]);
    // console.log(products);


    const handleSort = (key) => {
        let direction = 'ascending';

        if (sortConfig.key === key && sortConfig.direction === 'ascending') {
            direction = 'descending';
        }

        setSortConfig({ key, direction });
    };

    const getSortIcon = (columnName) => {
        if (sortConfig.key !== columnName) {
            return null;
        }

        return sortConfig.direction === 'ascending'
            ? <ChevronUpIcon className="h-4 w-4 inline-block ml-1" />
            : <ChevronDownIcon className="h-4 w-4 inline-block ml-1" />;
    };

    const handleDelete = (id) => {
        if (confirm('Are you sure you want to delete this product?')) {
            setProducts(products.filter(product => product.id !== id));
        }
    };

    const handleBulkDelete = () => {
        if (selectedProducts.length === 0) {
            alert('Please select at least one product to delete.');
            return;
        }

        if (confirm(`Are you sure you want to delete ${selectedProducts.length} selected products?`)) {
            setProducts(products.filter(product => !selectedProducts.includes(product.id)));
            setSelectedProducts([]);
        }
    };

    async function handleStatusChange(id, newStatus) {
        // console.log(id, newStatus);

        const payload = {
            status: newStatus
        };

        if (newStatus === "rejected") {
            const rejectNote = await Swal.fire({
                title: "Submit Rejection Reason",
                input: "text",
                inputAttributes: {
                    autocapitalize: "off"
                },
                showCancelButton: true,
                confirmButtonText: "Submit",
                showLoaderOnConfirm: true,

            });
            console.log(rejectNote);

            payload.rejection_reason = rejectNote.value;
        }

        // task!!! toggle approval (patch) | review (405 (Method Not Allowed))
        const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/admin/products/${id}/toggle-approval`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('superAdminAuthToken')}`
            },
            body: JSON.stringify(payload)
        });

        const data = await response.json();
        console.log(data);

        if (data.success === true) {
            setProducts(products.map(product =>
                product.id === id ? { ...product, approval_status: newStatus } : product
            ));

            Swal.fire({
                title: "Success",
                text: data.message || "Product status has been updated",
                icon: "success",
                timer: 1000,
                showConfirmButton: false
            });
        } else {
            Swal.fire({
                title: data.message || "Error",
                text: data.details || "Failed to update product status",
                icon: "error",
                timer: 10000,
                showConfirmButton: true
            })
        }

    };

    const toggleSelectAll = (e) => {
        if (e.target.checked) {
            const currentPageIds = getCurrentPageProducts().map(product => product.id);
            setSelectedProducts(currentPageIds);
        } else {
            setSelectedProducts([]);
        }
    };

    const toggleSelectProduct = (id) => {
        setSelectedProducts(prevSelected => {
            if (prevSelected.includes(id)) {
                return prevSelected.filter(productId => productId !== id);
            } else {
                return [...prevSelected, id];
            }
        });
    };

    // Pagination
    const indexOfLastProduct = currentPage * productsPerPage;
    const indexOfFirstProduct = indexOfLastProduct - productsPerPage;

    const getCurrentPageProducts = () => {
        return filteredProducts.slice(indexOfFirstProduct, indexOfLastProduct);
    };

    const totalPages = Math.ceil(filteredProducts.length / productsPerPage);

    const paginate = (pageNumber) => setCurrentPage(pageNumber);

    // task!!! toggle approval (patch) | review
    console.log(products);

    // Get unique categories for filter
    const categories = [...new Set(products.map(product => product.category))];
    return (
        <section className='text-gray-600'>
            {data && data.length > 0 && data.map((product, index) => <li key={index}>{product.name}</li>)}
            <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
                <h1 className="text-2xl font-semibold mb-4 md:mb-0 text-gray-800">Approve Products</h1>
                {/* Multiple delete button */}
                <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-2">
                    {selectedProducts.length > 0 && (
                        <button
                            onClick={handleBulkDelete}
                            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center justify-center"
                        >
                            <span>Delete Selected ({selectedProducts.length})</span>
                        </button>
                    )}
                </div>
            </div>

            <div className="bg-white rounded-lg shadow overflow-hidden mb-6">
                <div className="p-4 border-b border-gray-200">
                    <div className="flex flex-col md:flex-row md:items-center justify-between space-y-4 md:space-y-0">
                        <div className="relative w-full md:w-64">
                            <input
                                type="text"
                                placeholder="Search products..."
                                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full text-gray-900 bg-white"
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                            />
                            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-2.5" />
                        </div>

                        <button
                            className="flex items-center text-gray-700 px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                            onClick={() => setShowFilters(!showFilters)}
                        >
                            <AdjustmentsHorizontalIcon className="h-5 w-5 mr-2" />
                            Filters
                        </button>
                    </div>

                    {showFilters && (
                        <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label htmlFor="categoryFilter" className="block text-sm font-medium text-gray-700 mb-1">
                                    Category
                                </label>
                                <select
                                    id="categoryFilter"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900"
                                    value={selectedCategory}
                                    onChange={(e) => setSelectedCategory(e.target.value)}
                                >
                                    <option value="">All Categories</option>
                                    {categories.map(category => (
                                        <option key={category} value={category}>{category}</option>
                                    ))}
                                </select>
                            </div>

                            <div>
                                <label htmlFor="statusFilter" className="block text-sm font-medium text-gray-700 mb-1">
                                    Status
                                </label>
                                <select
                                    id="statusFilter"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900"
                                    value={selectedStatus}
                                    onChange={(e) => setSelectedStatus(e.target.value)}
                                >
                                    <option value="">All Statuses</option>
                                    <option value="active">Active</option>
                                    <option value="draft">Draft</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>

                            <div>
                                <label htmlFor="verifyFilter" className="block text-sm font-medium text-gray-700 mb-1">
                                    Verify
                                </label>
                                <select
                                    id="verifyFilter"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900"
                                    value={selectedVerify}
                                    onChange={(e) => setSelectedVerify(e.target.value)}
                                >
                                    <option value="">All Statuses</option>
                                    <option value="approved">Approved</option>
                                    <option value="pending">Pending</option>
                                    <option value="rejected">Rejected</option>
                                </select>
                            </div>

                            <div className="flex items-end">
                                <button
                                    className="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                                    onClick={() => {
                                        setSelectedCategory('');
                                        setSelectedStatus('');
                                        setSelectedVerify('');
                                        setSearchTerm('');
                                    }}
                                >
                                    Clear Filters
                                </button>
                            </div>
                        </div>
                    )}
                </div>

                <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-4 py-3 text-left">
                                    <input
                                        type="checkbox"
                                        className="h-4 w-4 text-orange-500  focus:ring-orange-1000 border-gray-300 rounded"
                                        onChange={toggleSelectAll}
                                        checked={selectedProducts.length === getCurrentPageProducts().length && getCurrentPageProducts().length > 0}
                                    />
                                </th>
                                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <button
                                        className="flex items-center font-medium text-gray-500 uppercase tracking-wider"
                                        onClick={() => handleSort('name')}
                                    >
                                        Product
                                        {getSortIcon('name')}
                                    </button>
                                </th>
                                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <button
                                        className="flex items-center font-medium text-gray-500 uppercase tracking-wider"
                                        onClick={() => handleSort('category')}
                                    >
                                        Category
                                        {getSortIcon('category')}
                                    </button>
                                </th>
                                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <button
                                        className="flex items-center font-medium text-gray-500 uppercase tracking-wider"
                                        onClick={() => handleSort('brand')}
                                    >
                                        Brand
                                        {getSortIcon('Brand')}
                                    </button>
                                </th>
                                {/* <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <button
                                        className="flex items-center justify-end font-medium text-gray-500 uppercase tracking-wider"
                                        onClick={() => handleSort('price')}
                                    >
                                        Price
                                        {getSortIcon('price')}
                                    </button>
                                </th>
                                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <button
                                        className="flex items-center justify-end font-medium text-gray-500 uppercase tracking-wider"
                                        onClick={() => handleSort('stock')}
                                    >
                                        Stock
                                        {getSortIcon('stock')}
                                    </button>
                                </th>
                                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Status
                                </th>
                                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Approval Status
                                </th>
                                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Approve Status
                                </th>
                                {/* <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th> */}
                            </tr >
                        </thead >
                        <tbody className="bg-white divide-y divide-gray-200">
                            {getCurrentPageProducts().length > 0 ? (
                                getCurrentPageProducts().map((product) => (
                                    <tr key={product.id} className="hover:bg-gray-50">

                                        <td className="px-4 py-4 whitespace-nowrap">
                                            <input
                                                type="checkbox"
                                                className="h-4 w-4 text-orange-500  focus:ring-orange-1000 border-gray-300 rounded"
                                                checked={selectedProducts.includes(product.id)}
                                                onChange={() => toggleSelectProduct(product.id)}
                                            />
                                        </td>

                                        <td className="px-4 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                                <div className="h-10 w-10 flex-shrink-0 mr-3">
                                                    <img
                                                        className="h-10 w-10 rounded-md object-cover"
                                                        src={product.imageUrl}
                                                        alt={product.name}
                                                    />
                                                </div>
                                                <div>
                                                    <div className="font-medium text-gray-900">{product.name}</div>
                                                    <div className="text-gray-500 text-sm">SKU: {product.sku}</div>
                                                    <div className="text-gray-500 text-sm">ID: {product.id}</div>
                                                </div >
                                                {
                                                    product.featured && (
                                                        <span className="ml-2 px-2 py-0.5 text-xs bg-indigo-100 text-orange-600  rounded-full">
                                                            Featured
                                                        </span>
                                                    )
                                                }
                                            </div >
                                        </td >

                                        <td className="px-4 py-4 whitespace-nowrap">
                                            <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                                {product?.categories.map(category => category.name).join(', ')}
                                            </span>
                                        </td>

                                        <td className="px-4 py-4 whitespace-nowrap">
                                            <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                                {product?.brand?.name}
                                            </span>
                                        </td>
                                        <td className="px-4 py-4 whitespace-nowrap flex flex-col">
                                            <p>{product.price} (regular)</p>
                                            <p>{product.special_price} (special)</p>
                                        </td>
                                        <td className="px-4 py-4 whitespace-nowrap text-right">
                                            <span className={`font-medium ${product.stock < 10 ? 'text-red-600' : 'text-gray-900'}`}>
                                                {product.quantity}
                                            </span>
                                        </td>
                                        <td className="px-4 py-4 whitespace-nowrap">
                                            <select
                                                value={product.status}
                                                onChange={(e) => handleStatusChange(product.id, e.target.value)}
                                                className={`text-sm rounded-full px-2 py-1 border ${product.status === 'active' ? 'bg-green-100 text-green-800 border-green-200' :
                                                    product.status === 'inactive' ? 'bg-red-100 text-red-800 border-red-200' :
                                                        'bg-gray-100 text-gray-800 border-gray-200'
                                                    }`}
                                            >
                                                <option value="active">Active</option>
                                                <option value="inactive">Inactive</option>
                                                <option value="draft">Draft</option>
                                            </select>
                                        </td>

                                        <td className="px-4 py-4 whitespace-nowrap">
                                            <select
                                                value={product?.approval_status}
                                                onChange={(e) => handleStatusChange(product.id, e.target.value)}
                                                className={`text-sm rounded-full px-2 py-1 border ${product.approval_status === 'approved' ? 'bg-green-100 text-green-800 border-green-200' :
                                                    product.approval_status === 'rejected' ? 'bg-red-100 text-red-800 border-red-200' :
                                                        'bg-gray-100 text-gray-800 border-gray-200'
                                                    }`}
                                            >
                                                <option value="approved">Approved</option>
                                                <option value="rejected">Rejected</option>
                                                <option value="pending">Pending</option>
                                            </select>
                                        </td>

                                        {/* <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div className="flex justify-end space-x-2">
                                                <button
                                                    title="View product"
                                                    className="text-gray-500 hover:text-gray-700"
                                                >
                                                    <Link href={`/super-admin/products/${product.id}`}>
                                                        <EyeIcon className="h-5 w-5" />
                                                    </Link>
                                                </button>
                                                <button
                                                    title="Edit product"
                                                    className="text-orange-500  hover:text-orange-600 "
                                                >
                                                    <PencilIcon className="h-5 w-5" />
                                                </button>
                                                <button
                                                    title="Delete product"
                                                    onClick={() => handleDelete(product.id)}
                                                    className="text-red-600 hover:text-red-900"
                                                >
                                                    <TrashIcon className="h-5 w-5" />
                                                </button>
                                            </div>
                                        </td> */}
                                    </tr >
                                ))
                            ) : (
                                <tr>
                                    <td colSpan="7" className="px-4 py-6 text-center text-gray-500">
                                        No products found matching your criteria
                                    </td>
                                </tr>
                            )
                            }
                        </tbody >
                    </table >
                </div >

                {totalPages > 1 && (
                    <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200 bg-gray-50">
                        <div className="flex-1 flex justify-between">
                            <button
                                onClick={() => paginate(currentPage - 1)}
                                disabled={currentPage === 1}
                                className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${currentPage === 1
                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                    : 'bg-white text-gray-700 hover:bg-gray-50'
                                    }`}
                            >
                                Previous
                            </button>
                            <div className="hidden md:flex">
                                {[...Array(totalPages)].map((_, i) => (
                                    <button
                                        key={i}
                                        onClick={() => paginate(i + 1)}
                                        className={`relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium ${currentPage === i + 1
                                            ? 'bg-orange-400 text-white border-orange-400'
                                            : 'text-gray-700 hover:bg-gray-50'
                                            } mx-1 rounded-md`}
                                    >
                                        {i + 1}
                                    </button>
                                ))}
                            </div>
                            <button
                                onClick={() => paginate(currentPage + 1)}
                                disabled={currentPage === totalPages}
                                className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${currentPage === totalPages
                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                    : 'bg-white text-gray-700 hover:bg-gray-50'
                                    }`}
                            >
                                Next
                            </button>
                        </div>
                    </div>
                )}
            </div >

            <div className="text-sm text-gray-500 mt-4">
                Showing {filteredProducts.length > 0 ? indexOfFirstProduct + 1 : 0} to {Math.min(indexOfLastProduct, filteredProducts.length)} of {filteredProducts.length} products
            </div>
        </section >
    )
}
