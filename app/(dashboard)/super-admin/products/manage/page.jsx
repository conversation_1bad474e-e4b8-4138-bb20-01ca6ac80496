import ManageProducts from '@/app/components/Products/ManageProducts'
// import webServer from '@/src/server/web/webServer';
import React from 'react'

// export const getProcuts = await webServer.products.getProducts();

export default function MangePage() {
    // const { data, meta } = getProcuts;

    return (
        // <ManageProducts data={data} meta={meta} isSuperAdmin={true} isPartnerAdmin={false} />
        <ManageProducts />
    )
}
