"use client";

import { useState } from "react";
import { motion } from "framer-motion";

// Partner list
const partners = [
    { id: 1, name: "Samsung", logo: "/logos/samsung-logo.png" },
    { id: 2, name: "<PERSON>", logo: "/logos/apple-logo.png" },
    { id: 3, name: "Sony", logo: "/logos/sony-logo.png" },
    { id: 4, name: "<PERSON>", logo: "/logos/dell-logo.png" },
    { id: 5, name: "HP", logo: "/logos/hp-logo.png" },
    { id: 6, name: "Microsoft", logo: "/logos/microsoft-logo.png" },
    { id: 7, name: "Google", logo: "/logos/google-logo.png" },
    { id: 8, name: "Intel", logo: "/logos/intel-logo.png" },
    { id: 9, name: "Len<PERSON>", logo: "/logos/lenovo-logo.png" },
    { id: 10, name: "<PERSON><PERSON>", logo: "/logos/asus-logo.png" },
];

// Product list (separate from partner)
const productList = [
    {
        id: 101, name: "Galaxy S22", prices: {
            pack: { numberOfProducts: 12, price: 120, margin: 5 },
            bulk: [
                { numberOfPacks: 10, perPackPrice: 115, margin: 4 },
                { numberOfPacks: 20, perPackPrice: 110, margin: 3 },
                { numberOfPacks: 30, perPackPrice: 105, margin: 2 },
                { numberOfPacks: 40, perPackPrice: 100, margin: 1 },
            ],
        }
    },
    {
        id: 102, name: "iPhone 14", prices: {
            pack: { numberOfProducts: 12, price: 120, margin: 5 },
            bulk: [
                { numberOfPacks: 12, perPackPrice: 120, margin: 5 },
                { numberOfPacks: 48, perPackPrice: 480, margin: 5 },
            ],
        }
    },
    {
        id: 103, name: "PlayStation 5", prices: {
            pack: { numberOfProducts: 12, price: 120, margin: 5 },
            bulk: [
                { numberOfPacks: 24, perPackPrice: 240, margin: 4 },
                { numberOfPacks: 36, perPackPrice: 360, margin: 5 },
                { numberOfPacks: 48, perPackPrice: 480, margin: 5 },
            ],
        }
    },
    {
        id: 104, name: "XPS 13", prices: {
            pack: { numberOfProducts: 12, price: 120, margin: 5 },
            bulk: [
                { numberOfPacks: 12, perPackPrice: 120, margin: 5 },
                { numberOfPacks: 24, perPackPrice: 240, margin: 1 },
                { numberOfPacks: 36, perPackPrice: 360, margin: 5 },
            ],
        }
    },
    {
        id: 105, name: "Pavilion Desktop", prices: {
            pack: { numberOfProducts: 12, price: 120, margin: 5 },
            bulk: [
                { numberOfPacks: 12, perPackPrice: 120, margin: 5 },
                { numberOfPacks: 24, perPackPrice: 240, margin: 7 },
            ],
        }
    },
    {
        id: 106, name: "MacBook Pro", prices: {
            pack: { numberOfProducts: 12, price: 120, margin: 5 },
            bulk: [
                { numberOfPacks: 12, perPackPrice: 120, margin: 9 },
            ],
        }
    },
    {
        id: 107, name: "MacBook Air", prices: {
            pack: { numberOfProducts: 12, price: 120, margin: 5 },
            bulk: [],
        }
    },
    {
        id: 108, name: "MacBook Pro", prices: {
            pack: { numberOfProducts: 12, price: 120, margin: 5 },
            bulk: [
                { numberOfPacks: 12, perPackPrice: 120, margin: 5 },
                { numberOfPacks: 36, perPackPrice: 360, margin: 5 },
            ],
        }
    },
    {
        id: 109, name: "MacBook Air", prices: {
            pack: { numberOfProducts: 12, price: 120, margin: 5 },
            bulk: [
                { numberOfPacks: 12, perPackPrice: 120, margin: 5 },
                { numberOfPacks: 24, perPackPrice: 240, margin: 2 },
                { numberOfPacks: 36, perPackPrice: 360, margin: 5 },
                { numberOfPacks: 48, perPackPrice: 480, margin: 2 },
            ],
        }
    },
    {
        id: 110, name: "MacBook Pro", prices: {
            pack: { numberOfProducts: 12, price: 120, margin: 5 },
            bulk: [
                { numberOfPacks: 12, perPackPrice: 120, margin: 5 },
                { numberOfPacks: 24, perPackPrice: 240, margin: 5 },
            ],
        }
    },
    {
        id: 111, name: "MacBook Air", prices: {
            pack: { numberOfProducts: 12, price: 120, margin: 5 },
            bulk: [
                { numberOfPacks: 12, perPackPrice: 120, margin: 3 },
                { numberOfPacks: 24, perPackPrice: 240, margin: 5 },
                { numberOfPacks: 36, perPackPrice: 360, margin: 8 },
                { numberOfPacks: 48, perPackPrice: 480, margin: 5 },
            ],
        }
    },
];

// Build merged combinations: each product with every partner
const dummyProducts = productList.flatMap((product) =>
    partners.map((partner) => ({
        ...product,
        partnerId: partner.id,
        partnerName: partner.name,
        logo: partner.logo,
    }))
);

export default function BulkOrdersPage() {
    const [searchQuery, setSearchQuery] = useState("");
    const [products] = useState(dummyProducts);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [priceData, setPriceData] = useState([]);

    const filteredProducts = products.filter(
        (item) =>
            item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            item.id.toString().includes(searchQuery)
    );

    const handleSelectProduct = (product) => {
        setSelectedProduct(pre => structuredClone(product));
        const prices = structuredClone(product.prices);
        setPriceData(prices);
    };

    const handleMarginChange = (e, index = null) => {
        const newMargin = parseFloat(e.target.value) || 0;
        setPriceData(prev => {
            if (index === null) {
                return {
                    ...prev,
                    pack: { ...prev.pack, margin: newMargin },
                };
            } else {
                const updatedBulk = [...prev.bulk];
                updatedBulk[index].margin = newMargin;
                return { ...prev, bulk: updatedBulk };
            }
        });
    };

    const calculateFinalSinglePackPrice = (base = 0, margin = 0) => {
        return (parseFloat(base) + (parseFloat(base) * parseFloat(margin)) / 100).toFixed(2);
    };

    function calculateFinalBulkPrice(base = 0, numberOfPacks = 0, margin = 0,) {
        return (parseFloat(base) * parseFloat(numberOfPacks) + (parseFloat(base) * parseFloat(numberOfPacks) * parseFloat(margin)) / 100).toFixed(2);
    }

    const handleSave = () => {
        console.log("Updated Price Data:", priceData);
        alert("Saved! Check the console.", priceData);
    };

    const handleResetAll = () => {
        setSelectedProduct(null);
        setSearchQuery("");
    };

    return (
        <div className="p-5 space-y-5 text-gray-800">
            {/* Product Search */}
            <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white p-5 rounded-xl shadow-md"
            >
                <h2 className="text-xl font-bold mb-4">Search Products</h2>
                <input
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search by name or ID"
                    className="w-full px-4 py-2 border rounded-md"
                />
                <div className="mt-4 max-h-64 overflow-y-auto space-y-2 min-h-[50dvh]">
                    {filteredProducts.map((item) => (
                        <motion.div
                            key={`${item.id}-${item.partnerId}`}
                            whileHover={{ scale: 1.02 }}
                            className="flex justify-between items-center p-3 bg-gray-100 rounded cursor-pointer mx-3"
                            onClick={() => handleSelectProduct(item)}
                        >
                            <div>
                                <p className="font-semibold">{item.name}</p>
                                <p className="text-sm text-gray-500">{item.partnerName}</p>
                            </div>
                            {/* <img src={item.logo} alt="Logo" className="w-6 h-6" /> */}
                        </motion.div>
                    ))}
                </div>
            </motion.div>

            {/* Margin Section */}
            {selectedProduct && (
                <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-white p-6 rounded-xl shadow-md"
                >
                    <h2 className="text-xl font-bold mb-4">
                        Set Margins for {selectedProduct.name} ({selectedProduct.partnerName})
                    </h2>

                    {/* Price Preview */}
                    <div className="mb-4 bg-orange-50 p-3 rounded text-md">
                        <div className="overflow-x-auto shadow-md">
                            <table className="min-w-full text-sm text-left text-gray-700 border border-gray-200">
                                <thead className="bg-gray-100 text-gray-800 uppercase text-xs">
                                    <tr>
                                        <th className="px-4 py-3 border">No. of Packs</th>
                                        <th className="px-4 py-3 border">No. of Products</th>
                                        <th className="px-4 py-3 border text-right">Per Pack Price</th>
                                        <th className="px-4 py-3 border text-right">Current Margin</th>
                                        <th className="px-4 py-3 border text-right">Current Total Amount</th>
                                        <th className="px-4 py-3 border text-right">Updated Margin</th>
                                        <th className="px-4 py-3 border text-right">Updated Total Amount</th>
                                    </tr>
                                </thead>
                                <tbody className="divide-y divide-gray-200">
                                    <tr className="bg-white hover:bg-blue-50 transition">
                                        <td className="px-4 py-3">
                                            1(one pack)
                                        </td>
                                        <td className="px-4 py-3">
                                            {selectedProduct.prices.pack.numberOfProducts}
                                        </td>
                                        <td className="px-4 py-3 text-right">
                                            ${selectedProduct.prices.pack.price}
                                        </td>
                                        <td className="px-4 py-3 text-right">
                                            {selectedProduct.prices.pack.margin}%
                                        </td>
                                        <td className="px-4 py-3 text-right">
                                            ${selectedProduct.prices.pack.price}
                                        </td>
                                        <td className="px-4 py-3 text-right">
                                            {priceData.pack.margin}%
                                        </td>
                                        <td className="px-4 py-3 text-right">
                                            ${calculateFinalSinglePackPrice(selectedProduct.prices.pack.price, priceData.pack.margin)}
                                        </td>
                                    </tr>
                                    {selectedProduct.prices.bulk.map((item, index) => (
                                        <tr
                                            key={index}
                                            className={`${index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
                                                } hover:bg-blue-50 transition`}
                                        >
                                            <td className="px-4 py-3">
                                                {item.numberOfPacks} packs
                                            </td>
                                            <td className="px-4 py-3">
                                                {selectedProduct.prices.pack.numberOfProducts * item.numberOfPacks}
                                            </td>
                                            <td className="px-4 py-3 text-right">
                                                ${item.perPackPrice}
                                            </td>
                                            <td className="px-4 py-3 text-right">
                                                {selectedProduct.prices.bulk[index].margin}%
                                            </td>
                                            <td className="px-4 py-3 text-right">
                                                ${calculateFinalBulkPrice(item.perPackPrice, item.numberOfPacks, selectedProduct.prices.bulk[index].margin)}
                                            </td>
                                            <td className="px-4 py-3 text-right">
                                                {priceData.bulk[index].margin}%
                                            </td>
                                            <td className="px-4 py-3 text-right">${calculateFinalBulkPrice(item.perPackPrice, item.numberOfPacks, priceData.bulk[index].margin)}
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    {/* Pack margin editing Section */}
                    <div className="p-4 sm:p-6 max-w-4xl mx-auto space-y-6">
                        <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="bg-white shadow-md rounded-2xl p-4 border"
                        >
                            <h2 className="text-xl font-semibold mb-4">Pack Pricing</h2>
                            <div className="grid sm:grid-cols-3 gap-4 text-sm">
                                <div>
                                    <span className="text-gray-600">Number of Pack:</span><br />
                                    1
                                </div>
                                <div>
                                    <span className="text-gray-600">Number of Products:</span><br />
                                    {priceData.pack.numberOfProducts}
                                </div>
                                <div>
                                    <span className="text-gray-600">Base Pack Price:</span><br />
                                    ${priceData.pack.price}
                                </div>
                                <div>
                                    <span className="text-gray-600">Current margin:</span><br />
                                    {selectedProduct.prices.pack.margin}%
                                </div>
                                <div>
                                    <span>Current Total Price:</span><br />
                                    ${calculateFinalSinglePackPrice(selectedProduct.prices.pack.price, selectedProduct.prices.pack.margin)}
                                </div>
                                <br />
                                <div>
                                    <label className="text-gray-600 block mb-1">Margin (%)</label>
                                    <input
                                        type="number"
                                        value={priceData.pack.margin}
                                        onChange={e => handleMarginChange(e)}
                                        className="w-full p-2 border rounded-xl focus:outline-none focus:ring"
                                    />
                                </div>
                                <div className="sm:col-span-3 text-green-600 font-medium">
                                    Final Single Pack Price: ${calculateFinalSinglePackPrice(priceData.pack.price, priceData.pack.margin)}
                                </div>
                                <div className="sm:col-span-3 text-green-600 font-medium">
                                    Final All Pack Total Price: ${calculateFinalSinglePackPrice(priceData.pack.price, priceData.pack.margin)}
                                </div>
                            </div>
                        </motion.div>

                        {/* Bulk Section */}
                        <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.1 }}
                            className="bg-white shadow-md rounded-2xl p-4 border"
                        >
                            <h2 className="text-xl font-semibold mb-4">Bulk Pricing</h2>
                            <div className="grid gap-4">
                                {priceData.bulk.map((item, index) => (
                                    <motion.div
                                        key={index}
                                        whileHover={{ scale: 1.01 }}
                                        className="bg-gray-50 p-4 rounded-xl border"
                                    >
                                        <div className="grid sm:grid-cols-3 gap-4 text-sm mb-3">
                                            <div>
                                                <span className="text-gray-600">Number of Packs:</span><br />
                                                {item.numberOfPacks}
                                            </div>
                                            <div>
                                                <span className="text-gray-600">Number of Products:</span><br />
                                                {item.numberOfPacks * selectedProduct?.prices?.pack?.numberOfProducts}
                                            </div>
                                            <div>
                                                <span className="text-gray-600">Per Pack Price:</span><br />
                                                ${item.perPackPrice}
                                            </div>
                                            <div>
                                                <span className="text-gray-600">Current margin:</span><br />
                                                {selectedProduct.prices.bulk[index].margin}%
                                            </div>
                                            <div>
                                                <span className="text-gray-600">Current Total Price:</span><br />
                                                ${calculateFinalBulkPrice(item.perPackPrice, item.numberOfPacks, selectedProduct.prices.bulk[index].margin)}
                                            </div>
                                            <br />
                                            <div>
                                                <label className="text-gray-600 block mb-1">Margin (%)</label>
                                                <input
                                                    type="number"
                                                    value={item.margin}
                                                    onChange={e => handleMarginChange(e, index)}
                                                    className="w-full p-2 border rounded-xl focus:outline-none focus:ring"
                                                />
                                            </div>
                                        </div>
                                        <div className="text-green-600 font-medium">
                                            Final Single Pack Price: ${calculateFinalSinglePackPrice(item.perPackPrice, item.margin)}
                                        </div>
                                        <div className="text-green-600 font-medium">
                                            Final All Pack Total Price: ${calculateFinalBulkPrice(item.perPackPrice, item.numberOfPacks, item.margin)}
                                        </div>
                                    </motion.div>
                                ))}
                            </div>
                        </motion.div>

                        {/* Save and Reset Button */}
                        <div className="text-right space-x-5">
                            <motion.button
                                whileTap={{ scale: 0.95 }}
                                onClick={handleSave}
                                className="bg-orange-400 text-white px-6 py-2 rounded-lg shadow hover:bg-orange-500 transition"
                            >
                                Save
                            </motion.button>
                            <motion.button
                                whileTap={{ scale: 0.95 }}
                                onClick={handleResetAll}
                                className="bg-gray-400 hover:bg-gray-500 text-white px-6 py-2 rounded-md"
                            >
                                Reset
                            </motion.button>
                        </div>
                    </div>
                </motion.div>
            )}
        </div>
    );
};
