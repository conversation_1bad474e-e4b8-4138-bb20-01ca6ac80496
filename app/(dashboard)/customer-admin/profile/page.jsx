'use client';

import { motion } from 'framer-motion';
import {
  UserIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

export default function CustomerProfile() {
  return (
    <div className="p-6 max-w-4xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Profile</h1>
          <p className="text-gray-600">Manage your personal information and account details</p>
        </div>

        {/* Under Development Notice */}
        <div className="bg-gradient-to-r from-orange-50 to-yellow-50 border-l-4 border-orange-400 rounded-xl p-8 shadow-sm">
          <div className="flex items-center justify-center mb-6">
            <div className="bg-orange-100 rounded-full p-4 mr-4">
              <UserIcon className="h-12 w-12 text-orange-600" />
            </div>
            <div className="bg-yellow-100 rounded-full p-4">
              <ExclamationTriangleIcon className="h-12 w-12 text-yellow-600" />
            </div>
          </div>

          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              🚧 Page Under Development 🚧
            </h2>
            <p className="text-lg text-gray-700 mb-6">
              We're working hard to bring you an amazing profile experience! This page is currently under development and will be available soon.
            </p>
            <div className="bg-white rounded-lg p-6 border border-orange-200 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Coming Soon:</h3>
              <ul className="text-left text-gray-600 space-y-2">
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-orange-400 rounded-full mr-3"></span>
                  Personal information management
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-orange-400 rounded-full mr-3"></span>
                  Company profile setup
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-orange-400 rounded-full mr-3"></span>
                  Address management
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-orange-400 rounded-full mr-3"></span>
                  Account verification
                </li>
              </ul>
            </div>

            <p className="text-sm text-gray-500 mt-6">
              Stay tuned for updates! New features will come gradually with next updates.
            </p>
          </div>
        </div>
      </motion.div>
    </div>
  );
}