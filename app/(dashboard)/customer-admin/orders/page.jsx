'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
    MagnifyingGlassIcon,
    FunnelIcon,
    DocumentArrowDownIcon,
    EyeIcon,
    CalendarDaysIcon,
    TruckIcon,
    CreditCardIcon,
    ChevronLeftIcon,
    ChevronRightIcon,
    XMarkIcon
} from '@heroicons/react/24/outline';
import { downloadOrderPDF } from '@/app/utils/pdfGenerator';
import toast from 'react-hot-toast';

export default function OrdersPage() {
    const [orders, setOrders] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [selectedOrder, setSelectedOrder] = useState(null);
    const [showOrderModal, setShowOrderModal] = useState(false);
    const [isDemoMode, setIsDemoMode] = useState(false);

    useEffect(() => {
        fetchOrders();
    }, [currentPage]);

    const getSampleOrders = () => {
        return [
            {
                id: 1,
                order_number: "ORD-DEMO001",
                user_id: "1",
                cart_id: null,
                status: "completed",
                payment_status: "paid",
                shipping_status: "delivered",
                subtotal: "1200.00",
                discount_amount: "50.00",
                shipping_amount: "15.00",
                tax_amount: "120.00",
                total_amount: "1285.00",
                currency_code: "AUD",
                notes: null,
                billing_address: "John Smith\n123 Business Street\nSydney, NSW 2000\nAustralia",
                shipping_address: "John Smith\n123 Business Street\nSydney, NSW 2000\nAustralia",
                billing_phone: "+61 2 9876 5432",
                shipping_phone: "+61 2 9876 5432",
                billing_email: "<EMAIL>",
                payment_method: "credit_card",
                payment_transaction_id: "txn_demo123",
                refund_reason: null,
                refund_amount: null,
                refund_transaction_id: null,
                customer_notes: "Please deliver during business hours",
                admin_notes: null,
                invoice_id: "INV-001",
                created_at: "2024-01-15T10:30:00.000000Z",
                updated_at: "2024-01-15T10:30:00.000000Z",
                items: [
                    {
                        id: 1,
                        order_id: "1",
                        product_id: "1",
                        product_name: "Premium Business Laptop",
                        product_sku: "PBL-12345",
                        quantity: 2,
                        unit_price: "600.00",
                        subtotal: "1200.00",
                        discount_amount: "50.00",
                        tax_amount: "120.00",
                        total_amount: "1270.00",
                        is_bulk_pricing: false,
                        is_pack_pricing: true,
                        bulk_price_id: null,
                        pack_price_id: "1",
                        customer_margin: "15.50",
                        partner_margin: "10.25",
                        customer_margin_type: "percentage",
                        partner_margin_type: "percentage",
                        created_at: "2024-01-15T10:30:00.000000Z",
                        updated_at: "2024-01-15T10:30:00.000000Z",
                        product: {
                            id: 1,
                            name: "Premium Business Laptop",
                            description: "High-performance business laptop with Intel Core i7 processor, 16GB RAM, and 512GB SSD storage. Perfect for professionals who need reliable performance for their everyday tasks."
                        }
                    }
                ]
            },
            {
                id: 2,
                order_number: "ORD-DEMO002",
                user_id: "1",
                cart_id: null,
                status: "pending",
                payment_status: "paid",
                shipping_status: "processing",
                subtotal: "450.00",
                discount_amount: "0.00",
                shipping_amount: "10.00",
                tax_amount: "45.00",
                total_amount: "505.00",
                currency_code: "AUD",
                notes: null,
                billing_address: "Jane Doe\n456 Corporate Ave\nMelbourne, VIC 3000\nAustralia",
                shipping_address: "Jane Doe\n456 Corporate Ave\nMelbourne, VIC 3000\nAustralia",
                billing_phone: "+61 3 8765 4321",
                shipping_phone: "+61 3 8765 4321",
                billing_email: "<EMAIL>",
                payment_method: "bank_transfer",
                payment_transaction_id: "txn_demo456",
                refund_reason: null,
                refund_amount: null,
                refund_transaction_id: null,
                customer_notes: null,
                admin_notes: null,
                invoice_id: "INV-002",
                created_at: "2024-01-14T14:20:00.000000Z",
                updated_at: "2024-01-14T14:20:00.000000Z",
                items: [
                    {
                        id: 2,
                        order_id: "2",
                        product_id: "2",
                        product_name: "Wireless Bluetooth Headphones",
                        product_sku: "WBH-789",
                        quantity: 3,
                        unit_price: "150.00",
                        subtotal: "450.00",
                        discount_amount: "0.00",
                        tax_amount: "45.00",
                        total_amount: "495.00",
                        is_bulk_pricing: false,
                        is_pack_pricing: true,
                        bulk_price_id: null,
                        pack_price_id: "2",
                        customer_margin: "12.00",
                        partner_margin: "8.00",
                        customer_margin_type: "percentage",
                        partner_margin_type: "percentage",
                        created_at: "2024-01-14T14:20:00.000000Z",
                        updated_at: "2024-01-14T14:20:00.000000Z",
                        product: {
                            id: 2,
                            name: "Wireless Bluetooth Headphones",
                            description: "Premium noise-cancelling wireless headphones with 30-hour battery life and superior sound quality."
                        }
                    }
                ]
            }
        ];
    };

    const fetchOrders = async () => {
        try {
            setLoading(true);
            const token = localStorage.getItem('userAuthToken');

            if (!token) {
                // For demo purposes, show sample data when no token is available
                console.log('No auth token found, showing demo data');
                setIsDemoMode(true);
                setTimeout(() => {
                    setOrders(getSampleOrders());
                    setTotalPages(1);
                    setLoading(false);
                }, 1000);
                return;
            }

            try {
                const response = await fetch(`/api/orders?page=${currentPage}&limit=10`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    console.log('API response not ok, falling back to demo data');
                    setIsDemoMode(true);
                    setOrders(getSampleOrders());
                    setTotalPages(1);
                    return;
                }

                const data = await response.json();

                if (data.success && data.data?.orders) {
                    setOrders(data.data.orders.data || []);
                    setTotalPages(data.data.orders.last_page || 1);
                } else {
                    console.log('Invalid data structure, falling back to demo data');
                    setIsDemoMode(true);
                    setOrders(getSampleOrders());
                    setTotalPages(1);
                }
            } catch (apiError) {
                console.log('API call failed, showing demo data:', apiError.message);
                setIsDemoMode(true);
                setOrders(getSampleOrders());
                setTotalPages(1);
            }
        } catch (error) {
            console.error('Error in fetchOrders:', error);
            // Always show demo data on any error
            setIsDemoMode(true);
            setOrders(getSampleOrders());
            setTotalPages(1);
        } finally {
            setLoading(false);
        }
    };

    const handleDownloadPDF = (order) => {
        try {
            downloadOrderPDF(order);
            toast.success('PDF downloaded successfully!');
        } catch (error) {
            console.error('Error generating PDF:', error);
            toast.error('Failed to generate PDF');
        }
    };

    const getStatusColor = (status) => {
        switch (status.toLowerCase()) {
            case 'completed': return 'bg-green-100 text-green-800';
            case 'pending': return 'bg-yellow-100 text-yellow-800';
            case 'cancelled': return 'bg-red-100 text-red-800';
            case 'processing': return 'bg-blue-100 text-blue-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getPaymentStatusColor = (status) => {
        switch (status.toLowerCase()) {
            case 'paid': return 'bg-green-100 text-green-800';
            case 'pending': return 'bg-yellow-100 text-yellow-800';
            case 'failed': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getShippingStatusColor = (status) => {
        switch (status.toLowerCase()) {
            case 'delivered': return 'bg-green-100 text-green-800';
            case 'shipped': return 'bg-blue-100 text-blue-800';
            case 'processing': return 'bg-yellow-100 text-yellow-800';
            case 'pending': return 'bg-gray-100 text-gray-800';
            case 'cancelled': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const filteredOrders = orders.filter(order => {
        const matchesSearch = order.order_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            order.billing_email.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
        return matchesSearch && matchesStatus;
    });

    return (
        <div className="min-h-screen bg-white p-6">
            <div className="max-w-7xl mx-auto">
                {/* Header */}
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mb-8"
                >
                    <h1 className="text-3xl font-bold text-black mb-2">Order History</h1>
                    <p className="text-blue-600">Manage and track all your orders</p>
                </motion.div>

                {/* Demo Banner */}
                {isDemoMode && (
                    <motion.div
                        initial={{ opacity: 0, y: -20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.05 }}
                        className="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6"
                    >
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-blue-800">Demo Mode</h3>
                                <p className="text-sm text-blue-700">
                                    You're viewing sample order data. In production, this will show real orders from your API.
                                </p>
                            </div>
                        </div>
                    </motion.div>
                )}

                {/* Search and Filter Bar */}
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                    className="bg-blue-50 rounded-xl shadow-sm border border-blue-200 p-4 md:p-6 mb-6"
                >
                    <div className="flex flex-col gap-4">
                        {/* Search */}
                        <div className="relative">
                            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-blue-500" />
                            <input
                                type="text"
                                placeholder="Search by order number or email..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full pl-10 pr-4 py-3 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black placeholder-blue-400"
                            />
                        </div>

                        {/* Filter and Download Row */}
                        <div className="flex flex-col sm:flex-row gap-3">
                            {/* Status Filter */}
                            <div className="relative flex-1 sm:max-w-xs">
                                <FunnelIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-blue-500" />
                                <select
                                    value={statusFilter}
                                    onChange={(e) => setStatusFilter(e.target.value)}
                                    className="w-full pl-10 pr-8 py-3 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none bg-white text-black"
                                >
                                    <option value="all">All Status</option>
                                    <option value="pending">Pending</option>
                                    <option value="processing">Processing</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>

                            {/* Download All Button */}
                            <button
                                onClick={() => {
                                    // Download all orders as a combined PDF
                                    if (filteredOrders.length > 0) {
                                        filteredOrders.forEach(order => {
                                            setTimeout(() => handleDownloadPDF(order), 100);
                                        });
                                        toast.success(`Downloading ${filteredOrders.length} invoices!`);
                                    } else {
                                        toast.error('No orders to download');
                                    }
                                }}
                                className="flex items-center justify-center px-4 py-3 bg-orange-500 hover:bg-orange-600 text-white font-medium rounded-lg transition-colors duration-200 shadow-sm whitespace-nowrap"
                            >
                                <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
                                <span className="hidden sm:inline">Download All Invoices</span>
                                <span className="sm:hidden">Download All</span>
                            </button>
                        </div>
                    </div>
                </motion.div>

                {/* Orders Display */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="bg-white rounded-xl shadow-sm border border-blue-200 overflow-hidden"
                >
                    {loading ? (
                        <div className="flex items-center justify-center py-12">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                            <span className="ml-3 text-blue-600">Loading orders...</span>
                        </div>
                    ) : filteredOrders.length === 0 ? (
                        <div className="text-center py-12">
                            <div className="text-blue-400 mb-4">
                                <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                            </div>
                            <h3 className="text-lg font-medium text-black mb-2">No orders found</h3>
                            <p className="text-blue-600">You haven't placed any orders yet or no orders match your search criteria.</p>
                        </div>
                    ) : (
                        <>
                            {/* Desktop Table View - Hidden on Mobile */}
                            <div className="hidden md:block">
                                {/* Table Header */}
                                <div className="bg-blue-50 px-6 py-4 border-b border-blue-200">
                                    <div className="grid grid-cols-12 gap-4 text-xs font-medium text-blue-700 uppercase tracking-wider">
                                        <div className="col-span-3">Order Details</div>
                                        <div className="col-span-2">Customer</div>
                                        <div className="col-span-2">Status</div>
                                        <div className="col-span-2">Amount</div>
                                        <div className="col-span-2">Date</div>
                                        <div className="col-span-1">Actions</div>
                                    </div>
                                </div>

                                {/* Table Body */}
                                <div className="divide-y divide-blue-200">
                                    {filteredOrders.map((order, index) => (
                                        <motion.div
                                            key={order.id}
                                            initial={{ opacity: 0, x: -20 }}
                                            animate={{ opacity: 1, x: 0 }}
                                            transition={{ delay: index * 0.05 }}
                                            className="px-6 py-4 hover:bg-blue-50 transition-colors duration-200"
                                        >
                                            <div className="grid grid-cols-12 gap-4 items-center">
                                                {/* Order Details */}
                                                <div className="col-span-3">
                                                    <div className="font-medium text-black">{order.order_number}</div>
                                                    <div className="text-sm text-blue-600">
                                                        Invoice: {order.invoice_id || 'N/A'}
                                                    </div>
                                                    <div className="text-sm text-blue-600">
                                                        {order.items?.length || 0} item(s)
                                                    </div>
                                                </div>

                                                {/* Customer */}
                                                <div className="col-span-2">
                                                    <div className="text-sm text-black">{order.billing_email}</div>
                                                    <div className="text-sm text-blue-600">
                                                        {order.billing_phone}
                                                    </div>
                                                </div>

                                                {/* Status */}
                                                <div className="col-span-2">
                                                    <div className="space-y-1">
                                                        <div className="flex items-center space-x-1">
                                                            <span className="text-xs text-blue-500 font-medium">Order:</span>
                                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                                                                {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                                                            </span>
                                                        </div>
                                                        <div className="flex items-center space-x-1">
                                                            <CreditCardIcon className="h-3 w-3 text-blue-500" />
                                                            <span className="text-xs text-blue-500 font-medium">Pay:</span>
                                                            <span className={`text-xs px-1.5 py-0.5 rounded font-semibold ${getPaymentStatusColor(order.payment_status)}`}>
                                                                {order.payment_status.charAt(0).toUpperCase() + order.payment_status.slice(1)}
                                                            </span>
                                                        </div>
                                                        <div className="flex items-center space-x-1">
                                                            <TruckIcon className="h-3 w-3 text-blue-500" />
                                                            <span className="text-xs text-blue-500 font-medium">Ship:</span>
                                                            <span className={`text-xs px-1.5 py-0.5 rounded font-semibold ${getShippingStatusColor(order.shipping_status)}`}>
                                                                {order.shipping_status.charAt(0).toUpperCase() + order.shipping_status.slice(1)}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* Amount */}
                                                <div className="col-span-2">
                                                    <div className="font-medium text-black">
                                                        {order.currency_code} {parseFloat(order.total_amount).toFixed(2)}
                                                    </div>
                                                    <div className="text-sm text-blue-600">
                                                        Subtotal: {order.currency_code} {parseFloat(order.subtotal).toFixed(2)}
                                                    </div>
                                                </div>

                                                {/* Date */}
                                                <div className="col-span-2">
                                                    <div className="flex items-center text-sm text-black">
                                                        <CalendarDaysIcon className="h-4 w-4 mr-1 text-blue-500" />
                                                        {new Date(order.created_at).toLocaleDateString()}
                                                    </div>
                                                    <div className="text-sm text-blue-600">
                                                        {new Date(order.created_at).toLocaleTimeString()}
                                                    </div>
                                                </div>

                                                {/* Actions */}
                                                <div className="col-span-1">
                                                    <div className="flex space-x-2">
                                                        <button
                                                            onClick={() => {
                                                                setSelectedOrder(order);
                                                                setShowOrderModal(true);
                                                            }}
                                                            className="p-2 text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                                                            title="View Details"
                                                        >
                                                            <EyeIcon className="h-4 w-4" />
                                                        </button>
                                                        <button
                                                            onClick={() => handleDownloadPDF(order)}
                                                            className="p-2 text-orange-500 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors duration-200"
                                                            title="Download PDF"
                                                        >
                                                            <DocumentArrowDownIcon className="h-4 w-4" />
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </motion.div>
                                    ))}
                                </div>
                            </div>

                            {/* Mobile Card View - Visible only on Mobile */}
                            <div className="md:hidden divide-y divide-blue-200">
                                {filteredOrders.map((order, index) => (
                                    <motion.div
                                        key={order.id}
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ delay: index * 0.05 }}
                                        className="p-4 hover:bg-blue-50 transition-colors duration-200"
                                    >
                                        {/* Card Header */}
                                        <div className="flex justify-between items-start mb-3">
                                            <div>
                                                <div className="font-semibold text-black text-lg">{order.order_number}</div>
                                                <div className="text-sm text-blue-600">
                                                    Invoice: {order.invoice_id || 'N/A'}
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <div className="font-bold text-black text-lg">
                                                    {order.currency_code} {parseFloat(order.total_amount).toFixed(2)}
                                                </div>
                                                <div className="text-sm text-blue-600">
                                                    {order.items?.length || 0} item(s)
                                                </div>
                                            </div>
                                        </div>

                                        {/* Customer Info */}
                                        <div className="mb-3">
                                            <div className="text-sm text-black font-medium">{order.billing_email}</div>
                                            <div className="text-sm text-blue-600">{order.billing_phone}</div>
                                        </div>

                                        {/* Status Section */}
                                        <div className="mb-3">
                                            <div className="grid grid-cols-1 gap-2">
                                                <div className="flex items-center justify-between">
                                                    <span className="text-sm text-blue-600 font-medium">Order Status:</span>
                                                    <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                                                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                                                    </span>
                                                </div>
                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-center">
                                                        <CreditCardIcon className="h-4 w-4 text-blue-500 mr-1" />
                                                        <span className="text-sm text-blue-600 font-medium">Payment:</span>
                                                    </div>
                                                    <span className={`px-3 py-1 text-xs font-semibold rounded-full ${getPaymentStatusColor(order.payment_status)}`}>
                                                        {order.payment_status.charAt(0).toUpperCase() + order.payment_status.slice(1)}
                                                    </span>
                                                </div>
                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-center">
                                                        <TruckIcon className="h-4 w-4 text-blue-500 mr-1" />
                                                        <span className="text-sm text-blue-600 font-medium">Shipping:</span>
                                                    </div>
                                                    <span className={`px-3 py-1 text-xs font-semibold rounded-full ${getShippingStatusColor(order.shipping_status)}`}>
                                                        {order.shipping_status.charAt(0).toUpperCase() + order.shipping_status.slice(1)}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Date and Actions */}
                                        <div className="flex justify-between items-center pt-3 border-t border-blue-200">
                                            <div className="flex items-center text-sm text-blue-600">
                                                <CalendarDaysIcon className="h-4 w-4 mr-1" />
                                                {new Date(order.created_at).toLocaleDateString()}
                                            </div>
                                            <div className="flex space-x-2">
                                                <button
                                                    onClick={() => {
                                                        setSelectedOrder(order);
                                                        setShowOrderModal(true);
                                                    }}
                                                    className="p-2 text-blue-500 hover:text-blue-700 hover:bg-blue-100 rounded-lg transition-colors duration-200"
                                                    title="View Details"
                                                >
                                                    <EyeIcon className="h-5 w-5" />
                                                </button>
                                                <button
                                                    onClick={() => handleDownloadPDF(order)}
                                                    className="p-2 text-orange-500 hover:text-orange-700 hover:bg-orange-100 rounded-lg transition-colors duration-200"
                                                    title="Download PDF"
                                                >
                                                    <DocumentArrowDownIcon className="h-5 w-5" />
                                                </button>
                                            </div>
                                        </div>
                                    </motion.div>
                                ))}
                            </div>
                        </>
                    )}
                </motion.div>

                {/* Pagination */}
                {totalPages > 1 && (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                        className="bg-white rounded-xl shadow-sm border border-blue-200 px-6 py-4 mt-6"
                    >
                        <div className="flex items-center justify-between">
                            <div className="text-sm text-black">
                                Page {currentPage} of {totalPages}
                            </div>
                            <div className="flex space-x-2">
                                <button
                                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                                    disabled={currentPage === 1}
                                    className="flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-white border border-blue-300 rounded-lg hover:bg-blue-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    <ChevronLeftIcon className="h-4 w-4 mr-1" />
                                    Previous
                                </button>
                                <button
                                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                                    disabled={currentPage === totalPages}
                                    className="flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-white border border-blue-300 rounded-lg hover:bg-blue-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    Next
                                    <ChevronRightIcon className="h-4 w-4 ml-1" />
                                </button>
                            </div>
                        </div>
                    </motion.div>
                )}

                {/* Order Detail Modal */}
                <AnimatePresence mode="wait">
                    {showOrderModal && selectedOrder && (
                        <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
                            onClick={() => setShowOrderModal(false)}
                        >
                            <motion.div
                                initial={{ scale: 0.95, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                exit={{ scale: 0.95, opacity: 0 }}
                                className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
                                onClick={(e) => e.stopPropagation()}
                            >
                                {/* Modal Header */}
                                <div className="flex items-center justify-between p-6 border-b border-blue-200">
                                    <div>
                                        <h2 className="text-xl font-semibold text-black">Order Details</h2>
                                        <p className="text-sm text-blue-600">{selectedOrder.order_number}</p>
                                    </div>
                                    <button
                                        onClick={() => setShowOrderModal(false)}
                                        className="p-2 text-blue-500 hover:text-blue-700 hover:bg-blue-100 rounded-lg"
                                    >
                                        <XMarkIcon className="h-5 w-5" />
                                    </button>
                                </div>

                                {/* Modal Content */}
                                <div className="p-6 space-y-6">
                                    {/* Order Summary */}
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <h3 className="text-lg font-medium text-black mb-4">Order Information</h3>
                                            <div className="space-y-3">
                                                <div className="flex justify-between">
                                                    <span className="text-blue-600">Order Number:</span>
                                                    <span className="font-medium text-black">{selectedOrder.order_number}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-blue-600">Invoice ID:</span>
                                                    <span className="font-medium text-black">{selectedOrder.invoice_id || 'N/A'}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-blue-600">Status:</span>
                                                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(selectedOrder.status)}`}>
                                                        {selectedOrder.status.charAt(0).toUpperCase() + selectedOrder.status.slice(1)}
                                                    </span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-blue-600">Payment Status:</span>
                                                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getPaymentStatusColor(selectedOrder.payment_status)}`}>
                                                        {selectedOrder.payment_status}
                                                    </span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-blue-600">Payment Method:</span>
                                                    <span className="font-medium text-black">{selectedOrder.payment_method.replace('_', ' ').toUpperCase()}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-blue-600">Order Date:</span>
                                                    <span className="font-medium text-black">{new Date(selectedOrder.created_at).toLocaleDateString()}</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div>
                                            <h3 className="text-lg font-medium text-black mb-4">Billing Address</h3>
                                            <div className="text-sm text-blue-600 whitespace-pre-line">
                                                {selectedOrder.billing_address}
                                            </div>
                                            <div className="mt-2 text-sm text-blue-600">
                                                <div>Email: {selectedOrder.billing_email}</div>
                                                <div>Phone: {selectedOrder.billing_phone}</div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Order Items */}
                                    <div>
                                        <h3 className="text-lg font-medium text-black mb-4">Order Items</h3>
                                        <div className="border border-blue-200 rounded-lg overflow-hidden">
                                            <div className="bg-blue-50 px-4 py-3 border-b border-blue-200">
                                                <div className="grid grid-cols-12 gap-4 text-xs font-medium text-blue-700 uppercase tracking-wider">
                                                    <div className="col-span-5">Product</div>
                                                    <div className="col-span-2">SKU</div>
                                                    <div className="col-span-1">Qty</div>
                                                    <div className="col-span-2">Unit Price</div>
                                                    <div className="col-span-2">Total</div>
                                                </div>
                                            </div>
                                            <div className="divide-y divide-blue-200">
                                                {selectedOrder.items?.map((item, index) => (
                                                    <div key={index} className="px-4 py-3">
                                                        <div className="grid grid-cols-12 gap-4 items-center">
                                                            <div className="col-span-5">
                                                                <div className="font-medium text-black">{item.product_name}</div>
                                                                <div className="text-sm text-blue-600">{item.product?.description?.substring(0, 100)}...</div>
                                                            </div>
                                                            <div className="col-span-2 text-sm text-blue-600">{item.product_sku}</div>
                                                            <div className="col-span-1 text-sm text-blue-600">{item.quantity}</div>
                                                            <div className="col-span-2 text-sm text-blue-600">
                                                                {selectedOrder.currency_code} {parseFloat(item.unit_price).toFixed(2)}
                                                            </div>
                                                            <div className="col-span-2 font-medium text-black">
                                                                {selectedOrder.currency_code} {parseFloat(item.total_amount).toFixed(2)}
                                                            </div>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    </div>

                                    {/* Order Totals */}
                                    <div className="bg-blue-50 rounded-lg p-4">
                                        <div className="space-y-2">
                                            <div className="flex justify-between text-sm">
                                                <span className="text-blue-600">Subtotal:</span>
                                                <span className="font-medium text-black">{selectedOrder.currency_code} {parseFloat(selectedOrder.subtotal).toFixed(2)}</span>
                                            </div>
                                            <div className="flex justify-between text-sm">
                                                <span className="text-blue-600">Shipping:</span>
                                                <span className="font-medium text-black">{selectedOrder.currency_code} {parseFloat(selectedOrder.shipping_amount).toFixed(2)}</span>
                                            </div>
                                            <div className="flex justify-between text-sm">
                                                <span className="text-blue-600">Tax:</span>
                                                <span className="font-medium text-black">{selectedOrder.currency_code} {parseFloat(selectedOrder.tax_amount).toFixed(2)}</span>
                                            </div>
                                            {parseFloat(selectedOrder.discount_amount) > 0 && (
                                                <div className="flex justify-between text-sm">
                                                    <span className="text-blue-600">Discount:</span>
                                                    <span className="font-medium text-green-600">-{selectedOrder.currency_code} {parseFloat(selectedOrder.discount_amount).toFixed(2)}</span>
                                                </div>
                                            )}
                                            <div className="border-t border-blue-200 pt-2">
                                                <div className="flex justify-between text-lg font-semibold">
                                                    <span className="text-black">Total:</span>
                                                    <span className="text-black">{selectedOrder.currency_code} {parseFloat(selectedOrder.total_amount).toFixed(2)}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Customer Notes */}
                                    {selectedOrder.customer_notes && (
                                        <div>
                                            <h3 className="text-lg font-medium text-black mb-2">Customer Notes</h3>
                                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                                <p className="text-sm text-blue-800">{selectedOrder.customer_notes}</p>
                                            </div>
                                        </div>
                                    )}

                                    {/* Modal Actions */}
                                    <div className="flex justify-end space-x-3 pt-4 border-t border-blue-200">
                                        <button
                                            onClick={() => setShowOrderModal(false)}
                                            className="px-4 py-2 text-sm font-medium text-blue-600 bg-white border border-blue-300 rounded-lg hover:bg-blue-50"
                                        >
                                            Close
                                        </button>
                                        <button
                                            onClick={() => handleDownloadPDF(selectedOrder)}
                                            className="px-4 py-2 text-sm font-medium text-white bg-orange-500 border border-transparent rounded-lg hover:bg-orange-600 flex items-center"
                                        >
                                            <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                                            Download Invoice PDF
                                        </button>
                                    </div>
                                </div>
                            </motion.div>
                        </motion.div>
                    )}
                </AnimatePresence>
            </div>
        </div>
    );
}