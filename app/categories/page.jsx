import React from 'react';
import AllCategories from '../components/Categories/AllCategories';
import metaInfo from '@/src/seo/meta-info';

// Server-side function to fetch categories
async function fetchCategoriesData() {
    try {
        const response = await fetch('https://b2b.instinctfusionx.xyz/public/api/v1/categories', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            cache: 'no-store' // Ensure fresh data on each request
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Categories API Response:', data);
        return data;
    } catch (error) {
        console.error('Error fetching categories data:', error);
        // Return fallback data structure
        return {
            success: false,
            message: 'Failed to fetch categories',
            data: {
                categories: {
                    current_page: 1,
                    data: [],
                    first_page_url: null,
                    from: null,
                    last_page: 1,
                    last_page_url: null,
                    links: [],
                    next_page_url: null,
                    path: null,
                    per_page: 15,
                    prev_page_url: null,
                    to: null,
                    total: 0
                }
            }
        };
    }
}

export default async function CategoriesPage() {
    // Fetch categories data server-side
    const categoriesData = await fetchCategoriesData();

    return (
        <main className="min-h-screen">
            <AllCategories categoriesData={categoriesData} />
        </main>
    );
}

// Metadata for SEO
export const metadata = metaInfo.categoriesPage;
