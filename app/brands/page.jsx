import React from 'react';
import AllBrands from '../components/Brands/AllBrands';
import metaInfo from '@/src/seo/meta-info';
import ModularNavbar from '../components/Navigation/ModularNavbar';
import Footer from '../components/LandingPage/Footer';

// Server-side function to fetch brands data
async function fetchBrandsData(resolvedSearchParams) {
    try {
        // Build query parameters from URL search params
        const params = new URLSearchParams();

        // Add pagination parameters
        if (resolvedSearchParams.page) params.append('page', resolvedSearchParams.page);
        if (resolvedSearchParams.per_page) params.append('per_page', resolvedSearchParams.per_page);

        // Add search parameter
        if (resolvedSearchParams.search) params.append('search', resolvedSearchParams.search);

        // Add sorting parameters
        if (resolvedSearchParams.sort) params.append('sort', resolvedSearchParams.sort);
        if (resolvedSearchParams.order) params.append('order', resolvedSearchParams.order);

        // Add filter parameters
        if (resolvedSearchParams.is_featured) params.append('is_featured', resolvedSearchParams.is_featured);
        if (resolvedSearchParams.is_active) params.append('is_active', resolvedSearchParams.is_active);
        if (resolvedSearchParams.has_products) params.append('has_products', resolvedSearchParams.has_products);

        const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/brands?${params.toString()}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            cache: 'no-store' // Ensure fresh data on each request
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Brands API Response:', data);
        return data;
    } catch (error) {
        console.error('Error fetching brands data:', error);
        // Return fallback data structure
        return {
            success: false,
            message: 'Failed to fetch brands',
            data: {
                brands: {
                    current_page: 1,
                    data: [],
                    first_page_url: null,
                    from: null,
                    last_page: 1,
                    last_page_url: null,
                    links: [],
                    next_page_url: null,
                    path: null,
                    per_page: 15,
                    prev_page_url: null,
                    to: null,
                    total: 0
                }
            }
        };
    }
}

export default async function BrandsPage({ searchParams }) {
    // Await searchParams for Next.js 15 compatibility
    const resolvedSearchParams = await searchParams;

    // Fetch brands data server-side
    const brandsData = await fetchBrandsData(resolvedSearchParams);

    return (
        <div className="min-h-screen bg-white">
            <ModularNavbar />
            <main className="min-h-screen bg-gray-50">
                <AllBrands
                    initialData={brandsData}
                    searchParams={resolvedSearchParams}
                />
            </main>
            <Footer />
        </div>
    );
}

// Metadata for SEO
export const metadata = metaInfo.brandsPage;