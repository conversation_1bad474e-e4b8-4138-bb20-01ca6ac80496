import React, { useState } from 'react'
import {
    useStripe,
    useElements,
    PaymentElement,
} from '@stripe/react-stripe-js';
import toast from 'react-hot-toast';
import { motion } from 'framer-motion';
import { MdPlaylistAddCheckCircle } from 'react-icons/md';
import Link from 'next/link';
import Swal from 'sweetalert2';
import { BsCreditCard2FrontFill, BsShieldCheck } from 'react-icons/bs';
import { FaHandHoldingDollar, FaSpinner } from 'react-icons/fa6';
import { HiCheckCircle, HiExclamationCircle } from 'react-icons/hi';

export default function CheckoutForm({ paymentInstance: serverPaymentInstance, billingAddress, shippingAddress, paymentInfo, notes }) {
    const stripe = useStripe();
    const elements = useElements();

    const [errorMessage, setErrorMessage] = useState(null);
    const [loading, setLoading] = useState(false);
    const [transactionId, setTransactionId] = useState(null);

    console.log({ serverPaymentInstance }, { billingAddress }, { shippingAddress });

    async function handleSubmit(event) {
        event.preventDefault();
        setLoading(true);


        if (!stripe || !elements) {
            toast.error("Stripe not loaded");
            return;
        }

        const { error: submitError } = elements.submit();
        console.log({ submitError });

        if (submitError) {
            setLoading(false);
            setErrorMessage(submitError.message);
            toast.error(submitError.message);
            return;
        }

        const { error: confirmError, paymentIntent } = await stripe.confirmPayment({
            elements,
            clientSecret: serverPaymentInstance?.data?.client_secret,
            confirmParams: {
                // receipt_email: "<EMAIL>",
                // shipping: shippingAddress,
                payment_method_data: {
                    // billing_details: billingAddress

                },
                return_url: `${process.env.NEXT_PUBLIC_BASE_URL}/cart`
            },
            redirect: 'if_required'
        });
        console.log({ confirmError }, { paymentIntent });

        if (confirmError) {
            setLoading(false);
            setErrorMessage(confirmError.message);
            toast.error(confirmError.message);
            return;
        }

        if (paymentIntent.status === "succeeded" && paymentIntent.id) {
            setTransactionId(paymentIntent.id);

            toast.success(`Payment successful! Transaction ID: ${paymentIntent.id}`, {
                autoClose: 5000,
                hideProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                draggable: true,
                progress: undefined,
            });

            const cartId = localStorage.getItem("cartId");
            if (!cartId) {
                throw new Error("Cart ID not found");
            }

            const payment = {
                payment_intent: paymentIntent,
                payment_intent_id: paymentIntent.id,
                cart_id: cartId,
                shipping_address: {
                    contact_name: shippingAddress.name || "anonymous",
                    contact_phone: shippingAddress.phone || "anonymous",
                    contact_email: shippingAddress.email || "",
                    address_line1: shippingAddress.street || "anonymous",
                    address_line2: shippingAddress.street2 || "",
                    city: shippingAddress.city || "anonymous",
                    state: shippingAddress.state || "anonymous",
                    postal_code: shippingAddress.zip || "anonymous",
                    zip_code: shippingAddress.zip || "anonymous",
                    country: shippingAddress.country || "anonymous",
                    delivery_instructions: shippingAddress.instructions || "Please deliver to reception during business hours"
                },
                billing_address: {
                    contact_name: billingAddress.name || "anonymous",
                    contact_phone: billingAddress.phone || "anonymous",
                    contact_email: billingAddress.email || "",
                    address_line1: billingAddress.street || "anonymous",
                    address_line2: billingAddress.street2 || "",
                    city: billingAddress.city || "anonymous",
                    state: billingAddress.state || "anonymous",
                    postal_code: billingAddress.zip || "anonymous",
                    zip_code: billingAddress.zip || "anonymous",
                    country: billingAddress.country || "anonymous"
                },
                payment_method: paymentIntent.payment_method_types.toString() || "pm_card_visa",
                notes: `${notes} Please include tax invoice with the delivery`
            };
            // console.log("PAYLOAD: ", JSON.stringify(payment), payment);


            const token = localStorage.getItem("userAuthToken");
            if (!token) {
                throw new Error("Authentication token not found");
            }

            const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/checkout/process`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${token}`,
                },
                body: JSON.stringify(payment)
            });
            const result = await response.json();
            console.log("After Payment Order Process result:", result);


            if (result.success === true && result.data?.order?.id) {

                toast.success(result.message || 'Payment processed successfully!', {
                    duration: 10000,
                });

                Swal.fire({
                    title: "Payment Successful!",
                    text: result.message || "Your payment has been processed successfully.",
                    icon: "success",
                    confirmButtonText: "OK",
                    timer: 5000,
                    timerProgressBar: true,
                });
                setLoading(false);
            } else {
                toast.error(result.message || 'Failed to store payment in database', {
                    hideProgressBar: false,
                    closeOnClick: true,
                    pauseOnHover: true,
                    draggable: true,
                    progress: undefined,
                    timer: 10000,
                });

                Swal.fire({
                    title: "Payment Failed!",
                    text: result.message || "Your payment has failed to stroe in Database.",
                    icon: "error",
                    confirmButtonText: "OK",
                    timerProgressBar: true,
                })
            }
        }

        setLoading(false);
    }

    if (loading && !stripe && !elements) {
        return (
            <div className="flex items-center justify-center">
                <div
                    className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-e-transparent align-[-0.125em] text-surface motion-reduce:animate-[spin_1.5s_linear_infinite] dark:text-white"
                    role="status"
                >
                    <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
                        Loading...
                    </span>
                </div>
            </div>
        );
    }


    return (
        <div className="max-w-2xl mx-auto p-2">
            {/* Payment Card */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden mb-5"
            >
                {/* Card Header */}
                <div className="bg-gradient-to-r from-yellow-400 via-amber-500 to-orange-600 px-5 py-5">
                    <h2 className="text-2xl font-bold text-white flex items-center">
                        <BsCreditCard2FrontFill className="mr-4 text-3xl" />
                        Payment Details
                    </h2>
                </div>

                {/* Amount Display */}
                <div className="p-3">
                    <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-3xl p-5 mb-8 border border-gray-100">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-gray-600 text-lg mb-1">Total Amount</p>
                            </div>
                            <div className="flex items-center gap-3">
                                <FaHandHoldingDollar className="text-3xl text-indigo-600" />
                                <span className="text-4xl font-bold text-gray-900">
                                    ${serverPaymentInstance.data.amount?.toFixed(2) || '0.00'}
                                </span>
                            </div>
                        </div>
                        <p className="text-sm text-gray-500">Including all taxes and fees</p>
                    </div>

                    {serverPaymentInstance?.data?.client_secret && (
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Payment Element */}
                            <div className="space-y-4">
                                <PaymentElement
                                    onChange={(e) => {
                                        if (e.error) {
                                            setErrorMessage(e.error.message);
                                        } else {
                                            setErrorMessage("");
                                        }
                                    }}
                                    options={{
                                        style: {
                                            base: {
                                                fontSize: '16px',
                                                color: '#374151',
                                                fontFamily: 'system-ui, -apple-system, sans-serif',
                                                '::placeholder': {
                                                    color: '#9CA3AF',
                                                },
                                            },
                                        },
                                    }}
                                />

                            </div>

                            {/* Security Notice */}
                            <div className="bg-green-50 border border-green-200 rounded-3xl p-3">
                                <div className="flex items-center text-green-800">
                                    <BsShieldCheck className="mr-3 text-green-600 text-xl" />
                                    <div>
                                        <p className="font-semibold">Secure Payment</p>
                                        <p className="text-sm text-green-700">Your payment information is encrypted and secure</p>
                                    </div>
                                </div>
                            </div>

                            {/* Submit Button */}
                            <motion.button
                                type="submit"
                                disabled={loading || !stripe || !elements}
                                className={`w-full py-4 px-6 rounded-3xl font-bold text-lg transition-all duration-300 ${loading || !stripe || !elements
                                    ? 'bg-gray-400 cursor-not-allowed text-gray-600'
                                    : 'bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 hover:from-blue-700 hover:via-indigo-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-1'
                                    }`}
                                whileHover={!loading && stripe && elements ? { scale: 1.02 } : {}}
                                whileTap={!loading && stripe && elements ? { scale: 0.98 } : {}}
                                aria-label={loading ? "Processing payment" : "Pay now"}
                            >
                                {loading ? (
                                    <div className="flex items-center justify-center">
                                        <FaSpinner className="animate-spin mr-3 text-xl" />
                                        Processing Payment...
                                    </div>
                                ) : (
                                    <div className="flex items-center justify-center">
                                        <FaHandHoldingDollar className="mr-3 text-xl" />
                                        Complete Payment
                                    </div>
                                )}
                            </motion.button>
                        </form>
                    )}
                </div>
            </motion.div>

            {/* Error Message */}
            {errorMessage && (
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-red-50 border border-red-200 rounded-3xl p-5 mb-6"
                >
                    <div className="flex items-center gap-4">
                        <HiExclamationCircle className="w-8 h-8 text-red-500 flex-shrink-0" />
                        <div>
                            <h3 className="font-semibold text-red-800 mb-1">Payment Error</h3>
                            <p className="text-red-700">{errorMessage}</p>
                        </div>
                    </div>
                </motion.div>
            )}

            {/* Success Message */}
            {transactionId && (
                <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5 }}
                    className="bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden"
                >
                    {/* Success Header */}
                    <div className="bg-gradient-to-r from-green-400 to-emerald-500 px-8 py-6">
                        <div className="flex items-center text-white">
                            <HiCheckCircle className="w-8 h-8 mr-4" />
                            <div>
                                <h2 className="text-2xl font-bold">Payment Successful!</h2>
                                <p className="text-green-100">Your order has been confirmed</p>
                            </div>
                        </div>
                    </div>

                    {/* Success Content */}
                    <div className="p-5 text-center space-y-6">
                        <MdPlaylistAddCheckCircle className="w-20 h-20 mx-auto text-green-500" />

                        <div className="space-y-2">
                            <h3 className="text-2xl font-bold text-gray-900">Order Confirmed!</h3>
                            <p className="text-gray-600 text-lg">Thank you for your purchase.</p>
                        </div>

                        {/* Transaction Details */}
                        <div className="bg-gray-50 rounded-3xl p-5 space-y-3">
                            <div className="flex flex-col md:flex-row justify-between items-center">
                                <span className="text-gray-600">Transaction ID:</span>
                                <span className="font-mono text-sm bg-white px-3 py-1 rounded-lg border li">{transactionId}</span>
                            </div>
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Total Amount:</span>
                                <span className="text-2xl font-bold text-green-600">${serverPaymentInstance.data.amount}</span>
                            </div>
                        </div>

                        {/* Continue Shopping Button */}
                        <motion.div
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            <Link
                                href="/products"
                                className="inline-block bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-bold py-4 px-8 rounded-3xl shadow-lg hover:shadow-xl transition-all duration-300"
                            >
                                Continue Shopping
                            </Link>
                        </motion.div>
                    </div>
                </motion.div>
            )}
        </div>

    )
}
