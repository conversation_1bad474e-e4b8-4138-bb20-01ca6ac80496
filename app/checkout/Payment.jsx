import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import CheckoutForm from './ChekcoutForm';

export default function Payment({
    paymentInstance,
    shippingAddress,
    billingAddress,
    paymentInfo,
    notes
}) {


    const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);

    return (
        <div>
            {
                paymentInstance?.data &&
                <Elements
                    stripe={stripePromise}
                    options={{
                        mode: "payment",
                        amount: parseInt(paymentInfo.data.amount),
                        currency: paymentInfo.data.currency,
                    }}>
                    <CheckoutForm
                        paymentInstance={paymentInstance}
                        shippingAddress={shippingAddress}
                        billingAddress={billingAddress}
                        paymentInfo={paymentInfo}
                        notes={notes}
                    />
                </Elements>
            }
        </div>
    )
}
