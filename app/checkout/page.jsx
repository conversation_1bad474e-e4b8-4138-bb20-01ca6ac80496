"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaUser, FaCheckCircle, FaBuilding, FaMapMarkerAlt, FaStickyNote } from "react-icons/fa";
import { BsCreditCard2FrontFill, BsShieldCheck } from "react-icons/bs";
import { TiArrowLeftThick, TiArrowRightThick } from "react-icons/ti";
import { PiMapPinFill } from "react-icons/pi";
import { GiWallet } from "react-icons/gi";
import { TbShieldCheckFilled } from "react-icons/tb";
import { HiLocationMarker, HiMail, HiPhone, HiOfficeBuilding } from "react-icons/hi";
import { MdSecurity, MdVerified } from "react-icons/md";
import Payment from './Payment';
import { useRouter } from 'next/navigation';
import { FaTruckPlane } from 'react-icons/fa6';
import ModularNavbar from '../components/Navigation/ModularNavbar';
import Footer from '../components/LandingPage/Footer';

const Checkout = () => {
    const router = useRouter();

    const [step, setStep] = useState(1);
    const [shippingAddress, setShippingAddress] = useState({});
    const [billingAddress, setBillingAddress] = useState({});
    const [customerAddresses, setCustomerAddresses] = useState([]);
    const [isLoadingAddresses, setIsLoadingAddresses] = useState(true);
    const [addressError, setAddressError] = useState(null);
    const [showNewShippingAddress, setShowNewShippingAddress] = useState(false);
    const [paymentInstance, setPaymentInstance] = useState(null);
    const [paymentInfo, setPaymentInfo] = useState(null);
    const [notes, setNotes] = useState('');

    const nextStep = async () => {
        if (step === 1) { // Assuming step 2 is payment
            try {
                const response = await postInitailOrder();
                console.log(response);
                setPaymentInstance(response);
                if (response.success === true) {
                    // Get the appropriate token for payment info
                    const token = localStorage.getItem('userAuthToken') ||
                                localStorage.getItem('partnerAuthToken') ||
                                localStorage.getItem('superAdminAuthToken');

                    const responsePaymentInfo = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/payments/intent/${response.data.payment_intent_id}/info`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`,
                        },
                    });
                    const data = await responsePaymentInfo.json();
                    console.log(data);

                    setPaymentInfo(data);
                }

                setStep(prev => prev + 1);
            } catch (error) {
                console.error('Error proceeding to payment:', error);
                // Handle error (show error message to user)
            }
        } else {
            setStep(prev => prev + 1);
        }
    };
    const prevStep = () => setStep(prev => prev - 1);

    async function postInitailOrder() {
        console.log(localStorage.getItem('cartId'));
        const cartId = localStorage.getItem('cartId');

        // Get the appropriate token for checkout
        const token = localStorage.getItem('userAuthToken') ||
                    localStorage.getItem('partnerAuthToken') ||
                    localStorage.getItem('superAdminAuthToken');

        const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/checkout/initialize`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`,
            },
            body: JSON.stringify({
                cart_id: cartId,
                shipping_address: shippingAddress,
                billing_address: billingAddress,
                notes: "Please include tax invoice with the delivery"
            })
        });

        const data = await response.json();
        console.log(data);

        return data;
    }

    // Fetch customer addresses
    useEffect(() => {
        const fetchCustomerAddresses = async () => {
            try {
                setIsLoadingAddresses(true);

                // First check for user token
                let token = localStorage.getItem('userAuthToken');
                let userType = 'user';

                // If no user token, check for partner token
                if (!token) {
                    token = localStorage.getItem('partnerAuthToken');
                    userType = 'partner';
                }

                // If no token found, redirect to checkout to trigger middleware smart redirect
                if (!token) {
                    // Let the middleware handle the smart redirect by redirecting to checkout
                    window.location.href = '/checkout';
                    return;
                }

                console.log('Using token for:', userType); // Debug log

                const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/customer/addresses`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                if (data.success) {
                    setCustomerAddresses(data.data.addresses);
                    // Set default address if available
                    const defaultAddress = data.data.addresses.find(addr => addr.is_default);
                    if (defaultAddress) {
                        const formattedAddress = {
                            name: defaultAddress.contact_name,
                            street: defaultAddress.address_line1,
                            street2: defaultAddress.address_line2,
                            city: defaultAddress.city,
                            state: defaultAddress.state,
                            zip: defaultAddress.postal_code,
                            country: defaultAddress.country,
                            email: defaultAddress.contact_email,
                            phone: defaultAddress.contact_phone,
                            company: defaultAddress.company_name,
                            instructions: defaultAddress.delivery_instructions
                        };
                        setShippingAddress(formattedAddress);
                        setBillingAddress(prev => ({
                            ...prev,
                            ...formattedAddress
                        }));
                    }
                } else {
                    setAddressError('Failed to fetch addresses');
                }
            } catch (error) {
                console.error('Error fetching addresses:', error);
                setAddressError(`Failed to fetch addresses: ${error.message}`);
            } finally {
                setIsLoadingAddresses(false);
            }
        };

        fetchCustomerAddresses();
    }, []);

    const handleShippingChange = (e) => {
        const { name, value } = e.target;
        const updatedShippingAddress = { ...shippingAddress, [name]: value };
        setShippingAddress(updatedShippingAddress);
        setBillingAddress(prev => ({
            ...prev,
            [name]: value,
        }));
    };

    const handleBillingChange = (e) => {
        const { name, value, type, checked } = e.target;
        if (type === 'checkbox') {
            setBillingAddress(prev => ({
                ...prev,
                sameAsShipping: checked,
                ...(checked ? shippingAddress : {}), // Use shippingAddress here
            }));
        } else {
            setBillingAddress(prev => ({ ...prev, [name]: value }));
        }
    };

    // Animation variants
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.2, // Stagger the appearance of children
            },
        },
    };

    const itemVariants = {
        hidden: { x: -20, opacity: 0 },
        visible: { x: 0, opacity: 1 },
        exit: { x: 20, opacity: 0 },
    };

    const shippingAddressForm = (
        <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200"
        >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                    <label htmlFor="billingName" className="block text-gray-700 text-sm font-semibold">
                        <FaUser className="inline mr-2 text-blue-600" />
                        Full Name
                    </label>
                    <input
                        type="text"
                        id="billingName"
                        name="name"
                        value={shippingAddress.name}
                        onChange={handleShippingChange}
                        className="w-full py-3 px-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white"
                        placeholder="Enter your full name"
                        required={!shippingAddress.sameAsShipping}
                    />
                </div>
                <div className="space-y-2">
                    <label htmlFor="billingStreet" className="block text-gray-700 text-sm font-semibold">
                        <HiLocationMarker className="inline mr-2 text-blue-600" />
                        Street Address
                    </label>
                    <input
                        type="text"
                        id="billingStreet"
                        name="street"
                        value={shippingAddress.street}
                        onChange={handleShippingChange}
                        className="w-full py-3 px-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white"
                        placeholder="Enter street address"
                        required={!shippingAddress.sameAsShipping}
                    />
                </div>
                <div className="space-y-2">
                    <label htmlFor="billingCity" className="block text-gray-700 text-sm font-semibold">
                        <FaBuilding className="inline mr-2 text-blue-600" />
                        City
                    </label>
                    <input
                        type="text"
                        id="billingCity"
                        name="city"
                        value={shippingAddress.city}
                        onChange={handleShippingChange}
                        className="w-full py-3 px-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white"
                        placeholder="Enter city"
                        required={!shippingAddress.sameAsShipping}
                    />
                </div>
                <div className="space-y-2">
                    <label htmlFor="billingState" className="block text-gray-700 text-sm font-semibold">
                        <FaMapMarkerAlt className="inline mr-2 text-blue-600" />
                        State
                    </label>
                    <input
                        type="text"
                        id="billingState"
                        name="state"
                        value={shippingAddress.state}
                        onChange={handleShippingChange}
                        className="w-full py-3 px-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white"
                        placeholder="Enter state"
                        required={!shippingAddress.sameAsShipping}
                    />
                </div>
                <div className="space-y-2">
                    <label htmlFor="billingZip" className="block text-gray-700 text-sm font-semibold">
                        ZIP Code / Postal Code
                    </label>
                    <input
                        type="text"
                        id="billingZip"
                        name="zip"
                        value={shippingAddress.zip}
                        onChange={handleShippingChange}
                        className="w-full py-3 px-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white"
                        placeholder="Enter ZIP/Postal code"
                        required={!shippingAddress.sameAsShipping}
                    />
                </div>
                <div className="space-y-2">
                    <label htmlFor="billingCountry" className="block text-gray-700 text-sm font-semibold">
                        Country
                    </label>
                    <select
                        id="billingCountry"
                        name="country"
                        value={shippingAddress.country}
                        onChange={handleShippingChange}
                        className="w-full py-3 px-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white"
                        required={!shippingAddress.sameAsShipping}
                    >
                        <option value="">Select a country</option>
                        <option value="USA">United States</option>
                        <option value="Canada">Canada</option>
                        <option value="UK">United Kingdom</option>
                        <option value="AUS">Australia</option>
                    </select>
                </div>
            </div>
        </motion.div>
    )

    const renderStep = () => {
        switch (step) {
            case 1:
                return (
                    <motion.div variants={itemVariants} className="p-5 space-y-8">
                        {/* Shipping Address Section */}
                        <div className="space-y-6">
                            <div className="flex items-center gap-3 mb-6">
                                <div className="p-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl">
                                    {/* <FaTruckPlane className="w-6 h-6 text-white" /> */}
                                </div>
                                <h2 className="text-2xl font-bold text-gray-900">Shipping Address</h2>
                            </div>

                            {isLoadingAddresses ? (
                                <div className="flex justify-center py-12">
                                    <div className="animate-spin h-8 w-8 border-3 border-blue-500 border-t-transparent rounded-full" />
                                </div>
                            ) : addressError ? (
                                <div className="bg-red-50 border border-red-200 rounded-2xl p-6 text-red-700">
                                    <div className="flex items-center gap-3">
                                        <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                                            <span className="text-white text-sm">!</span>
                                        </div>
                                        {addressError}
                                    </div>
                                </div>
                            ) : customerAddresses.length > 0 ? (
                                <div className="space-y-6">
                                    <div className="grid gap-4">
                                        {customerAddresses.map((address) => (
                                            <motion.div
                                                key={address.id}
                                                whileHover={{ scale: 1.02 }}
                                                whileTap={{ scale: 0.98 }}
                                                onClick={() => {
                                                    const formattedAddress = {
                                                        name: address.contact_name,
                                                        street: address.address_line1,
                                                        street2: address.address_line2,
                                                        city: address.city,
                                                        state: address.state,
                                                        zip: address.postal_code,
                                                        country: address.country,
                                                        email: address.contact_email,
                                                        phone: address.contact_phone,
                                                        company: address.company_name,
                                                        instructions: address.delivery_instructions
                                                    };
                                                    setShippingAddress(formattedAddress);
                                                    if (billingAddress.sameAsShipping) {
                                                        setBillingAddress(prev => ({
                                                            ...prev,
                                                            ...formattedAddress
                                                        }));
                                                    }
                                                }}
                                                className={`relative border-2 p-6 rounded-2xl cursor-pointer transition-all duration-200 ${shippingAddress.name === address.contact_name
                                                    ? 'border-blue-500 bg-blue-50 shadow-lg'
                                                    : 'border-gray-200 hover:border-blue-300 hover:shadow-md bg-white'
                                                    }`}
                                            >
                                                {shippingAddress.name === address.contact_name && (
                                                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                                                        <FaCheckCircle className="w-4 h-4 text-white" />
                                                    </div>
                                                )}

                                                <div className="flex justify-between items-start mb-4">
                                                    <div className="flex items-center gap-3">
                                                        <FaUser className="text-blue-600" />
                                                        <span className="font-semibold text-gray-900">{address.contact_name}</span>
                                                    </div>
                                                    {address.is_default && (
                                                        <span className="bg-green-100 text-green-800 text-xs font-medium px-3 py-1 rounded-full">
                                                            Default
                                                        </span>
                                                    )}
                                                </div>

                                                <div className="space-y-2 text-gray-600">
                                                    {address.company_name && (
                                                        <div className="flex items-center gap-2">
                                                            <HiOfficeBuilding className="text-gray-400" />
                                                            <span>{address.company_name}</span>
                                                        </div>
                                                    )}
                                                    <div className="flex items-center gap-2">
                                                        <HiLocationMarker className="text-gray-400" />
                                                        <span>{address.address_line1}</span>
                                                    </div>
                                                    {address.address_line2 && (
                                                        <div className="ml-6 text-gray-500">{address.address_line2}</div>
                                                    )}
                                                    <div className="flex items-center gap-2">
                                                        <FaMapMarkerAlt className="text-gray-400" />
                                                        <span>{address.city}, {address.state} {address.postal_code}</span>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <span className="text-gray-500 font-medium">{address.country}</span>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <HiMail className="text-gray-400" />
                                                        <span>{address.contact_email}</span>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <HiPhone className="text-gray-400" />
                                                        <span>{address.contact_phone}</span>
                                                    </div>
                                                </div>
                                            </motion.div>
                                        ))}
                                    </div>

                                    <motion.button
                                        whileHover={{ scale: 1.02 }}
                                        whileTap={{ scale: 0.98 }}
                                        onClick={() => setShowNewShippingAddress(!showNewShippingAddress)}
                                        className="w-full py-4 border-2 border-dashed border-blue-300 text-blue-600 rounded-2xl hover:bg-blue-50 transition-all duration-200 font-medium"
                                    >
                                        {showNewShippingAddress ? "Hide New Address Form" : "Add New Address"}
                                    </motion.button>

                                    <AnimatePresence>
                                        {showNewShippingAddress && shippingAddressForm}
                                    </AnimatePresence>
                                </div>
                            ) : (
                                <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-2xl bg-gray-50">
                                    <FaMapMarkerAlt className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                    <div className="text-gray-500 mb-6 text-lg">No Addresses Found</div>
                                    <motion.button
                                        whileHover={{ scale: 1.05 }}
                                        whileTap={{ scale: 0.95 }}
                                        onClick={() => window.location.href = '/customer-admin/profile'}
                                        className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white px-6 py-3 rounded-xl hover:from-blue-600 hover:to-indigo-600 transition-all duration-200 font-medium shadow-lg"
                                    >
                                        Add Your First Address
                                    </motion.button>
                                </div>
                            )}
                        </div>

                        {/* Billing Address Section */}
                        <div className="space-y-6">
                            <div className="flex items-center gap-3 mb-6">
                                <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl">
                                    <BsShieldCheck className="w-6 h-6 text-white" />
                                </div>
                                <h2 className="text-2xl font-bold text-gray-900">Billing Address</h2>
                            </div>

                            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-200">
                                <label className="flex items-center gap-3 cursor-pointer">
                                    <input
                                        type="checkbox"
                                        name="sameAsShipping"
                                        checked={billingAddress.sameAsShipping || false}
                                        onChange={handleBillingChange}
                                        className="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                                    />
                                    <span className="text-gray-700 font-medium">Same as Shipping Address</span>
                                </label>
                            </div>

                            <AnimatePresence>
                                {!billingAddress.sameAsShipping && (
                                    <motion.div
                                        initial={{ opacity: 0, height: 0 }}
                                        animate={{ opacity: 1, height: 'auto' }}
                                        exit={{ opacity: 0, height: 0 }}
                                        className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-6 border border-gray-200"
                                    >
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            {['name', 'street', 'city', 'state', 'zip'].map(field => (
                                                <div key={field} className="space-y-2">
                                                    <label className="block text-gray-700 text-sm font-semibold capitalize">
                                                        {field === 'name' && <FaUser className="inline mr-2 text-blue-600" />}
                                                        {field === 'street' && <HiLocationMarker className="inline mr-2 text-blue-600" />}
                                                        {field === 'city' && <FaBuilding className="inline mr-2 text-blue-600" />}
                                                        {field === 'state' && <FaMapMarkerAlt className="inline mr-2 text-blue-600" />}
                                                        {field}
                                                    </label>
                                                    <input
                                                        type="text"
                                                        name={field}
                                                        value={billingAddress[field] || ''}
                                                        onChange={handleBillingChange}
                                                        className="w-full py-3 px-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white"
                                                        placeholder={`Enter ${field}`}
                                                        required
                                                    />
                                                </div>
                                            ))}
                                            <div className="space-y-2">
                                                <label className="block text-gray-700 text-sm font-semibold">Country</label>
                                                <select
                                                    name="country"
                                                    value={billingAddress.country || ''}
                                                    onChange={handleBillingChange}
                                                    className="w-full py-3 px-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white"
                                                    required
                                                >
                                                    <option value="">Select a country</option>
                                                    <option value="USA">United States</option>
                                                    <option value="Canada">Canada</option>
                                                    <option value="UK">United Kingdom</option>
                                                    <option value="AUS">Australia</option>
                                                </select>
                                            </div>
                                        </div>
                                    </motion.div>
                                )}
                            </AnimatePresence>
                        </div>

                        {/* Notes Section */}
                        <div className="space-y-6">
                            <div className="flex items-center gap-3 mb-6">
                                <div className="p-3 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl">
                                    <FaStickyNote className="w-6 h-6 text-white" />
                                </div>
                                <h2 className="text-2xl font-bold text-gray-900">Additional Notes</h2>
                            </div>

                            <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-6 border border-gray-200">
                                <textarea
                                    name="notes"
                                    value={notes}
                                    onChange={(e) => setNotes(e.target.value)}
                                    rows="4"
                                    className="w-full py-3 px-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white"
                                    placeholder="Add any additional notes or instructions for your order"
                                />
                            </div>
                        </div>


                        {/* Order Review Section */}
                        <div className="space-y-6">
                            <div className="flex items-center gap-3 mb-6">
                                <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl">
                                    <MdVerified className="w-6 h-6 text-white" />
                                </div>
                                <h2 className="text-2xl font-bold text-gray-900">Order Review</h2>
                            </div>

                            <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-3xl p-5 border border-gray-200 shadow-lg">
                                {/* Shipping Address Review */}
                                <div className="mb-8">
                                    <div className="flex items-center gap-4 mb-6">
                                        <div className="p-3 bg-blue-500 rounded-xl shadow-lg">
                                            {/* <FaTruckPlane className="w-6 h-6 text-white" /> */}
                                        </div>
                                        <h3 className="text-xl font-bold text-gray-900">Shipping Address</h3>
                                    </div>
                                    <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div className="flex items-center gap-3">
                                                <FaUser className="text-blue-500" />
                                                <div>
                                                    <span className="text-gray-500 text-sm">Name:</span>
                                                    <p className="font-semibold text-gray-900">{shippingAddress.name}</p>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-3">
                                                <HiLocationMarker className="text-blue-500" />
                                                <div>
                                                    <span className="text-gray-500 text-sm">Address:</span>
                                                    <p className="font-medium text-gray-700">{shippingAddress.street}</p>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-3">
                                                <FaMapMarkerAlt className="text-blue-500" />
                                                <div>
                                                    <span className="text-gray-500 text-sm">Location:</span>
                                                    <p className="font-medium text-gray-700">{shippingAddress.city}, {shippingAddress.state}</p>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-3">
                                                <span className="text-blue-500 font-bold">#</span>
                                                <div>
                                                    <span className="text-gray-500 text-sm">Postal Code:</span>
                                                    <p className="font-medium text-gray-700">{shippingAddress.zip}</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="mt-4 pt-4 border-t border-gray-100">
                                            <div className="flex items-center gap-3">
                                                <span className="text-blue-500 font-bold">🌍</span>
                                                <div>
                                                    <span className="text-gray-500 text-sm">Country:</span>
                                                    <p className="font-semibold text-gray-900">{shippingAddress.country}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Billing Address Review */}
                                <div>
                                    <div className="flex items-center gap-4 mb-6">
                                        <div className="p-3 bg-green-500 rounded-xl shadow-lg">
                                            <BsShieldCheck className="w-6 h-6 text-white" />
                                        </div>
                                        <h3 className="text-xl font-bold text-gray-900">Billing Address</h3>
                                    </div>
                                    <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                                        {billingAddress.sameAsShipping ? (
                                            <div className="flex items-center gap-3 text-green-700">
                                                <FaCheckCircle className="text-green-500" />
                                                <span className="font-medium">Same as shipping address</span>
                                            </div>
                                        ) : (
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div className="flex items-center gap-3">
                                                    <FaUser className="text-green-500" />
                                                    <div>
                                                        <span className="text-gray-500 text-sm">Name:</span>
                                                        <p className="font-semibold text-gray-900">{billingAddress.name}</p>
                                                    </div>
                                                </div>
                                                <div className="flex items-center gap-3">
                                                    <HiLocationMarker className="text-green-500" />
                                                    <div>
                                                        <span className="text-gray-500 text-sm">Address:</span>
                                                        <p className="font-medium text-gray-700">{billingAddress.street}</p>
                                                    </div>
                                                </div>
                                                <div className="flex items-center gap-3">
                                                    <FaMapMarkerAlt className="text-green-500" />
                                                    <div>
                                                        <span className="text-gray-500 text-sm">Location:</span>
                                                        <p className="font-medium text-gray-700">{billingAddress.city}, {billingAddress.state}</p>
                                                    </div>
                                                </div>
                                                <div className="flex items-center gap-3">
                                                    <span className="text-green-500 font-bold">#</span>
                                                    <div>
                                                        <span className="text-gray-500 text-sm">Postal Code:</span>
                                                        <p className="font-medium text-gray-700">{billingAddress.zip}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Additional Notes */}
                                {notes && (
                                    <div className="mt-8">
                                        <div className="flex items-center gap-4 mb-6">
                                            <div className="p-3 bg-yellow-500 rounded-xl shadow-lg">
                                                <FaStickyNote className="w-6 h-6 text-white" />
                                            </div>
                                            <h3 className="text-xl font-bold text-gray-900">Additional Notes</h3>
                                        </div>
                                        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                                            <p className="text-gray-700">{notes}</p>
                                        </div>
                                    </div>
                                )}

                            </div>

                            {/* Navigation Buttons */}
                            <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-6">
                                <motion.button
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                    onClick={nextStep}
                                    className="w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-2xl font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 flex items-center justify-center gap-3 shadow-lg text-sm sm:text-base"
                                >
                                    <span className="hidden sm:inline">Continue to Payment</span>
                                    <span className="sm:hidden">Next: Payment</span>
                                    <TiArrowRightThick className="w-4 h-4 sm:w-5 sm:h-5" />
                                </motion.button>
                            </div>
                        </div>
                    </motion.div>
                );
            case 2:
                return (
                    <motion.div variants={itemVariants} className="space-y-8">
                        {/* Payment Component */}
                        <Payment
                            paymentInstance={paymentInstance}
                            billingAddress={billingAddress}
                            shippingAddress={shippingAddress}
                            paymentInfo={paymentInfo}
                            notes={notes}
                        />

                        {/* Navigation */}
                        <div className="flex flex-col sm:flex-row justify-between items-center gap-4 p-2">
                            <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={prevStep}
                                className="w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 border-2 border-gray-300 rounded-2xl font-semibold text-gray-700 hover:bg-gray-50 transition-all duration-200 flex items-center justify-center gap-3 shadow-lg text-sm sm:text-base"
                            >
                                <TiArrowLeftThick className="w-4 h-4 sm:w-5 sm:h-5" />
                                <span className="hidden sm:inline">Back to Address</span>
                                <span className="sm:hidden">Back</span>
                            </motion.button>

                            {/* Security Badge */}
                            <div className="flex items-center gap-2 text-xs sm:text-sm text-gray-600 order-first sm:order-last">
                                <BsShieldCheck className="text-green-500 w-4 h-4" />
                                <span className="hidden sm:inline px-5">Secured by SSL encryption</span>
                                <span className="sm:hidden">SSL Secured</span>
                            </div>
                        </div>
                    </motion.div>
                );
            default:
                return null;
        }
    };



    return (
        <div className="min-h-screen bg-white">
            <ModularNavbar />
            <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 text-gray-600">
            <div className="container mx-auto px-4 py-8 max-w-6xl">
                {/* Header */}
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-center mb-8 sm:mb-12"
                >
                    <div className="flex flex-col sm:flex-row items-center justify-center gap-3 sm:gap-4 mb-4">
                        <div className="p-2 sm:p-3 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl sm:rounded-2xl shadow-lg">
                            {/* <FaCartShopping className="w-6 h-6 sm:w-8 sm:h-8 text-white" /> */}
                        </div>
                        <h1 className="text-2xl sm:text-4xl font-bold text-gray-900">Secure Checkout</h1>
                    </div>
                    <p className="text-gray-600 text-base sm:text-lg px-4 sm:px-0">Complete your order in just a few simple steps</p>
                </motion.div>

                {/* Progress Steps */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                    className="bg-white rounded-3xl shadow-xl border border-gray-100 p-4 sm:p-8 mb-8"
                >
                    <div className="flex items-center justify-between relative">
                        {/* Progress Line */}
                        <div className="absolute top-1/2 left-0 right-0 h-1 bg-gray-200 rounded-full -translate-y-1/2 z-0">
                            <div
                                className={`h-full bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full transition-all duration-500 ${step >= 2 ? 'w-full' : 'w-1/2'
                                    }`}
                            />
                        </div>

                        {/* Step 1: Address */}
                        <div className="flex flex-col items-center relative z-10">
                            <div className={`w-12 h-12 sm:w-16 sm:h-16 rounded-full flex items-center justify-center border-4 transition-all duration-300 ${step >= 1
                                ? 'bg-gradient-to-r from-blue-500 to-indigo-500 border-blue-500 text-white shadow-lg'
                                : 'bg-white border-gray-300 text-gray-400'
                                }`}>
                                {step > 1 ? (
                                    <FaCheckCircle className="w-5 h-5 sm:w-8 sm:h-8" />
                                ) : (
                                    <FaTruckPlane className="w-5 h-5 sm:w-8 sm:h-8" />
                                )}
                            </div>
                            <div className="mt-2 sm:mt-3 text-center">
                                <p className={`font-semibold text-xs sm:text-base ${step >= 1 ? 'text-gray-900' : 'text-gray-500'}`}>
                                    <span className="hidden sm:inline">Shipping & Billing</span>
                                    <span className="sm:hidden">Address</span>
                                </p>
                                <p className="text-xs sm:text-sm text-gray-500 hidden sm:block">Enter your address details</p>
                            </div>
                        </div>

                        {/* Step 2: Payment */}
                        <div className="flex flex-col items-center relative z-10">
                            <div className={`w-12 h-12 sm:w-16 sm:h-16 rounded-full flex items-center justify-center border-4 transition-all duration-300 ${step >= 2
                                ? 'bg-gradient-to-r from-blue-500 to-indigo-500 border-blue-500 text-white shadow-lg'
                                : 'bg-white border-gray-300 text-gray-400'
                                }`}>
                                <BsCreditCard2FrontFill className="w-5 h-5 sm:w-8 sm:h-8" />
                            </div>
                            <div className="mt-2 sm:mt-3 text-center">
                                <p className={`font-semibold text-xs sm:text-base ${step >= 2 ? 'text-gray-900' : 'text-gray-500'}`}>
                                    Payment
                                </p>
                                <p className="text-xs sm:text-sm text-gray-500 hidden sm:block">Secure payment processing</p>
                            </div>
                        </div>
                    </div>
                </motion.div>

                {/* Main Content */}
                <motion.div
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                    className="bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden"
                >
                    <AnimatePresence mode='wait'>
                        {renderStep()}
                    </AnimatePresence>
                </motion.div>
            </div>
            </div>
            <Footer />
        </div>
    );
};

export default Checkout;
