"use client";

import React, { useState, useEffect } from 'react';
import { FaPlus, FaMinus, FaShoppingCart, FaTrash, FaTag, FaBoxOpen, FaTruck, FaTimes, FaSave } from 'react-icons/fa';
import { MdCheckCircleOutline, MdLocalOffer, MdShoppingBasket, MdSaveAs } from 'react-icons/md';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-hot-toast';
import { useRouter } from 'next/navigation';

const GST_RATE = 0.10; // 10% GST rate
const SERVICE_CHARGE_RATE = 0.01; // 1% Service charge rate

const CartPageWithData = () => {
    const router = useRouter();

    const [cartData, setCartData] = useState({});
    const [productData, setProductData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [totalPrice, setTotalPrice] = useState(0);
    const [totalRegularPrice, setTotalRegularPrice] = useState(0);
    const [updatingItems, setUpdatingItems] = useState(new Set());
    const [isCustomInput, setIsCustomInput] = useState(false);
    const [customInputTimeout, setCustomInputTimeout] = useState(null);
    const [showClearSuccess, setShowClearSuccess] = useState(false);

    // New state for local cart management
    const [localCartItems, setLocalCartItems] = useState([]);
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [calPacks, setCalPacks] = useState({});

    // Check for authentication tokens
    useEffect(() => {
        const checkAuth = () => {
            const userToken = localStorage.getItem('userAuthToken');
            const partnerToken = localStorage.getItem('partnerAuthToken');
            const superAdminToken = localStorage.getItem('superAdminAuthToken');

            if (userToken) {
                return userToken;
            } else if (partnerToken) {
                return partnerToken;
            } else if (superAdminToken) {
                return superAdminToken;
            }
            return null;
        };

        const token = checkAuth();
        if (!token) {
            // Let the middleware handle the smart redirect by redirecting to cart
            // This will trigger middleware which will redirect to appropriate login page
            window.location.href = '/cart';
            return;
        }

        fetchCartData(token);
    }, []);

    // Fetch cart data
    const fetchCartData = async (token) => {
        try {
            setLoading(true);
            const cartId = localStorage.getItem('cartId');

            if (!cartId) {
                setError('No cart found. Please try adding items to cart.');
                setLoading(false);
                return;
            }

            const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/carts/${cartId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            });

            const data = await response.json();
            console.log(data);
            if (data.success) {
                setCartData(data.data.cart);
                setLocalCartItems(data.data.cart.items || []);
                setHasUnsavedChanges(false);
            } else {
                setError('Failed to fetch cart data');
            }
        } catch (error) {
            console.error('Error fetching cart:', error);
            setError('Failed to fetch cart data');
        } finally {
            setLoading(false);
        }
    };

    // Update cart item (this function is kept for backward compatibility but not used in the new flow)
    const updateCartItem = async (itemId, quantity) => {
        try {
            setUpdatingItems(prev => new Set([...prev, itemId]));

            const token = localStorage.getItem('userAuthToken') ||
                localStorage.getItem('partnerAuthToken') ||
                localStorage.getItem('superAdminAuthToken');
            const cartId = localStorage.getItem('cartId');

            const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/carts/${cartId}/items/${itemId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    quantity: quantity
                })
            });

            const data = await response.json();
            if (data.success) {
                fetchCartData(token);
            } else {
                setError('Failed to update cart item');
            }
        } catch (error) {
            console.error('Error updating cart item:', error);
            setError('Failed to update cart item');
        } finally {
            setUpdatingItems(prev => {
                const newSet = new Set(prev);
                newSet.delete(itemId);
                return newSet;
            });
        }
    };

    // Helper function to get the correct item ID for API calls
    const getItemId = (item) => {
        // For cart operations, we use the cart item ID (item.id)
        return item.id;
    };

    // Helper function to get the product ID for delete operations (deprecated - now using item ID)
    const getProductId = (item) => {
        // For delete operations, we need the product_id converted to number
        return parseInt(item.product_id);
    };

    // Delete cart item - send the item_id (cart item ID) of the clicked item
    const handleDeleteItem = async (item) => {
        try {
            // Debug: Log the entire item to see its structure
            // console.log('Full item object:', item);
            // console.log('Item product_id:', item.product_id);
            // console.log('Item id:', item.id);

            // Get the item_id (cart item ID) from the clicked item
            const itemId = getItemId(item);
            // console.log('Cart item ID for deletion:', itemId);

            const token = localStorage.getItem('userAuthToken') ||
                localStorage.getItem('partnerAuthToken') ||
                localStorage.getItem('superAdminAuthToken');
            const cartId = localStorage.getItem('cartId');

            // console.log('Cart ID:', cartId);
            // console.log('Token exists:', !!token);

            const deleteUrl = `${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/carts/${cartId}/items/${itemId}`;
            console.log('Delete URL:', deleteUrl);

            // Send delete request with the item_id (cart item ID)
            const response = await fetch(deleteUrl, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            });

            // console.log('Delete response status:', response.status);
            // console.log('Delete response ok:', response.ok);

            // Check if response is successful based on HTTP status
            if (response.ok) {
                // Try to parse JSON, but handle cases where response might be empty
                let data = {};
                try {
                    const responseText = await response.text();
                    console.log('Delete response text:', responseText);
                    if (responseText) {
                        data = JSON.parse(responseText);
                    }
                } catch (parseError) {
                    console.log('Response parsing error:', parseError);
                }

                console.log('Delete response data:', data);

                // Remove the item from local state immediately using item ID
                setLocalCartItems(prevItems =>
                    prevItems.filter(prevItem => getItemId(prevItem) !== itemId)
                );
                // Refresh cart data from server
                fetchCartData(token);
                toast.success('Item removed from cart');
            } else {
                // Handle HTTP error responses
                let errorData = {};
                try {
                    errorData = await response.json();
                } catch (parseError) {
                    console.log('Error response parsing failed:', parseError);
                }
                console.error('Delete failed with status:', response.status, 'data:', errorData);
                setError(`Failed to remove item from cart (${response.status})`);
            }
        } catch (error) {
            console.error('Error removing item from cart:', error);
            setError('Failed to remove item from cart');
        }
    };

    // Local quantity update handlers (no immediate API calls, no restrictions)
    const updateLocalQuantity = (itemId, newQuantity) => {
        // Allow any number input, including 0 and negative numbers
        const quantity = parseInt(newQuantity, 10) || 0;
        setLocalCartItems(prevItems =>
            prevItems.map(item =>
                getItemId(item) === itemId
                    ? { ...item, quantity: quantity }
                    : item
            )
        );
        setHasUnsavedChanges(true);
    };

    // Update quantity handlers
    const handleQuantityChange = (itemId, newQuantity) => {
        updateLocalQuantity(itemId, newQuantity);
    };

    const incrementQuantity = (itemId) => {
        const currentItem = localCartItems.find(item => getItemId(item) === itemId);
        if (currentItem) {
            updateLocalQuantity(itemId, currentItem.quantity + 1);
        }
    };

    const decrementQuantity = (itemId) => {
        const currentItem = localCartItems.find(item => getItemId(item) === itemId);
        if (currentItem) {
            // Allow decrementing to 0 or negative numbers - no restrictions
            updateLocalQuantity(itemId, currentItem.quantity - 1);
        }
    };

    // Save cart changes to server
    const saveCartChanges = async () => {
        if (!hasUnsavedChanges) {
            toast.info("No changes to save");
            return;
        }

        // Validate quantities - check for zero or negative numbers
        const invalidItems = localCartItems.filter(item => item.quantity <= 0);

        if (invalidItems.length > 0) {
            // Show error message
            toast.error("Negative numbers and zero are not allowed. At least 1 item is required.");

            // Auto-correct invalid quantities to 1
            setLocalCartItems(prevItems =>
                prevItems.map(item =>
                    item.quantity <= 0
                        ? { ...item, quantity: 1 }
                        : item
                )
            );

            // Keep the unsaved changes flag since we corrected the values
            return;
        }

        try {
            setIsSaving(true);
            const token = localStorage.getItem('userAuthToken') ||
                localStorage.getItem('partnerAuthToken') ||
                localStorage.getItem('superAdminAuthToken');
            const cartId = localStorage.getItem('cartId');

            // Find items that have changed quantities
            const changedItems = localCartItems.filter(localItem => {
                const originalItem = cartData.items.find(item => getItemId(item) === getItemId(localItem));
                return originalItem && originalItem.quantity !== localItem.quantity;
            });

            // Update each changed item using cart item ID
            const updatePromises = changedItems.map(item => {
                const itemId = getItemId(item);
                console.log('Updating cart item ID:', itemId, 'with quantity:', item.quantity);
                return fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/carts/${cartId}/items/${itemId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        quantity: item.quantity
                    })
                });
            });

            const responses = await Promise.all(updatePromises);
            const allSuccessful = responses.every(response => response.ok);

            if (allSuccessful) {
                // Refresh cart data from server
                await fetchCartData(token);
                toast.success("Cart saved successfully!");
            } else {
                throw new Error("Some updates failed");
            }
        } catch (error) {
            console.error('Error saving cart:', error);
            toast.error("Failed to save cart changes");
        } finally {
            setIsSaving(false);
        }
    };

    const handleCheckout = () => {
        if (hasUnsavedChanges) {
            toast.error("Please save your cart changes before checkout");
            return;
        }

        if (cartData.items.length === 0) {
            toast.error("Your cart is empty");
            return;
        }

        // Proceed to checkout
        router.push('/checkout');
    };

    // Create new empty cart
    const createEmptyCart = async (token) => {
        try {
            const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/carts`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            });

            const data = await response.json();
            if (data.success) {
                // Store new cart ID in localStorage
                localStorage.setItem('cartId', data.data.cart.id);
                return data.data.cart;
            } else {
                throw new Error('Failed to create new cart');
            }
        } catch (error) {
            console.error('Error creating new cart:', error);
            throw error;
        }
    };

    // Clear cart
    const clearCart = async () => {
        try {
            const token = localStorage.getItem('userAuthToken') ||
                localStorage.getItem('partnerAuthToken') ||
                localStorage.getItem('superAdminAuthToken');
            const cartId = localStorage.getItem('cartId');

            // First delete the existing cart
            const deleteResponse = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/carts/${cartId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            });

            const deleteData = await deleteResponse.json();
            if (deleteData.success) {
                // Create a new empty cart
                const newCart = await createEmptyCart(token);

                // Update cart data with new empty cart
                setCartData({
                    ...newCart,
                    items: [],
                    subtotal: "0.00",
                    discount_amount: "0.00",
                    tax_amount: "0.00",
                    shipping_amount: "0.00",
                    total_amount: "0.00",
                    currency: {
                        code: "AUD",
                        symbol: "$"
                    }
                });

                // Reset local state
                setLocalCartItems([]);
                setHasUnsavedChanges(false);

                toast.success('Cart cleared successfully');
            } else {
                setError('Failed to clear cart');
            }
        } catch (error) {
            console.error('Error clearing cart:', error);
            setError('Failed to clear cart');
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-white flex items-center justify-center">
                <motion.div
                    animate={{ rotate: 360 }}
                    transition={{
                        duration: 1,
                        repeat: Infinity,
                        ease: "linear"
                    }}
                    className="w-12 h-12 border-3 border-orange-400 border-t-transparent rounded-full"
                />
            </div>
        );
    }

    return (
        <div className="bg-gradient-to-b from-gray-50 to-gray-100 py-6 sm:py-8 md:py-12 text-gray-700 min-h-screen">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="text-center mb-6 sm:mb-8 md:mb-10"
                >
                    <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 flex items-center justify-center gap-3" tabIndex="0">
                        <FaShoppingCart className="text-orange-400" aria-hidden="true" />
                        <span>Your Shopping Cart</span>
                    </h1>
                </motion.div>

                {error && (
                    <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                        {error}
                    </div>
                )}

                {showClearSuccess && (
                    <motion.div
                        initial={{ opacity: 0, y: -20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 flex items-center justify-between"
                    >
                        <div className="flex items-center gap-2">
                            <MdCheckCircleOutline className="text-xl" />
                            <span>Cart cleared successfully</span>
                        </div>
                        <button
                            onClick={() => setShowClearSuccess(false)}
                            className="text-green-700 hover:text-green-900"
                        >
                            <FaTimes className="text-lg" />
                        </button>
                    </motion.div>
                )}

                {cartData.items && cartData.items.length === 0 ? (
                    <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.5 }}
                        className="text-center py-12"
                    >
                        <FaBoxOpen className="mx-auto text-6xl text-orange-400 mb-4" />
                        <h2 className="text-2xl font-semibold mb-4">Your cart is empty</h2>
                        <p className="text-gray-600 mb-8">Looks like you haven't added anything to your cart yet.</p>
                        <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            className="bg-orange-400 text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-500 transition-colors"
                            onClick={() => router.push('/products')}
                        >
                            Start Shopping
                        </motion.button>
                    </motion.div>
                ) : (
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div className="lg:col-span-2 space-y-6">
                            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                                <div className="p-4 border-b border-gray-100 flex justify-between items-center">
                                    <h3 className="text-lg font-semibold text-gray-800">Cart Items</h3>
                                    <div className="flex items-center gap-3">
                                        {hasUnsavedChanges && (
                                            <motion.button
                                                whileHover={{ scale: 1.02 }}
                                                whileTap={{ scale: 0.98 }}
                                                onClick={saveCartChanges}
                                                disabled={isSaving}
                                                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                            >
                                                {isSaving ? (
                                                    <motion.div
                                                        animate={{ rotate: 360 }}
                                                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                                                        className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                                                    />
                                                ) : (
                                                    <FaSave className="text-sm" />
                                                )}
                                                {isSaving ? 'Saving...' : 'Save Changes'}
                                            </motion.button>
                                        )}
                                        {cartData.items && cartData.items.length > 0 && (
                                            <motion.button
                                                whileHover={{ scale: 1.02 }}
                                                whileTap={{ scale: 0.98 }}
                                                onClick={clearCart}
                                                className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
                                            >
                                                <FaTrash className="text-sm" />
                                                Clear Cart
                                            </motion.button>
                                        )}
                                    </div>
                                </div>
                                <ul className="divide-y divide-gray-100">
                                    <AnimatePresence mode="popLayout">
                                        {localCartItems && localCartItems.map((item) => {
                                            const itemId = getItemId(item);
                                            const isUpdating = updatingItems.has(itemId);
                                            const hasChanged = cartData.items.find(originalItem =>
                                                getItemId(originalItem) === itemId && originalItem.quantity !== item.quantity
                                            );
                                            return (
                                                <motion.li
                                                    key={itemId}
                                                    initial={{ opacity: 0, x: -20 }}
                                                    animate={{ opacity: 1, x: 0 }}
                                                    exit={{ opacity: 0, x: 20, height: 0, marginTop: 0, marginBottom: 0, padding: 0 }}
                                                    transition={{ duration: 0.3 }}
                                                    className={`p-4 sm:p-6 hover:bg-gray-50 transition-colors relative ${hasChanged ? 'border-l-4 border-orange-400 bg-orange-50' : ''
                                                        }`}
                                                >
                                                    <div className="flex flex-col sm:flex-row gap-4 sm:items-center">
                                                        <div className="w-full sm:w-32 md:w-40 lg:w-48">
                                                            <motion.div
                                                                whileHover={{ scale: 1.05 }}
                                                                className="relative rounded-lg overflow-hidden bg-gray-100"
                                                            >
                                                                {item.product.images && item.product.images[0] ? (
                                                                    <img
                                                                        src={item.product.images[0].image_url}
                                                                        alt={item.product.images[0].alt_text}
                                                                        className="w-full h-32 object-cover"
                                                                    />
                                                                ) : (
                                                                    <div className="w-full h-32 flex items-center justify-center text-gray-400">
                                                                        No Image Available
                                                                    </div>
                                                                )}
                                                            </motion.div>
                                                        </div>

                                                        <div className="flex-grow space-y-2 w-full">
                                                            <div className="flex justify-between items-start">
                                                                <div className="flex items-center gap-2">
                                                                    <h3 className="text-lg sm:text-xl font-semibold text-gray-800">
                                                                        {item.product.name}
                                                                    </h3>
                                                                    {hasChanged && (
                                                                        <span className="text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded-full">
                                                                            Unsaved
                                                                        </span>
                                                                    )}
                                                                </div>
                                                                <motion.button
                                                                    whileHover={{ scale: 1.1 }}
                                                                    whileTap={{ scale: 0.9 }}
                                                                    onClick={() => handleDeleteItem(item)}
                                                                    className="p-2 text-red-500 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors"
                                                                >
                                                                    <FaTrash className="text-2xl" />
                                                                </motion.button>
                                                            </div>

                                                            <div className="flex items-center gap-2">
                                                                <motion.button
                                                                    whileHover={{ scale: 1.1 }}
                                                                    whileTap={{ scale: 0.9 }}
                                                                    className="p-2 bg-gray-100 hover:bg-gray-200 rounded-full"
                                                                    onClick={() => decrementQuantity(itemId)}
                                                                    disabled={isCustomInput}
                                                                >
                                                                    <FaMinus className="text-xs" />
                                                                </motion.button>
                                                                <div className="relative">
                                                                    <input
                                                                        type="number"
                                                                        className={`w-16 text-center border rounded p-1 focus:ring-2 focus:ring-orange-300 focus:border-orange-300 ${hasChanged ? 'border-orange-300 bg-orange-50' : 'border-gray-200'
                                                                            }`}
                                                                        value={item.quantity}
                                                                        onChange={(e) => handleQuantityChange(itemId, e.target.value)}
                                                                    />
                                                                    {isCustomInput && (
                                                                        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75">
                                                                            <motion.div
                                                                                animate={{
                                                                                    rotate: 360
                                                                                }}
                                                                                transition={{
                                                                                    duration: 1,
                                                                                    repeat: Infinity,
                                                                                    ease: "linear"
                                                                                }}
                                                                                className="w-5 h-5 border-2 border-orange-400 border-t-transparent rounded-full"
                                                                            />
                                                                        </div>
                                                                    )}
                                                                </div>
                                                                <motion.button
                                                                    whileHover={{ scale: 1.1 }}
                                                                    whileTap={{ scale: 0.9 }}
                                                                    className="p-2 bg-gray-100 hover:bg-gray-200 rounded-full"
                                                                    onClick={() => incrementQuantity(itemId)}
                                                                    disabled={isCustomInput}
                                                                >
                                                                    <FaPlus className="text-xs" />
                                                                </motion.button>
                                                            </div>
                                                        </div>

                                                        <div className="text-right space-y-1">
                                                            {parseFloat(item.discount_amount) > 0 && (
                                                                <div className="text-sm">
                                                                    <del className="text-gray-400">
                                                                        {cartData.currency.symbol}{item.unit_price}
                                                                    </del>
                                                                    <span className="ml-2 text-orange-500">
                                                                        {cartData.currency.symbol}{item.total_amount}
                                                                    </span>
                                                                </div>
                                                            )}
                                                            <div className="text-lg font-semibold text-gray-800 relative">
                                                                {isUpdating ? (
                                                                    <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75">
                                                                        <motion.div
                                                                            animate={{
                                                                                rotate: 360
                                                                            }}
                                                                            transition={{
                                                                                duration: 1,
                                                                                repeat: Infinity,
                                                                                ease: "linear"
                                                                            }}
                                                                            className="w-5 h-5 border-2 border-orange-400 border-t-transparent rounded-full"
                                                                        />
                                                                    </div>
                                                                ) : null}
                                                                {cartData.currency.symbol}{item.total_amount}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </motion.li>
                                            );
                                        })}
                                    </AnimatePresence>
                                </ul>
                            </div>
                        </div>

                        <div className="lg:col-span-1">
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                className="bg-white rounded-xl shadow-lg p-6 sticky top-6"
                            >
                                <h2 className="text-xl font-semibold mb-6 flex items-center gap-2">
                                    <MdCheckCircleOutline className="text-orange-400" />
                                    Order Summary
                                </h2>

                                {hasUnsavedChanges && (
                                    <div className="mb-4 p-3 bg-orange-100 border border-orange-300 rounded-lg">
                                        <div className="flex items-center gap-2 text-orange-700">
                                            <FaSave className="text-sm" />
                                            <span className="text-sm font-medium">You have unsaved changes</span>
                                        </div>
                                        <p className="text-xs text-orange-600 mt-1">
                                            Save your changes before proceeding to checkout
                                        </p>
                                    </div>
                                )}

                                <div className="space-y-4">
                                    <div className="flex justify-between text-sm">
                                        <span>Subtotal ({cartData.items.length} items)</span>
                                        <span>{cartData.currency.symbol}{cartData.subtotal}</span>
                                    </div>
                                    {parseFloat(cartData.discount_amount) > 0 && (
                                        <div className="flex justify-between text-sm text-green-600">
                                            <span>Discount</span>
                                            <span>-{cartData.currency.symbol}{cartData.discount_amount}</span>
                                        </div>
                                    )}
                                    {parseFloat(cartData.tax_amount) > 0 && (
                                        <div className="flex justify-between text-sm">
                                            <span>Tax</span>
                                            <span>{cartData.currency.symbol}{cartData.tax_amount}</span>
                                        </div>
                                    )}
                                    {parseFloat(cartData.shipping_amount) > 0 && (
                                        <div className="flex justify-between text-sm">
                                            <span>Shipping</span>
                                            <span>{cartData.currency.symbol}{cartData.shipping_amount}</span>
                                        </div>
                                    )}
                                    <div className="border-t pt-4">
                                        <div className="flex justify-between font-semibold text-lg">
                                            <span>Total</span>
                                            <span>{cartData.currency.symbol}{cartData.total_amount}</span>
                                        </div>
                                    </div>

                                    <motion.button
                                        whileHover={{ scale: hasUnsavedChanges ? 1 : 1.02 }}
                                        whileTap={{ scale: hasUnsavedChanges ? 1 : 0.98 }}
                                        className={`w-full py-3 rounded-lg font-semibold text-white flex items-center justify-center gap-2 transition-colors ${hasUnsavedChanges
                                            ? 'bg-gray-400 cursor-not-allowed'
                                            : 'bg-orange-400 hover:bg-orange-500'
                                            }`}
                                        onClick={handleCheckout}
                                        disabled={hasUnsavedChanges}
                                    >
                                        <MdCheckCircleOutline />
                                        {hasUnsavedChanges ? 'Save Changes First' : 'Proceed to Pay'}
                                    </motion.button>
                                </div>
                            </motion.div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default CartPageWithData;
