'use client';

import { useEffect, useState } from 'react';
import { 
  CheckCircleIcon, ExclamationTriangleIcon, 
  InformationCircleIcon, XCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

export const toastTypes = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
};

export default function Toast({ message, type = toastTypes.INFO, duration = 3000, onClose }) {
  const [visible, setVisible] = useState(true);
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(false);
      setTimeout(() => {
        onClose && onClose();
      }, 300);
    }, duration);
    
    return () => clearTimeout(timer);
  }, [duration, onClose]);
  
  const getToastIcon = () => {
    switch (type) {
      case toastTypes.SUCCESS:
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case toastTypes.ERROR:
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case toastTypes.WARNING:
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      case toastTypes.INFO:
      default:
        return <InformationCircleIcon className="h-5 w-5 text-blue-500" />;
    }
  };
  
  const getToastClasses = () => {
    switch (type) {
      case toastTypes.SUCCESS:
        return 'bg-green-50 border-green-200';
      case toastTypes.ERROR:
        return 'bg-red-50 border-red-200';
      case toastTypes.WARNING:
        return 'bg-yellow-50 border-yellow-200';
      case toastTypes.INFO:
      default:
        return 'bg-blue-50 border-blue-200';
    }
  };
  
  return (
    <div 
      className={`fixed top-4 right-4 z-50 max-w-md transform transition-transform duration-300 ease-in-out ${
        visible ? 'translate-x-0' : 'translate-x-full'
      }`}
    >
      <div className={`rounded-lg shadow-md p-4 border ${getToastClasses()} flex items-start`}>
        <div className="flex-shrink-0 mr-3">
          {getToastIcon()}
        </div>
        
        <div className="flex-1 mr-2">
          <p className="text-sm font-medium text-gray-900">{message}</p>
        </div>
        
        <button
          onClick={() => {
            setVisible(false);
            setTimeout(() => {
              onClose && onClose();
            }, 300);
          }}
          className="flex-shrink-0 text-gray-400 hover:text-gray-600 focus:outline-none"
        >
          <XMarkIcon className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
}
