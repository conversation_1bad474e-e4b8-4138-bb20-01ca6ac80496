'use client';

import { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { FaTimes, FaShoppingCart, FaUser, FaUserTie, FaSignInAlt, FaUserPlus } from 'react-icons/fa';
import { MdSecurity, MdLogin } from 'react-icons/md';

export default function AuthRequiredPopup({ isOpen, onClose }) {
  const [isVisible, setIsVisible] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
    }
  }, [isOpen]);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => {
      onClose();
    }, 300);
  };

  const handleSignIn = (userType) => {
    handleClose();
    setTimeout(() => {
      // Capture current URL and search params for redirect
      const currentUrl = window.location.pathname + window.location.search;
      const redirectUrl = encodeURIComponent(currentUrl);
      router.push(`/login/${userType}?redirect=${redirectUrl}`);
    }, 300);
  };

  const handleSignUp = (userType) => {
    handleClose();
    setTimeout(() => {
      // Capture current URL and search params for redirect
      const currentUrl = window.location.pathname + window.location.search;
      const redirectUrl = encodeURIComponent(currentUrl);
      router.push(`/signup/${userType}?redirect=${redirectUrl}`);
    }, 300);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className={`absolute inset-0 bg-black transition-opacity duration-300 ${
          isVisible ? 'opacity-50' : 'opacity-0'
        }`}
        onClick={handleClose}
      />
      
      {/* Popup */}
      <div 
        className={`relative bg-white rounded-2xl shadow-2xl max-w-lg w-full mx-4 transform transition-all duration-300 ${
          isVisible ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
        }`}
      >
        {/* Close Button */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors duration-200 z-10"
        >
          <FaTimes className="w-5 h-5" />
        </button>

        {/* Header with gradient background */}
        <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-t-2xl p-6 text-center">
          <div className="flex justify-center mb-4">
            <div className="bg-white bg-opacity-20 rounded-full p-3">
              <FaShoppingCart className="w-8 h-8 text-white" />
            </div>
          </div>
          <h2 className="text-2xl font-bold text-white mb-2">
            Authentication Required
          </h2>
          <p className="text-orange-100 text-sm">
            Please sign in to add items to your cart
          </p>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="text-center mb-6">
            <div className="flex justify-center mb-4">
              <div className="bg-orange-100 rounded-full p-3">
                <MdSecurity className="w-6 h-6 text-orange-600" />
              </div>
            </div>
            
            <h3 className="text-xl font-semibold text-gray-900 mb-3">
              Sign In or Create Account
            </h3>
            
            <p className="text-gray-700 leading-relaxed mb-6">
              To add items to your cart and make purchases, you need to be signed in to your account. Choose your account type below:
            </p>
          </div>

          {/* Account Type Options */}
          <div className="space-y-4 mb-6">
            {/* Customer/Buyer Account */}
            <div className="border-2 border-gray-200 rounded-lg p-4 hover:border-orange-300 transition-colors duration-200">
              <div className="flex items-center mb-3">
                <div className="bg-orange-100 rounded-full p-2 mr-3">
                  <FaUser className="w-4 h-4 text-orange-600" />
                </div>
                <h4 className="font-semibold text-gray-900">Customer Account</h4>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                For individual buyers and customers
              </p>
              <div className="flex gap-2">
                <button
                  onClick={() => handleSignIn('user')}
                  className="flex-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white py-2 px-4 rounded-lg font-medium hover:from-orange-600 hover:to-orange-700 transition-all duration-300 flex items-center justify-center space-x-2 text-sm"
                >
                  <FaSignInAlt className="w-4 h-4" />
                  <span>Sign In</span>
                </button>
                <button
                  onClick={() => handleSignUp('user')}
                  className="flex-1 border-2 border-orange-500 text-orange-600 py-2 px-4 rounded-lg font-medium hover:bg-orange-50 transition-all duration-300 flex items-center justify-center space-x-2 text-sm"
                >
                  <FaUserPlus className="w-4 h-4" />
                  <span>Sign Up</span>
                </button>
              </div>
            </div>

            {/* Partner/Vendor Account */}
            <div className="border-2 border-gray-200 rounded-lg p-4 hover:border-orange-300 transition-colors duration-200">
              <div className="flex items-center mb-3">
                <div className="bg-orange-100 rounded-full p-2 mr-3">
                  <FaUserTie className="w-4 h-4 text-orange-600" />
                </div>
                <h4 className="font-semibold text-gray-900">Partner Account</h4>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                For business partners and vendors
              </p>
              <div className="flex gap-2">
                <button
                  onClick={() => handleSignIn('partner')}
                  className="flex-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white py-2 px-4 rounded-lg font-medium hover:from-orange-600 hover:to-orange-700 transition-all duration-300 flex items-center justify-center space-x-2 text-sm"
                >
                  <FaSignInAlt className="w-4 h-4" />
                  <span>Sign In</span>
                </button>
                <button
                  onClick={() => handleSignUp('partner')}
                  className="flex-1 border-2 border-orange-500 text-orange-600 py-2 px-4 rounded-lg font-medium hover:bg-orange-50 transition-all duration-300 flex items-center justify-center space-x-2 text-sm"
                >
                  <FaUserPlus className="w-4 h-4" />
                  <span>Sign Up</span>
                </button>
              </div>
            </div>
          </div>

          {/* Close Button */}
          <div className="flex justify-center">
            <button
              onClick={handleClose}
              className="px-6 py-2 border-2 border-gray-300 text-gray-700 rounded-lg font-medium hover:border-orange-500 hover:text-orange-600 transition-all duration-300"
            >
              Cancel
            </button>
          </div>
        </div>

        {/* Decorative elements */}
        <div className="absolute -top-2 -right-2 w-4 h-4 bg-orange-400 rounded-full opacity-60"></div>
        <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-orange-300 rounded-full opacity-40"></div>
      </div>
    </div>
  );
}
