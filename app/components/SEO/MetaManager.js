'use client';

import { useState, useEffect } from 'react';
import { 
    DocumentTextIcon, 
    PencilIcon, 
    CheckIcon,
    XMarkIcon,
    MagnifyingGlassIcon,
    ExclamationTriangleIcon,
    CheckCircleIcon
} from '@heroicons/react/24/outline';

export default function MetaManager() {
    const [searchTerm, setSearchTerm] = useState('');
    const [filterType, setFilterType] = useState('all');
    const [editingId, setEditingId] = useState(null);
    const [metaData, setMetaData] = useState([
        {
            id: 1,
            page: '/products/electronics',
            type: 'Product Category',
            title: 'Electronics - Wholesale B2B Marketplace | Diptouch',
            description: 'Browse our comprehensive electronics catalog. Wholesale prices on smartphones, laptops, accessories and more. Bulk ordering available.',
            keywords: 'electronics, wholesale electronics, bulk electronics, B2B electronics',
            status: 'optimized',
            lastUpdated: '2024-01-15',
            titleLength: 52,
            descriptionLength: 128
        },
        {
            id: 2,
            page: '/products/smartphones',
            type: 'Product',
            title: 'Wholesale Smartphones - Bulk Mobile Phones',
            description: 'Premium smartphones at wholesale prices. Latest models from top brands. Perfect for retailers and distributors.',
            keywords: 'wholesale smartphones, bulk mobile phones, B2B phones',
            status: 'needs-attention',
            lastUpdated: '2024-01-10',
            titleLength: 45,
            descriptionLength: 115
        },
        {
            id: 3,
            page: '/categories',
            type: 'Category Listing',
            title: 'Product Categories',
            description: 'Browse categories',
            keywords: 'categories',
            status: 'poor',
            lastUpdated: '2023-12-20',
            titleLength: 18,
            descriptionLength: 17
        },
        {
            id: 4,
            page: '/products/laptops/gaming-laptop-pro',
            type: 'Product',
            title: 'Gaming Laptop Pro - High Performance Gaming Computer | Diptouch B2B',
            description: 'Professional gaming laptop with RTX 4080, Intel i9 processor, 32GB RAM. Perfect for gaming centers, offices, and resellers. Bulk pricing available.',
            keywords: 'gaming laptop, high performance laptop, RTX 4080 laptop, bulk gaming computers',
            status: 'optimized',
            lastUpdated: '2024-01-18',
            titleLength: 68,
            descriptionLength: 145
        },
        {
            id: 5,
            page: '/brands/apple',
            type: 'Brand',
            title: 'Apple Products - Wholesale Apple Devices | Diptouch',
            description: 'Authorized Apple products at wholesale prices. iPhones, iPads, MacBooks, and accessories. Bulk ordering for businesses and retailers.',
            keywords: 'Apple wholesale, bulk Apple products, wholesale iPhones, wholesale iPads',
            status: 'optimized',
            lastUpdated: '2024-01-16',
            titleLength: 50,
            descriptionLength: 132
        }
    ]);

    const [editForm, setEditForm] = useState({
        title: '',
        description: '',
        keywords: ''
    });

    const statusColors = {
        optimized: 'bg-green-100 text-green-800',
        'needs-attention': 'bg-yellow-100 text-yellow-800',
        poor: 'bg-red-100 text-red-800'
    };

    const statusIcons = {
        optimized: <CheckCircleIcon className="h-4 w-4" />,
        'needs-attention': <ExclamationTriangleIcon className="h-4 w-4" />,
        poor: <XMarkIcon className="h-4 w-4" />
    };

    const getOptimizationScore = (item) => {
        let score = 0;
        
        // Title optimization (30 points)
        if (item.titleLength >= 30 && item.titleLength <= 60) score += 30;
        else if (item.titleLength >= 20 && item.titleLength <= 70) score += 20;
        else score += 10;
        
        // Description optimization (40 points)
        if (item.descriptionLength >= 120 && item.descriptionLength <= 160) score += 40;
        else if (item.descriptionLength >= 100 && item.descriptionLength <= 180) score += 30;
        else if (item.descriptionLength >= 50) score += 20;
        else score += 10;
        
        // Keywords optimization (30 points)
        const keywordCount = item.keywords.split(',').length;
        if (keywordCount >= 3 && keywordCount <= 8) score += 30;
        else if (keywordCount >= 2) score += 20;
        else score += 10;
        
        return score;
    };

    const filteredData = metaData.filter(item => {
        const matchesSearch = item.page.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            item.title.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesFilter = filterType === 'all' || item.status === filterType;
        return matchesSearch && matchesFilter;
    });

    const handleEdit = (item) => {
        setEditingId(item.id);
        setEditForm({
            title: item.title,
            description: item.description,
            keywords: item.keywords
        });
    };

    const handleSave = (id) => {
        setMetaData(prev => prev.map(item => 
            item.id === id 
                ? {
                    ...item,
                    title: editForm.title,
                    description: editForm.description,
                    keywords: editForm.keywords,
                    titleLength: editForm.title.length,
                    descriptionLength: editForm.description.length,
                    lastUpdated: new Date().toISOString().split('T')[0],
                    status: getOptimizationScore({
                        titleLength: editForm.title.length,
                        descriptionLength: editForm.description.length,
                        keywords: editForm.keywords
                    }) >= 80 ? 'optimized' : getOptimizationScore({
                        titleLength: editForm.title.length,
                        descriptionLength: editForm.description.length,
                        keywords: editForm.keywords
                    }) >= 60 ? 'needs-attention' : 'poor'
                }
                : item
        ));
        setEditingId(null);
    };

    const handleCancel = () => {
        setEditingId(null);
        setEditForm({ title: '', description: '', keywords: '' });
    };

    return (
        <div className="p-6 bg-gray-50 min-h-screen">
            {/* Header */}
            <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900">Meta Tags Management</h1>
                <p className="text-gray-600 mt-2">Optimize meta titles, descriptions, and keywords for better SEO performance</p>
            </div>

            {/* Filters and Search */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                <div className="flex flex-col md:flex-row gap-4">
                    <div className="flex-1">
                        <div className="relative">
                            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                            <input
                                type="text"
                                placeholder="Search pages, titles, or descriptions..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-black placeholder-gray-500 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                            />
                        </div>
                    </div>
                    <div className="flex gap-4">
                        <select
                            value={filterType}
                            onChange={(e) => setFilterType(e.target.value)}
                            className="border border-gray-300 rounded-lg px-4 py-2 bg-white text-black focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                        >
                            <option value="all">All Status</option>
                            <option value="optimized">Optimized</option>
                            <option value="needs-attention">Needs Attention</option>
                            <option value="poor">Poor</option>
                        </select>
                        <button className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                            Bulk Edit
                        </button>
                    </div>
                </div>
            </div>

            {/* Meta Data Table */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead className="bg-gray-50 border-b border-gray-200">
                            <tr>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Page</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Title</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Description</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Status</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Score</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Actions</th>
                            </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                            {filteredData.map((item) => (
                                <tr key={item.id} className="hover:bg-gray-50">
                                    <td className="py-4 px-6">
                                        <div>
                                            <p className="text-sm font-medium text-blue-600">{item.page}</p>
                                            <p className="text-xs text-gray-500">{item.type}</p>
                                        </div>
                                    </td>
                                    <td className="py-4 px-6">
                                        {editingId === item.id ? (
                                            <div>
                                                <input
                                                    type="text"
                                                    value={editForm.title}
                                                    onChange={(e) => setEditForm({...editForm, title: e.target.value})}
                                                    className="w-full p-2 border border-gray-300 rounded text-sm text-black focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                                />
                                                <p className={`text-xs mt-1 ${editForm.title.length > 60 ? 'text-red-500' : editForm.title.length < 30 ? 'text-yellow-500' : 'text-green-500'}`}>
                                                    {editForm.title.length}/60 characters
                                                </p>
                                            </div>
                                        ) : (
                                            <div>
                                                <p className="text-sm text-gray-900 line-clamp-2">{item.title}</p>
                                                <p className={`text-xs mt-1 ${item.titleLength > 60 ? 'text-red-500' : item.titleLength < 30 ? 'text-yellow-500' : 'text-green-500'}`}>
                                                    {item.titleLength} characters
                                                </p>
                                            </div>
                                        )}
                                    </td>
                                    <td className="py-4 px-6">
                                        {editingId === item.id ? (
                                            <div>
                                                <textarea
                                                    value={editForm.description}
                                                    onChange={(e) => setEditForm({...editForm, description: e.target.value})}
                                                    rows={3}
                                                    className="w-full p-2 border border-gray-300 rounded text-sm text-black focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                                />
                                                <p className={`text-xs mt-1 ${editForm.description.length > 160 ? 'text-red-500' : editForm.description.length < 120 ? 'text-yellow-500' : 'text-green-500'}`}>
                                                    {editForm.description.length}/160 characters
                                                </p>
                                            </div>
                                        ) : (
                                            <div>
                                                <p className="text-sm text-gray-900 line-clamp-3">{item.description}</p>
                                                <p className={`text-xs mt-1 ${item.descriptionLength > 160 ? 'text-red-500' : item.descriptionLength < 120 ? 'text-yellow-500' : 'text-green-500'}`}>
                                                    {item.descriptionLength} characters
                                                </p>
                                            </div>
                                        )}
                                    </td>
                                    <td className="py-4 px-6">
                                        <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${statusColors[item.status]}`}>
                                            {statusIcons[item.status]}
                                            {item.status.replace('-', ' ')}
                                        </span>
                                    </td>
                                    <td className="py-4 px-6">
                                        <div className="flex items-center">
                                            <span className="text-sm font-medium text-gray-900">{getOptimizationScore(item)}/100</span>
                                            <div className="ml-2 w-16 bg-gray-200 rounded-full h-2">
                                                <div 
                                                    className={`h-2 rounded-full ${getOptimizationScore(item) >= 80 ? 'bg-green-500' : getOptimizationScore(item) >= 60 ? 'bg-yellow-500' : 'bg-red-500'}`}
                                                    style={{ width: `${getOptimizationScore(item)}%` }}
                                                ></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td className="py-4 px-6">
                                        {editingId === item.id ? (
                                            <div className="flex items-center space-x-2">
                                                <button
                                                    onClick={() => handleSave(item.id)}
                                                    className="text-green-600 hover:text-green-800"
                                                >
                                                    <CheckIcon className="h-4 w-4" />
                                                </button>
                                                <button
                                                    onClick={handleCancel}
                                                    className="text-red-600 hover:text-red-800"
                                                >
                                                    <XMarkIcon className="h-4 w-4" />
                                                </button>
                                            </div>
                                        ) : (
                                            <button
                                                onClick={() => handleEdit(item)}
                                                className="text-blue-600 hover:text-blue-800"
                                            >
                                                <PencilIcon className="h-4 w-4" />
                                            </button>
                                        )}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* SEO Tips */}
            <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-4">SEO Optimization Tips</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <h4 className="font-medium text-blue-800 mb-2">Title Tags</h4>
                        <ul className="text-blue-700 space-y-1">
                            <li>• Keep between 30-60 characters</li>
                            <li>• Include primary keyword</li>
                            <li>• Make it compelling and unique</li>
                        </ul>
                    </div>
                    <div>
                        <h4 className="font-medium text-blue-800 mb-2">Meta Descriptions</h4>
                        <ul className="text-blue-700 space-y-1">
                            <li>• Keep between 120-160 characters</li>
                            <li>• Include call-to-action</li>
                            <li>• Summarize page content</li>
                        </ul>
                    </div>
                    <div>
                        <h4 className="font-medium text-blue-800 mb-2">Keywords</h4>
                        <ul className="text-blue-700 space-y-1">
                            <li>• Use 3-8 relevant keywords</li>
                            <li>• Separate with commas</li>
                            <li>• Focus on search intent</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    );
}
