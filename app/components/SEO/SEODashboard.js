'use client';

import { useState, useEffect } from 'react';
import {
    ChartBarIcon,
    MagnifyingGlassIcon,
    DocumentTextIcon,
    LinkIcon,
    CogIcon,
    GlobeAltIcon,
    EyeIcon,
    ArrowTrendingUpIcon,
    ExclamationTriangleIcon,
    CheckCircleIcon
} from '@heroicons/react/24/outline';

export default function SEODashboard() {
    const [seoData, setSeoData] = useState({
        overview: {
            totalPages: 1247,
            indexedPages: 1156,
            organicTraffic: 45678,
            avgPosition: 12.4,
            totalKeywords: 2341,
            backlinks: 8934,
            domainAuthority: 67,
            pageSpeed: 89
        },
        recentActivity: [
            { type: 'audit', message: 'Site audit completed - 23 issues found', time: '2 hours ago', status: 'warning' },
            { type: 'keyword', message: 'New keyword "wholesale electronics" ranked #8', time: '4 hours ago', status: 'success' },
            { type: 'meta', message: 'Meta tags updated for 15 product pages', time: '6 hours ago', status: 'success' },
            { type: 'backlink', message: '3 new high-quality backlinks detected', time: '1 day ago', status: 'success' },
            { type: 'error', message: '404 errors found on 5 pages', time: '2 days ago', status: 'error' }
        ],
        topKeywords: [
            { keyword: 'wholesale products', position: 3, traffic: 1234, change: '+2' },
            { keyword: 'bulk electronics', position: 7, traffic: 987, change: '-1' },
            { keyword: 'B2B marketplace', position: 12, traffic: 756, change: '+5' },
            { keyword: 'business supplies', position: 15, traffic: 543, change: '0' },
            { keyword: 'wholesale distributors', position: 18, traffic: 432, change: '+3' }
        ]
    });

    const quickActions = [
        {
            title: 'SEO Analytics',
            description: 'View detailed SEO performance metrics',
            icon: ChartBarIcon,
            href: '/seo-admin/analytics',
            color: 'bg-blue-500'
        },
        {
            title: 'Keyword Research',
            description: 'Research and track keywords',
            icon: MagnifyingGlassIcon,
            href: '/seo-admin/keyword-research',
            color: 'bg-green-500'
        },
        {
            title: 'Meta Management',
            description: 'Manage meta tags and descriptions',
            icon: DocumentTextIcon,
            href: '/seo-admin/meta-management',
            color: 'bg-purple-500'
        },
        {
            title: 'Content Optimization',
            description: 'Optimize content for better rankings',
            icon: EyeIcon,
            href: '/seo-admin/content-optimization',
            color: 'bg-orange-500'
        },
        {
            title: 'Site Audit',
            description: 'Comprehensive site health check',
            icon: CogIcon,
            href: '/seo-admin/site-audit',
            color: 'bg-red-500'
        },
        {
            title: 'Backlink Management',
            description: 'Monitor and manage backlinks',
            icon: LinkIcon,
            href: '/seo-admin/backlinks',
            color: 'bg-indigo-500'
        },
        {
            title: 'Sitemap Manager',
            description: 'Generate and manage sitemaps',
            icon: GlobeAltIcon,
            href: '/seo-admin/sitemap',
            color: 'bg-teal-500'
        }
    ];

    const getStatusIcon = (status) => {
        switch (status) {
            case 'success':
                return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
            case 'warning':
                return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
            case 'error':
                return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
            default:
                return <CheckCircleIcon className="h-5 w-5 text-gray-500" />;
        }
    };

    const getChangeColor = (change) => {
        if (change.startsWith('+')) return 'text-green-600';
        if (change.startsWith('-')) return 'text-red-600';
        return 'text-gray-600';
    };

    return (
        <div className="p-6 bg-gray-50 min-h-screen">
            {/* Header */}
            <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900">SEO Expert Dashboard</h1>
                <p className="text-gray-600 mt-2">Monitor and optimize your website's search engine performance</p>
            </div>

            {/* Overview Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-600">Organic Traffic</p>
                            <p className="text-2xl font-bold text-gray-900">{seoData.overview.organicTraffic.toLocaleString()}</p>
                        </div>
                        <ArrowTrendingUpIcon className="h-8 w-8 text-green-500" />
                    </div>
                    <p className="text-xs text-green-600 mt-2">+12.5% from last month</p>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-600">Indexed Pages</p>
                            <p className="text-2xl font-bold text-gray-900">{seoData.overview.indexedPages}/{seoData.overview.totalPages}</p>
                        </div>
                        <GlobeAltIcon className="h-8 w-8 text-blue-500" />
                    </div>
                    <p className="text-xs text-blue-600 mt-2">92.7% indexation rate</p>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-600">Avg. Position</p>
                            <p className="text-2xl font-bold text-gray-900">{seoData.overview.avgPosition}</p>
                        </div>
                        <ChartBarIcon className="h-8 w-8 text-purple-500" />
                    </div>
                    <p className="text-xs text-green-600 mt-2">+2.1 positions improved</p>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-600">Domain Authority</p>
                            <p className="text-2xl font-bold text-gray-900">{seoData.overview.domainAuthority}</p>
                        </div>
                        <LinkIcon className="h-8 w-8 text-orange-500" />
                    </div>
                    <p className="text-xs text-green-600 mt-2">+3 points this quarter</p>
                </div>
            </div>

            {/* Quick Actions */}
            <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {quickActions.map((action, index) => (
                        <a
                            key={index}
                            href={action.href}
                            className="bg-white rounded-lg shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow cursor-pointer group"
                        >
                            <div className="flex items-center mb-3">
                                <div className={`${action.color} p-2 rounded-lg`}>
                                    <action.icon className="h-6 w-6 text-white" />
                                </div>
                            </div>
                            <h3 className="font-semibold text-gray-900 group-hover:text-orange-600 transition-colors">
                                {action.title}
                            </h3>
                            <p className="text-sm text-gray-600 mt-1">{action.description}</p>
                        </a>
                    ))}
                </div>
            </div>

            {/* Recent Activity & Top Keywords */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Recent Activity */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div className="p-6 border-b border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
                    </div>
                    <div className="p-6">
                        <div className="space-y-4">
                            {seoData.recentActivity.map((activity, index) => (
                                <div key={index} className="flex items-start space-x-3">
                                    {getStatusIcon(activity.status)}
                                    <div className="flex-1 min-w-0">
                                        <p className="text-sm text-gray-900">{activity.message}</p>
                                        <p className="text-xs text-gray-500">{activity.time}</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Top Keywords */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div className="p-6 border-b border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-900">Top Keywords</h3>
                    </div>
                    <div className="p-6">
                        <div className="space-y-4">
                            {seoData.topKeywords.map((keyword, index) => (
                                <div key={index} className="flex items-center justify-between">
                                    <div className="flex-1">
                                        <p className="text-sm font-medium text-gray-900">{keyword.keyword}</p>
                                        <p className="text-xs text-gray-500">Position #{keyword.position}</p>
                                    </div>
                                    <div className="text-right">
                                        <p className="text-sm text-gray-900">{keyword.traffic} visits</p>
                                        <p className={`text-xs ${getChangeColor(keyword.change)}`}>
                                            {keyword.change !== '0' ? keyword.change : 'No change'}
                                        </p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
