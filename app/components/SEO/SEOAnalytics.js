'use client';

import { useState, useEffect } from 'react';
import {
    ChartBarIcon,
    ArrowTrendingUpIcon,
    ArrowTrendingDownIcon,
    EyeIcon,
    CursorArrowRaysIcon,
    ClockIcon,
    DevicePhoneMobileIcon,
    ComputerDesktopIcon,
    GlobeAltIcon
} from '@heroicons/react/24/outline';

export default function SEOAnalytics() {
    const [timeRange, setTimeRange] = useState('30d');
    const [analyticsData, setAnalyticsData] = useState({
        overview: {
            totalClicks: 125678,
            totalImpressions: 2456789,
            avgCTR: 5.12,
            avgPosition: 12.4,
            clicksChange: '+15.3%',
            impressionsChange: '****%',
            ctrChange: '****%',
            positionChange: '-1.2'
        },
        topPages: [
            { url: '/products/electronics', clicks: 15678, impressions: 234567, ctr: 6.68, position: 8.2 },
            { url: '/products/categories/smartphones', clicks: 12456, impressions: 189234, ctr: 6.58, position: 9.1 },
            { url: '/products/wholesale-laptops', clicks: 9876, impressions: 156789, ctr: 6.30, position: 7.5 },
            { url: '/categories/electronics', clicks: 8765, impressions: 145678, ctr: 6.02, position: 10.3 },
            { url: '/products/bulk-accessories', clicks: 7654, impressions: 134567, ctr: 5.69, position: 11.8 }
        ],
        topQueries: [
            { query: 'wholesale electronics', clicks: 8765, impressions: 123456, ctr: 7.10, position: 6.2 },
            { query: 'bulk smartphones', clicks: 7654, impressions: 109876, ctr: 6.97, position: 7.8 },
            { query: 'B2B electronics marketplace', clicks: 6543, impressions: 98765, ctr: 6.62, position: 8.5 },
            { query: 'wholesale laptop distributors', clicks: 5432, impressions: 87654, ctr: 6.20, position: 9.2 },
            { query: 'bulk electronic accessories', clicks: 4321, impressions: 76543, ctr: 5.65, position: 10.1 }
        ],
        deviceBreakdown: {
            mobile: { percentage: 68, clicks: 85461, change: '+12.5%' },
            desktop: { percentage: 28, clicks: 35190, change: '****%' },
            tablet: { percentage: 4, clicks: 5027, change: '****%' }
        },
        countryBreakdown: [
            { country: 'United States', clicks: 45678, percentage: 36.3 },
            { country: 'Australia', clicks: 23456, percentage: 18.7 },
            { country: 'Canada', clicks: 15678, percentage: 12.5 },
            { country: 'United Kingdom', clicks: 12345, percentage: 9.8 },
            { country: 'Germany', clicks: 8765, percentage: 7.0 }
        ]
    });

    const timeRanges = [
        { value: '7d', label: 'Last 7 days' },
        { value: '30d', label: 'Last 30 days' },
        { value: '90d', label: 'Last 90 days' },
        { value: '1y', label: 'Last year' }
    ];

    const getChangeColor = (change) => {
        if (change.startsWith('+')) return 'text-green-600';
        if (change.startsWith('-')) return 'text-red-600';
        return 'text-gray-600';
    };

    const getChangeIcon = (change) => {
        if (change.startsWith('+')) return <ArrowTrendingUpIcon className="h-4 w-4 text-green-600" />;
        if (change.startsWith('-')) return <ArrowTrendingDownIcon className="h-4 w-4 text-red-600" />;
        return null;
    };

    return (
        <div className="p-6 bg-gray-50 min-h-screen">
            {/* Header */}
            <div className="flex justify-between items-center mb-8">
                <div>
                    <h1 className="text-3xl font-bold text-gray-900">SEO Analytics</h1>
                    <p className="text-gray-600 mt-2">Detailed search performance metrics and insights</p>
                </div>
                <div className="flex items-center space-x-4">
                    <select
                        value={timeRange}
                        onChange={(e) => setTimeRange(e.target.value)}
                        className="border border-gray-300 rounded-lg px-4 py-2 bg-white text-black focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    >
                        {timeRanges.map(range => (
                            <option key={range.value} value={range.value}>{range.label}</option>
                        ))}
                    </select>
                    <button className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                        Export Report
                    </button>
                </div>
            </div>

            {/* Overview Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                    <div className="flex items-center justify-between mb-2">
                        <p className="text-sm font-medium text-gray-600">Total Clicks</p>
                        <CursorArrowRaysIcon className="h-5 w-5 text-blue-500" />
                    </div>
                    <p className="text-2xl font-bold text-gray-900">{analyticsData.overview.totalClicks.toLocaleString()}</p>
                    <div className="flex items-center mt-2">
                        {getChangeIcon(analyticsData.overview.clicksChange)}
                        <span className={`text-sm ml-1 ${getChangeColor(analyticsData.overview.clicksChange)}`}>
                            {analyticsData.overview.clicksChange}
                        </span>
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                    <div className="flex items-center justify-between mb-2">
                        <p className="text-sm font-medium text-gray-600">Total Impressions</p>
                        <EyeIcon className="h-5 w-5 text-green-500" />
                    </div>
                    <p className="text-2xl font-bold text-gray-900">{analyticsData.overview.totalImpressions.toLocaleString()}</p>
                    <div className="flex items-center mt-2">
                        {getChangeIcon(analyticsData.overview.impressionsChange)}
                        <span className={`text-sm ml-1 ${getChangeColor(analyticsData.overview.impressionsChange)}`}>
                            {analyticsData.overview.impressionsChange}
                        </span>
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                    <div className="flex items-center justify-between mb-2">
                        <p className="text-sm font-medium text-gray-600">Average CTR</p>
                        <ChartBarIcon className="h-5 w-5 text-purple-500" />
                    </div>
                    <p className="text-2xl font-bold text-gray-900">{analyticsData.overview.avgCTR}%</p>
                    <div className="flex items-center mt-2">
                        {getChangeIcon(analyticsData.overview.ctrChange)}
                        <span className={`text-sm ml-1 ${getChangeColor(analyticsData.overview.ctrChange)}`}>
                            {analyticsData.overview.ctrChange}
                        </span>
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                    <div className="flex items-center justify-between mb-2">
                        <p className="text-sm font-medium text-gray-600">Average Position</p>
                        <ArrowTrendingUpIcon className="h-5 w-5 text-orange-500" />
                    </div>
                    <p className="text-2xl font-bold text-gray-900">{analyticsData.overview.avgPosition}</p>
                    <div className="flex items-center mt-2">
                        {getChangeIcon(analyticsData.overview.positionChange)}
                        <span className={`text-sm ml-1 ${getChangeColor(analyticsData.overview.positionChange)}`}>
                            {analyticsData.overview.positionChange}
                        </span>
                    </div>
                </div>
            </div>

            {/* Top Pages and Queries */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                {/* Top Pages */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div className="p-6 border-b border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-900">Top Performing Pages</h3>
                    </div>
                    <div className="p-6">
                        <div className="overflow-x-auto">
                            <table className="w-full text-sm">
                                <thead>
                                    <tr className="border-b border-gray-200">
                                        <th className="text-left py-2 text-gray-600">Page</th>
                                        <th className="text-right py-2 text-gray-600">Clicks</th>
                                        <th className="text-right py-2 text-gray-600">CTR</th>
                                        <th className="text-right py-2 text-gray-600">Position</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {analyticsData.topPages.map((page, index) => (
                                        <tr key={index} className="border-b border-gray-100">
                                            <td className="py-3 text-blue-600 hover:text-blue-800 cursor-pointer truncate max-w-xs">
                                                {page.url}
                                            </td>
                                            <td className="py-3 text-right text-gray-900">{page.clicks.toLocaleString()}</td>
                                            <td className="py-3 text-right text-gray-900">{page.ctr}%</td>
                                            <td className="py-3 text-right text-gray-900">{page.position}</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                {/* Top Queries */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div className="p-6 border-b border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-900">Top Search Queries</h3>
                    </div>
                    <div className="p-6">
                        <div className="overflow-x-auto">
                            <table className="w-full text-sm">
                                <thead>
                                    <tr className="border-b border-gray-200">
                                        <th className="text-left py-2 text-gray-600">Query</th>
                                        <th className="text-right py-2 text-gray-600">Clicks</th>
                                        <th className="text-right py-2 text-gray-600">CTR</th>
                                        <th className="text-right py-2 text-gray-600">Position</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {analyticsData.topQueries.map((query, index) => (
                                        <tr key={index} className="border-b border-gray-100">
                                            <td className="py-3 text-gray-900 truncate max-w-xs">{query.query}</td>
                                            <td className="py-3 text-right text-gray-900">{query.clicks.toLocaleString()}</td>
                                            <td className="py-3 text-right text-gray-900">{query.ctr}%</td>
                                            <td className="py-3 text-right text-gray-900">{query.position}</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            {/* Device and Country Breakdown */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Device Breakdown */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div className="p-6 border-b border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-900">Device Breakdown</h3>
                    </div>
                    <div className="p-6">
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                    <DevicePhoneMobileIcon className="h-5 w-5 text-blue-500" />
                                    <span className="text-gray-900">Mobile</span>
                                </div>
                                <div className="text-right">
                                    <p className="text-gray-900 font-medium">{analyticsData.deviceBreakdown.mobile.percentage}%</p>
                                    <p className="text-sm text-gray-500">{analyticsData.deviceBreakdown.mobile.clicks.toLocaleString()} clicks</p>
                                </div>
                            </div>
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                    <ComputerDesktopIcon className="h-5 w-5 text-green-500" />
                                    <span className="text-gray-900">Desktop</span>
                                </div>
                                <div className="text-right">
                                    <p className="text-gray-900 font-medium">{analyticsData.deviceBreakdown.desktop.percentage}%</p>
                                    <p className="text-sm text-gray-500">{analyticsData.deviceBreakdown.desktop.clicks.toLocaleString()} clicks</p>
                                </div>
                            </div>
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                    <GlobeAltIcon className="h-5 w-5 text-purple-500" />
                                    <span className="text-gray-900">Tablet</span>
                                </div>
                                <div className="text-right">
                                    <p className="text-gray-900 font-medium">{analyticsData.deviceBreakdown.tablet.percentage}%</p>
                                    <p className="text-sm text-gray-500">{analyticsData.deviceBreakdown.tablet.clicks.toLocaleString()} clicks</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Country Breakdown */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div className="p-6 border-b border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-900">Top Countries</h3>
                    </div>
                    <div className="p-6">
                        <div className="space-y-4">
                            {analyticsData.countryBreakdown.map((country, index) => (
                                <div key={index} className="flex items-center justify-between">
                                    <span className="text-gray-900">{country.country}</span>
                                    <div className="text-right">
                                        <p className="text-gray-900 font-medium">{country.percentage}%</p>
                                        <p className="text-sm text-gray-500">{country.clicks.toLocaleString()} clicks</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
