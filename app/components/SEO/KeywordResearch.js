'use client';

import { useState, useEffect } from 'react';
import {
    MagnifyingGlassIcon,
    ArrowTrendingUpIcon,
    ArrowTrendingDownIcon,
    StarIcon,
    PlusIcon,
    EyeIcon,
    ChartBarIcon,
    LightBulbIcon
} from '@heroicons/react/24/outline';

export default function KeywordResearch() {
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [trackedKeywords, setTrackedKeywords] = useState([
        {
            id: 1,
            keyword: 'wholesale electronics',
            volume: 12500,
            difficulty: 65,
            cpc: 2.45,
            currentPosition: 8,
            previousPosition: 12,
            trend: 'up',
            category: 'electronics',
            tracked: true
        },
        {
            id: 2,
            keyword: 'bulk smartphones',
            volume: 8900,
            difficulty: 72,
            cpc: 3.20,
            currentPosition: 15,
            previousPosition: 18,
            trend: 'up',
            category: 'electronics',
            tracked: true
        },
        {
            id: 3,
            keyword: 'B2B marketplace',
            volume: 15600,
            difficulty: 58,
            cpc: 4.10,
            currentPosition: 6,
            previousPosition: 6,
            trend: 'stable',
            category: 'general',
            tracked: true
        },
        {
            id: 4,
            keyword: 'wholesale laptops',
            volume: 6700,
            difficulty: 68,
            cpc: 5.25,
            currentPosition: 22,
            previousPosition: 19,
            trend: 'down',
            category: 'electronics',
            tracked: true
        }
    ]);

    const [keywordSuggestions, setKeywordSuggestions] = useState([
        {
            keyword: 'bulk office supplies',
            volume: 9800,
            difficulty: 45,
            cpc: 1.85,
            opportunity: 'high',
            category: 'office'
        },
        {
            keyword: 'wholesale furniture',
            volume: 7200,
            difficulty: 52,
            cpc: 3.40,
            opportunity: 'medium',
            category: 'furniture'
        },
        {
            keyword: 'B2B electronics distributor',
            volume: 4500,
            difficulty: 38,
            cpc: 2.90,
            opportunity: 'high',
            category: 'electronics'
        },
        {
            keyword: 'bulk industrial equipment',
            volume: 3200,
            difficulty: 62,
            cpc: 6.75,
            opportunity: 'medium',
            category: 'industrial'
        },
        {
            keyword: 'wholesale clothing suppliers',
            volume: 11200,
            difficulty: 55,
            cpc: 2.15,
            opportunity: 'high',
            category: 'clothing'
        }
    ]);

    const categories = [
        { value: 'all', label: 'All Categories' },
        { value: 'electronics', label: 'Electronics' },
        { value: 'office', label: 'Office Supplies' },
        { value: 'furniture', label: 'Furniture' },
        { value: 'industrial', label: 'Industrial' },
        { value: 'clothing', label: 'Clothing' },
        { value: 'general', label: 'General' }
    ];

    const getDifficultyColor = (difficulty) => {
        if (difficulty < 40) return 'text-green-600 bg-green-100';
        if (difficulty < 70) return 'text-yellow-600 bg-yellow-100';
        return 'text-red-600 bg-red-100';
    };

    const getOpportunityColor = (opportunity) => {
        switch (opportunity) {
            case 'high': return 'text-green-600 bg-green-100';
            case 'medium': return 'text-yellow-600 bg-yellow-100';
            case 'low': return 'text-red-600 bg-red-100';
            default: return 'text-gray-600 bg-gray-100';
        }
    };

    const getTrendIcon = (trend) => {
        switch (trend) {
            case 'up': return <ArrowTrendingUpIcon className="h-4 w-4 text-green-500" />;
            case 'down': return <ArrowTrendingDownIcon className="h-4 w-4 text-red-500" />;
            default: return <div className="h-4 w-4 bg-gray-400 rounded-full"></div>;
        }
    };

    const filteredKeywords = trackedKeywords.filter(keyword => {
        const matchesSearch = keyword.keyword.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesCategory = selectedCategory === 'all' || keyword.category === selectedCategory;
        return matchesSearch && matchesCategory;
    });

    const filteredSuggestions = keywordSuggestions.filter(keyword => {
        const matchesCategory = selectedCategory === 'all' || keyword.category === selectedCategory;
        return matchesCategory;
    });

    const addToTracking = (keyword) => {
        const newKeyword = {
            ...keyword,
            id: Date.now(),
            currentPosition: null,
            previousPosition: null,
            trend: 'stable',
            tracked: true
        };
        setTrackedKeywords([...trackedKeywords, newKeyword]);
        setKeywordSuggestions(keywordSuggestions.filter(k => k.keyword !== keyword.keyword));
    };

    return (
        <div className="p-6 bg-gray-50 min-h-screen">
            {/* Header */}
            <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900">Keyword Research & Tracking</h1>
                <p className="text-gray-600 mt-2">Research new keywords and track your ranking performance</p>
            </div>

            {/* Search and Filters */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                <div className="flex flex-col md:flex-row gap-4">
                    <div className="flex-1">
                        <div className="relative">
                            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                            <input
                                type="text"
                                placeholder="Search keywords or enter new keyword ideas..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-black placeholder-gray-500 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                            />
                        </div>
                    </div>
                    <div className="flex gap-4">
                        <select
                            value={selectedCategory}
                            onChange={(e) => setSelectedCategory(e.target.value)}
                            className="border border-gray-300 rounded-lg px-4 py-2 bg-white text-black focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                        >
                            {categories.map(category => (
                                <option key={category.value} value={category.value}>{category.label}</option>
                            ))}
                        </select>
                        <button className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                            Research Keywords
                        </button>
                    </div>
                </div>
            </div>

            {/* Tracked Keywords */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
                <div className="p-6 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-900">Tracked Keywords</h3>
                    <p className="text-sm text-gray-600 mt-1">Monitor your keyword rankings and performance</p>
                </div>
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead className="bg-gray-50 border-b border-gray-200">
                            <tr>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Keyword</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Volume</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Difficulty</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">CPC</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Position</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Trend</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Category</th>
                            </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                            {filteredKeywords.map((keyword) => (
                                <tr key={keyword.id} className="hover:bg-gray-50">
                                    <td className="py-4 px-6">
                                        <div className="flex items-center">
                                            <StarIcon className="h-4 w-4 text-yellow-500 mr-2" />
                                            <span className="text-sm font-medium text-gray-900">{keyword.keyword}</span>
                                        </div>
                                    </td>
                                    <td className="py-4 px-6">
                                        <span className="text-sm text-gray-900">{keyword.volume.toLocaleString()}</span>
                                    </td>
                                    <td className="py-4 px-6">
                                        <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(keyword.difficulty)}`}>
                                            {keyword.difficulty}%
                                        </span>
                                    </td>
                                    <td className="py-4 px-6">
                                        <span className="text-sm text-gray-900">${keyword.cpc}</span>
                                    </td>
                                    <td className="py-4 px-6">
                                        <div className="flex items-center">
                                            <span className="text-sm font-medium text-gray-900">
                                                {keyword.currentPosition ? `#${keyword.currentPosition}` : 'Not ranked'}
                                            </span>
                                            {keyword.previousPosition && keyword.currentPosition && (
                                                <span className={`ml-2 text-xs ${keyword.currentPosition < keyword.previousPosition ? 'text-green-600' : keyword.currentPosition > keyword.previousPosition ? 'text-red-600' : 'text-gray-600'}`}>
                                                    ({keyword.currentPosition - keyword.previousPosition > 0 ? '+' : ''}{keyword.currentPosition - keyword.previousPosition})
                                                </span>
                                            )}
                                        </div>
                                    </td>
                                    <td className="py-4 px-6">
                                        <div className="flex items-center">
                                            {getTrendIcon(keyword.trend)}
                                        </div>
                                    </td>
                                    <td className="py-4 px-6">
                                        <span className="inline-flex px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 capitalize">
                                            {keyword.category}
                                        </span>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Keyword Suggestions */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="p-6 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                        <div>
                            <h3 className="text-lg font-semibold text-gray-900">Keyword Opportunities</h3>
                            <p className="text-sm text-gray-600 mt-1">Discover new keywords to target and track</p>
                        </div>
                        <LightBulbIcon className="h-6 w-6 text-yellow-500" />
                    </div>
                </div>
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead className="bg-gray-50 border-b border-gray-200">
                            <tr>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Keyword</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Volume</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Difficulty</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">CPC</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Opportunity</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Category</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Action</th>
                            </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                            {filteredSuggestions.map((keyword, index) => (
                                <tr key={index} className="hover:bg-gray-50">
                                    <td className="py-4 px-6">
                                        <span className="text-sm font-medium text-gray-900">{keyword.keyword}</span>
                                    </td>
                                    <td className="py-4 px-6">
                                        <div className="flex items-center">
                                            <EyeIcon className="h-4 w-4 text-gray-400 mr-1" />
                                            <span className="text-sm text-gray-900">{keyword.volume.toLocaleString()}</span>
                                        </div>
                                    </td>
                                    <td className="py-4 px-6">
                                        <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(keyword.difficulty)}`}>
                                            {keyword.difficulty}%
                                        </span>
                                    </td>
                                    <td className="py-4 px-6">
                                        <span className="text-sm text-gray-900">${keyword.cpc}</span>
                                    </td>
                                    <td className="py-4 px-6">
                                        <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium capitalize ${getOpportunityColor(keyword.opportunity)}`}>
                                            {keyword.opportunity}
                                        </span>
                                    </td>
                                    <td className="py-4 px-6">
                                        <span className="inline-flex px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 capitalize">
                                            {keyword.category}
                                        </span>
                                    </td>
                                    <td className="py-4 px-6">
                                        <button
                                            onClick={() => addToTracking(keyword)}
                                            className="inline-flex items-center px-3 py-1 border border-orange-300 text-orange-700 bg-orange-50 rounded-md hover:bg-orange-100 transition-colors text-sm"
                                        >
                                            <PlusIcon className="h-4 w-4 mr-1" />
                                            Track
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Keyword Research Tips */}
            <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-4">Keyword Research Best Practices</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                    <div>
                        <h4 className="font-medium text-blue-800 mb-2">Search Volume</h4>
                        <p className="text-blue-700">Target keywords with sufficient search volume (1000+ monthly searches) but consider long-tail keywords for specific niches.</p>
                    </div>
                    <div>
                        <h4 className="font-medium text-blue-800 mb-2">Keyword Difficulty</h4>
                        <p className="text-blue-700">Start with low to medium difficulty keywords (under 60%) to build authority before targeting competitive terms.</p>
                    </div>
                    <div>
                        <h4 className="font-medium text-blue-800 mb-2">Search Intent</h4>
                        <p className="text-blue-700">Match keywords to user intent: informational, navigational, commercial, or transactional queries.</p>
                    </div>
                </div>
            </div>
        </div>
    );
}
