'use client';

import { useState, useEffect } from 'react';
import { 
    DocumentTextIcon, 
    CheckCircleIcon,
    ExclamationTriangleIcon,
    XCircleIcon,
    EyeIcon,
    PencilIcon,
    ChartBarIcon,
    LightBulbIcon
} from '@heroicons/react/24/outline';

export default function ContentOptimization() {
    const [selectedPage, setSelectedPage] = useState(null);
    const [contentData, setContentData] = useState([
        {
            id: 1,
            url: '/products/electronics',
            title: 'Electronics - Wholesale B2B Marketplace',
            wordCount: 1250,
            readabilityScore: 78,
            seoScore: 85,
            lastOptimized: '2024-01-15',
            issues: [
                { type: 'warning', message: 'Missing H2 headings', priority: 'medium' },
                { type: 'success', message: 'Good keyword density', priority: 'low' }
            ],
            keywords: ['electronics', 'wholesale electronics', 'B2B electronics'],
            headingStructure: {
                h1: 1,
                h2: 2,
                h3: 4,
                h4: 1
            }
        },
        {
            id: 2,
            url: '/products/smartphones',
            title: 'Wholesale Smartphones - Bulk Mobile Phones',
            wordCount: 890,
            readabilityScore: 65,
            seoScore: 72,
            lastOptimized: '2024-01-10',
            issues: [
                { type: 'error', message: 'Content too short (under 1000 words)', priority: 'high' },
                { type: 'warning', message: 'Low keyword density for "bulk smartphones"', priority: 'medium' },
                { type: 'error', message: 'Missing meta description', priority: 'high' }
            ],
            keywords: ['smartphones', 'bulk smartphones', 'wholesale phones'],
            headingStructure: {
                h1: 1,
                h2: 1,
                h3: 2,
                h4: 0
            }
        },
        {
            id: 3,
            url: '/categories',
            title: 'Product Categories',
            wordCount: 450,
            readabilityScore: 82,
            seoScore: 45,
            lastOptimized: '2023-12-20',
            issues: [
                { type: 'error', message: 'Content significantly too short', priority: 'high' },
                { type: 'error', message: 'No target keywords identified', priority: 'high' },
                { type: 'warning', message: 'Poor internal linking', priority: 'medium' }
            ],
            keywords: [],
            headingStructure: {
                h1: 1,
                h2: 0,
                h3: 0,
                h4: 0
            }
        }
    ]);

    const [optimizationSuggestions] = useState([
        {
            type: 'content',
            title: 'Add Product Comparison Tables',
            description: 'Include comparison tables for similar products to increase engagement and time on page.',
            impact: 'high',
            effort: 'medium'
        },
        {
            type: 'keywords',
            title: 'Target Long-tail Keywords',
            description: 'Focus on specific product + "wholesale" combinations for better ranking opportunities.',
            impact: 'high',
            effort: 'low'
        },
        {
            type: 'structure',
            title: 'Improve Heading Structure',
            description: 'Add more H2 and H3 headings to break up content and improve readability.',
            impact: 'medium',
            effort: 'low'
        },
        {
            type: 'internal-linking',
            title: 'Enhance Internal Linking',
            description: 'Add contextual links between related products and categories.',
            impact: 'medium',
            effort: 'medium'
        }
    ]);

    const getScoreColor = (score) => {
        if (score >= 80) return 'text-green-600 bg-green-100';
        if (score >= 60) return 'text-yellow-600 bg-yellow-100';
        return 'text-red-600 bg-red-100';
    };

    const getIssueIcon = (type) => {
        switch (type) {
            case 'error':
                return <XCircleIcon className="h-4 w-4 text-red-500" />;
            case 'warning':
                return <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500" />;
            case 'success':
                return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
            default:
                return <ExclamationTriangleIcon className="h-4 w-4 text-gray-500" />;
        }
    };

    const getImpactColor = (impact) => {
        switch (impact) {
            case 'high': return 'text-red-600 bg-red-100';
            case 'medium': return 'text-yellow-600 bg-yellow-100';
            case 'low': return 'text-green-600 bg-green-100';
            default: return 'text-gray-600 bg-gray-100';
        }
    };

    return (
        <div className="p-6 bg-gray-50 min-h-screen">
            {/* Header */}
            <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900">Content Optimization</h1>
                <p className="text-gray-600 mt-2">Analyze and optimize your content for better search engine rankings</p>
            </div>

            {/* Content Analysis Overview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-gray-900">Pages Analyzed</h3>
                        <DocumentTextIcon className="h-6 w-6 text-blue-500" />
                    </div>
                    <p className="text-3xl font-bold text-gray-900">{contentData.length}</p>
                    <p className="text-sm text-gray-600 mt-2">Total content pages</p>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-gray-900">Avg. SEO Score</h3>
                        <ChartBarIcon className="h-6 w-6 text-green-500" />
                    </div>
                    <p className="text-3xl font-bold text-gray-900">
                        {Math.round(contentData.reduce((acc, page) => acc + page.seoScore, 0) / contentData.length)}
                    </p>
                    <p className="text-sm text-gray-600 mt-2">Out of 100</p>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-gray-900">Issues Found</h3>
                        <ExclamationTriangleIcon className="h-6 w-6 text-red-500" />
                    </div>
                    <p className="text-3xl font-bold text-gray-900">
                        {contentData.reduce((acc, page) => acc + page.issues.length, 0)}
                    </p>
                    <p className="text-sm text-gray-600 mt-2">Across all pages</p>
                </div>
            </div>

            {/* Content Pages Table */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
                <div className="p-6 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-900">Content Analysis</h3>
                    <p className="text-sm text-gray-600 mt-1">Review and optimize individual pages</p>
                </div>
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead className="bg-gray-50 border-b border-gray-200">
                            <tr>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Page</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Word Count</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Readability</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">SEO Score</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Issues</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Actions</th>
                            </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                            {contentData.map((page) => (
                                <tr key={page.id} className="hover:bg-gray-50">
                                    <td className="py-4 px-6">
                                        <div>
                                            <p className="text-sm font-medium text-blue-600">{page.url}</p>
                                            <p className="text-xs text-gray-500 truncate max-w-xs">{page.title}</p>
                                        </div>
                                    </td>
                                    <td className="py-4 px-6">
                                        <span className={`text-sm ${page.wordCount < 1000 ? 'text-red-600' : page.wordCount < 1500 ? 'text-yellow-600' : 'text-green-600'}`}>
                                            {page.wordCount} words
                                        </span>
                                    </td>
                                    <td className="py-4 px-6">
                                        <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(page.readabilityScore)}`}>
                                            {page.readabilityScore}/100
                                        </span>
                                    </td>
                                    <td className="py-4 px-6">
                                        <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(page.seoScore)}`}>
                                            {page.seoScore}/100
                                        </span>
                                    </td>
                                    <td className="py-4 px-6">
                                        <div className="flex items-center space-x-1">
                                            {page.issues.slice(0, 2).map((issue, index) => (
                                                <div key={index} className="flex items-center">
                                                    {getIssueIcon(issue.type)}
                                                </div>
                                            ))}
                                            {page.issues.length > 2 && (
                                                <span className="text-xs text-gray-500">+{page.issues.length - 2}</span>
                                            )}
                                        </div>
                                    </td>
                                    <td className="py-4 px-6">
                                        <div className="flex items-center space-x-2">
                                            <button
                                                onClick={() => setSelectedPage(page)}
                                                className="text-blue-600 hover:text-blue-800"
                                            >
                                                <EyeIcon className="h-4 w-4" />
                                            </button>
                                            <button className="text-green-600 hover:text-green-800">
                                                <PencilIcon className="h-4 w-4" />
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Optimization Suggestions */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
                <div className="p-6 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                        <div>
                            <h3 className="text-lg font-semibold text-gray-900">Optimization Suggestions</h3>
                            <p className="text-sm text-gray-600 mt-1">Actionable recommendations to improve your content</p>
                        </div>
                        <LightBulbIcon className="h-6 w-6 text-yellow-500" />
                    </div>
                </div>
                <div className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {optimizationSuggestions.map((suggestion, index) => (
                            <div key={index} className="border border-gray-200 rounded-lg p-4">
                                <div className="flex items-start justify-between mb-3">
                                    <h4 className="font-medium text-gray-900">{suggestion.title}</h4>
                                    <div className="flex space-x-2">
                                        <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getImpactColor(suggestion.impact)}`}>
                                            {suggestion.impact} impact
                                        </span>
                                        <span className="inline-flex px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            {suggestion.effort} effort
                                        </span>
                                    </div>
                                </div>
                                <p className="text-sm text-gray-600 mb-4">{suggestion.description}</p>
                                <button className="text-orange-600 hover:text-orange-800 text-sm font-medium">
                                    Apply Suggestion →
                                </button>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            {/* Page Detail Modal */}
            {selectedPage && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
                        <div className="p-6 border-b border-gray-200">
                            <div className="flex items-center justify-between">
                                <h3 className="text-lg font-semibold text-gray-900">Content Analysis: {selectedPage.url}</h3>
                                <button
                                    onClick={() => setSelectedPage(null)}
                                    className="text-gray-400 hover:text-gray-600"
                                >
                                    <XCircleIcon className="h-6 w-6" />
                                </button>
                            </div>
                        </div>
                        <div className="p-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <h4 className="font-medium text-gray-900 mb-3">Content Metrics</h4>
                                    <div className="space-y-2 text-sm">
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Word Count:</span>
                                            <span className="text-gray-900">{selectedPage.wordCount}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Readability Score:</span>
                                            <span className="text-gray-900">{selectedPage.readabilityScore}/100</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">SEO Score:</span>
                                            <span className="text-gray-900">{selectedPage.seoScore}/100</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Last Optimized:</span>
                                            <span className="text-gray-900">{selectedPage.lastOptimized}</span>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <h4 className="font-medium text-gray-900 mb-3">Heading Structure</h4>
                                    <div className="space-y-2 text-sm">
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">H1 Tags:</span>
                                            <span className="text-gray-900">{selectedPage.headingStructure.h1}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">H2 Tags:</span>
                                            <span className="text-gray-900">{selectedPage.headingStructure.h2}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">H3 Tags:</span>
                                            <span className="text-gray-900">{selectedPage.headingStructure.h3}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">H4 Tags:</span>
                                            <span className="text-gray-900">{selectedPage.headingStructure.h4}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="mb-6">
                                <h4 className="font-medium text-gray-900 mb-3">Target Keywords</h4>
                                <div className="flex flex-wrap gap-2">
                                    {selectedPage.keywords.length > 0 ? (
                                        selectedPage.keywords.map((keyword, index) => (
                                            <span key={index} className="inline-flex px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                                                {keyword}
                                            </span>
                                        ))
                                    ) : (
                                        <span className="text-gray-500 text-sm">No keywords identified</span>
                                    )}
                                </div>
                            </div>

                            <div>
                                <h4 className="font-medium text-gray-900 mb-3">Issues & Recommendations</h4>
                                <div className="space-y-3">
                                    {selectedPage.issues.map((issue, index) => (
                                        <div key={index} className="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg">
                                            {getIssueIcon(issue.type)}
                                            <div className="flex-1">
                                                <p className="text-sm text-gray-900">{issue.message}</p>
                                                <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium mt-1 ${
                                                    issue.priority === 'high' ? 'bg-red-100 text-red-800' :
                                                    issue.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                                    'bg-green-100 text-green-800'
                                                }`}>
                                                    {issue.priority} priority
                                                </span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Content Optimization Tips */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-4">Content Optimization Best Practices</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                    <div>
                        <h4 className="font-medium text-blue-800 mb-2">Content Length</h4>
                        <p className="text-blue-700">Aim for 1000+ words for comprehensive coverage. Longer content typically ranks better for competitive keywords.</p>
                    </div>
                    <div>
                        <h4 className="font-medium text-blue-800 mb-2">Keyword Optimization</h4>
                        <p className="text-blue-700">Use primary keywords in H1, H2 tags, and naturally throughout content. Maintain 1-2% keyword density.</p>
                    </div>
                    <div>
                        <h4 className="font-medium text-blue-800 mb-2">User Experience</h4>
                        <p className="text-blue-700">Break up content with headings, bullet points, and images. Improve readability for better engagement.</p>
                    </div>
                </div>
            </div>
        </div>
    );
}
