'use client';

import { useState, useEffect } from 'react';
import {
    LinkI<PERSON>,
    ArrowTrendingUpIcon,
    ArrowTrendingDownIcon,
    CheckCircleIcon,
    ExclamationTriangleIcon,
    XCircleIcon,
    MagnifyingGlassIcon,
    PlusIcon,
    EyeIcon
} from '@heroicons/react/24/outline';

export default function BacklinkManager() {
    const [selectedTab, setSelectedTab] = useState('overview');
    const [searchTerm, setSearchTerm] = useState('');
    const [filterStatus, setFilterStatus] = useState('all');

    const [backlinkData, setBacklinkData] = useState({
        overview: {
            totalBacklinks: 8934,
            totalDomains: 1247,
            domainAuthority: 67,
            newBacklinks: 23,
            lostBacklinks: 8,
            toxicBacklinks: 12
        },
        backlinks: [
            {
                id: 1,
                sourceUrl: 'https://techcrunch.com/b2b-marketplace-trends',
                targetUrl: '/products/electronics',
                anchorText: 'wholesale electronics marketplace',
                domainAuthority: 92,
                pageAuthority: 78,
                status: 'active',
                type: 'dofollow',
                firstSeen: '2024-01-15',
                lastSeen: '2024-01-18',
                traffic: 1250
            },
            {
                id: 2,
                sourceUrl: 'https://businessinsider.com/wholesale-trends',
                targetUrl: '/categories',
                anchorText: 'B2B product categories',
                domainAuthority: 88,
                pageAuthority: 65,
                status: 'active',
                type: 'dofollow',
                firstSeen: '2024-01-10',
                lastSeen: '2024-01-18',
                traffic: 890
            },
            {
                id: 3,
                sourceUrl: 'https://spammy-site.com/random-page',
                targetUrl: '/products/smartphones',
                anchorText: 'click here',
                domainAuthority: 15,
                pageAuthority: 12,
                status: 'toxic',
                type: 'dofollow',
                firstSeen: '2024-01-12',
                lastSeen: '2024-01-18',
                traffic: 45
            },
            {
                id: 4,
                sourceUrl: 'https://industry-blog.com/electronics-wholesale',
                targetUrl: '/about',
                anchorText: 'Diptouch wholesale platform',
                domainAuthority: 45,
                pageAuthority: 38,
                status: 'lost',
                type: 'dofollow',
                firstSeen: '2023-12-20',
                lastSeen: '2024-01-05',
                traffic: 320
            },
            {
                id: 5,
                sourceUrl: 'https://forbes.com/b2b-digital-transformation',
                targetUrl: '/products',
                anchorText: 'digital B2B marketplace',
                domainAuthority: 95,
                pageAuthority: 82,
                status: 'active',
                type: 'dofollow',
                firstSeen: '2024-01-08',
                lastSeen: '2024-01-18',
                traffic: 2100
            }
        ],
        opportunities: [
            {
                domain: 'wired.com',
                domainAuthority: 91,
                relevance: 'high',
                difficulty: 'hard',
                estimatedTraffic: 5000,
                contactEmail: '<EMAIL>'
            },
            {
                domain: 'entrepreneur.com',
                domainAuthority: 87,
                relevance: 'high',
                difficulty: 'medium',
                estimatedTraffic: 3200,
                contactEmail: '<EMAIL>'
            },
            {
                domain: 'smallbusiness.com',
                domainAuthority: 72,
                relevance: 'medium',
                difficulty: 'easy',
                estimatedTraffic: 1800,
                contactEmail: '<EMAIL>'
            }
        ]
    });

    const getStatusColor = (status) => {
        switch (status) {
            case 'active': return 'bg-green-100 text-green-800';
            case 'lost': return 'bg-red-100 text-red-800';
            case 'toxic': return 'bg-yellow-100 text-yellow-800';
            case 'new': return 'bg-blue-100 text-blue-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusIcon = (status) => {
        switch (status) {
            case 'active': return <CheckCircleIcon className="h-4 w-4" />;
            case 'lost': return <XCircleIcon className="h-4 w-4" />;
            case 'toxic': return <ExclamationTriangleIcon className="h-4 w-4" />;
            case 'new': return <ArrowTrendingUpIcon className="h-4 w-4" />;
            default: return <CheckCircleIcon className="h-4 w-4" />;
        }
    };

    const getDifficultyColor = (difficulty) => {
        switch (difficulty) {
            case 'easy': return 'bg-green-100 text-green-800';
            case 'medium': return 'bg-yellow-100 text-yellow-800';
            case 'hard': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getRelevanceColor = (relevance) => {
        switch (relevance) {
            case 'high': return 'bg-green-100 text-green-800';
            case 'medium': return 'bg-yellow-100 text-yellow-800';
            case 'low': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const filteredBacklinks = backlinkData.backlinks.filter(backlink => {
        const matchesSearch = backlink.sourceUrl.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            backlink.anchorText.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesFilter = filterStatus === 'all' || backlink.status === filterStatus;
        return matchesSearch && matchesFilter;
    });

    return (
        <div className="p-6 bg-gray-50 min-h-screen">
            {/* Header */}
            <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900">Backlink Management</h1>
                <p className="text-gray-600 mt-2">Monitor and manage your website's backlink profile</p>
            </div>

            {/* Overview Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-8">
                <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-600">Total Backlinks</p>
                            <p className="text-xl font-bold text-gray-900">{backlinkData.overview.totalBacklinks.toLocaleString()}</p>
                        </div>
                        <LinkIcon className="h-6 w-6 text-blue-500" />
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-600">Referring Domains</p>
                            <p className="text-xl font-bold text-gray-900">{backlinkData.overview.totalDomains.toLocaleString()}</p>
                        </div>
                        <EyeIcon className="h-6 w-6 text-green-500" />
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-600">Domain Authority</p>
                            <p className="text-xl font-bold text-gray-900">{backlinkData.overview.domainAuthority}</p>
                        </div>
                        <ArrowTrendingUpIcon className="h-6 w-6 text-purple-500" />
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-600">New Backlinks</p>
                            <p className="text-xl font-bold text-green-600">+{backlinkData.overview.newBacklinks}</p>
                        </div>
                        <ArrowTrendingUpIcon className="h-6 w-6 text-green-500" />
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-600">Lost Backlinks</p>
                            <p className="text-xl font-bold text-red-600">-{backlinkData.overview.lostBacklinks}</p>
                        </div>
                        <ArrowTrendingDownIcon className="h-6 w-6 text-red-500" />
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-600">Toxic Links</p>
                            <p className="text-xl font-bold text-yellow-600">{backlinkData.overview.toxicBacklinks}</p>
                        </div>
                        <ExclamationTriangleIcon className="h-6 w-6 text-yellow-500" />
                    </div>
                </div>
            </div>

            {/* Tabs */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div className="border-b border-gray-200">
                    <nav className="flex space-x-8 px-6">
                        {[
                            { id: 'overview', label: 'Backlinks Overview' },
                            { id: 'opportunities', label: 'Link Opportunities' },
                            { id: 'disavow', label: 'Disavow Tool' }
                        ].map(tab => (
                            <button
                                key={tab.id}
                                onClick={() => setSelectedTab(tab.id)}
                                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                    selectedTab === tab.id
                                        ? 'border-orange-500 text-orange-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                            >
                                {tab.label}
                            </button>
                        ))}
                    </nav>
                </div>

                {/* Tab Content */}
                <div className="p-6">
                    {selectedTab === 'overview' && (
                        <div>
                            {/* Filters */}
                            <div className="flex flex-col md:flex-row gap-4 mb-6">
                                <div className="flex-1">
                                    <div className="relative">
                                        <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                                        <input
                                            type="text"
                                            placeholder="Search backlinks by domain or anchor text..."
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-black placeholder-gray-500 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                        />
                                    </div>
                                </div>
                                <div className="flex gap-4">
                                    <select
                                        value={filterStatus}
                                        onChange={(e) => setFilterStatus(e.target.value)}
                                        className="border border-gray-300 rounded-lg px-4 py-2 bg-white text-black focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                    >
                                        <option value="all">All Status</option>
                                        <option value="active">Active</option>
                                        <option value="lost">Lost</option>
                                        <option value="toxic">Toxic</option>
                                        <option value="new">New</option>
                                    </select>
                                    <button className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                                        Export
                                    </button>
                                </div>
                            </div>

                            {/* Backlinks Table */}
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gray-50 border-b border-gray-200">
                                        <tr>
                                            <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">Source</th>
                                            <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">Anchor Text</th>
                                            <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">DA/PA</th>
                                            <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">Status</th>
                                            <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">Traffic</th>
                                            <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">First Seen</th>
                                            <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-200">
                                        {filteredBacklinks.map((backlink) => (
                                            <tr key={backlink.id} className="hover:bg-gray-50">
                                                <td className="py-3 px-4">
                                                    <div>
                                                        <p className="text-sm font-medium text-blue-600 truncate max-w-xs">
                                                            {backlink.sourceUrl}
                                                        </p>
                                                        <p className="text-xs text-gray-500 truncate max-w-xs">
                                                            → {backlink.targetUrl}
                                                        </p>
                                                    </div>
                                                </td>
                                                <td className="py-3 px-4">
                                                    <span className="text-sm text-gray-900 truncate max-w-xs block">
                                                        {backlink.anchorText}
                                                    </span>
                                                    <span className="text-xs text-gray-500">
                                                        {backlink.type}
                                                    </span>
                                                </td>
                                                <td className="py-3 px-4">
                                                    <div className="text-sm">
                                                        <span className="font-medium text-gray-900">{backlink.domainAuthority}</span>
                                                        <span className="text-gray-500">/</span>
                                                        <span className="text-gray-700">{backlink.pageAuthority}</span>
                                                    </div>
                                                </td>
                                                <td className="py-3 px-4">
                                                    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(backlink.status)}`}>
                                                        {getStatusIcon(backlink.status)}
                                                        {backlink.status}
                                                    </span>
                                                </td>
                                                <td className="py-3 px-4">
                                                    <span className="text-sm text-gray-900">{backlink.traffic}</span>
                                                </td>
                                                <td className="py-3 px-4">
                                                    <span className="text-sm text-gray-900">{backlink.firstSeen}</span>
                                                </td>
                                                <td className="py-3 px-4">
                                                    <div className="flex items-center space-x-2">
                                                        <button className="text-blue-600 hover:text-blue-800 text-sm">
                                                            View
                                                        </button>
                                                        {backlink.status === 'toxic' && (
                                                            <button className="text-red-600 hover:text-red-800 text-sm">
                                                                Disavow
                                                            </button>
                                                        )}
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    )}

                    {selectedTab === 'opportunities' && (
                        <div>
                            <div className="mb-6">
                                <h3 className="text-lg font-semibold text-gray-900 mb-2">Link Building Opportunities</h3>
                                <p className="text-gray-600">High-quality domains where you could potentially earn backlinks</p>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {backlinkData.opportunities.map((opportunity, index) => (
                                    <div key={index} className="border border-gray-200 rounded-lg p-6">
                                        <div className="flex items-start justify-between mb-4">
                                            <h4 className="font-semibold text-gray-900">{opportunity.domain}</h4>
                                            <span className="text-sm font-medium text-gray-900">DA {opportunity.domainAuthority}</span>
                                        </div>
                                        
                                        <div className="space-y-3 mb-4">
                                            <div className="flex justify-between items-center">
                                                <span className="text-sm text-gray-600">Relevance:</span>
                                                <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getRelevanceColor(opportunity.relevance)}`}>
                                                    {opportunity.relevance}
                                                </span>
                                            </div>
                                            <div className="flex justify-between items-center">
                                                <span className="text-sm text-gray-600">Difficulty:</span>
                                                <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(opportunity.difficulty)}`}>
                                                    {opportunity.difficulty}
                                                </span>
                                            </div>
                                            <div className="flex justify-between items-center">
                                                <span className="text-sm text-gray-600">Est. Traffic:</span>
                                                <span className="text-sm font-medium text-gray-900">{opportunity.estimatedTraffic.toLocaleString()}</span>
                                            </div>
                                        </div>

                                        <div className="border-t border-gray-200 pt-4">
                                            <p className="text-xs text-gray-500 mb-3">Contact: {opportunity.contactEmail}</p>
                                            <div className="flex space-x-2">
                                                <button className="flex-1 bg-orange-500 text-white px-3 py-2 rounded text-sm hover:bg-orange-600 transition-colors">
                                                    Reach Out
                                                </button>
                                                <button className="px-3 py-2 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50 transition-colors">
                                                    Save
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    {selectedTab === 'disavow' && (
                        <div>
                            <div className="mb-6">
                                <h3 className="text-lg font-semibold text-gray-900 mb-2">Disavow Toxic Links</h3>
                                <p className="text-gray-600">Manage and disavow harmful backlinks that could negatively impact your SEO</p>
                            </div>

                            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                                <div className="flex">
                                    <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mt-0.5" />
                                    <div className="ml-3">
                                        <h4 className="text-sm font-medium text-yellow-800">Use with Caution</h4>
                                        <p className="text-sm text-yellow-700 mt-1">
                                            Only disavow links that are clearly spammy or harmful. Google is generally good at ignoring bad links automatically.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div className="space-y-4">
                                <div className="flex justify-between items-center">
                                    <h4 className="font-medium text-gray-900">Toxic Backlinks ({backlinkData.overview.toxicBacklinks})</h4>
                                    <button className="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors">
                                        Generate Disavow File
                                    </button>
                                </div>

                                <div className="border border-gray-200 rounded-lg overflow-hidden">
                                    <table className="w-full">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">
                                                    <input type="checkbox" className="rounded border-gray-300" />
                                                </th>
                                                <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">Source Domain</th>
                                                <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">Reason</th>
                                                <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">Risk Level</th>
                                                <th className="text-left py-3 px-4 text-sm font-medium text-gray-600">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody className="divide-y divide-gray-200">
                                            {backlinkData.backlinks.filter(link => link.status === 'toxic').map((link) => (
                                                <tr key={link.id}>
                                                    <td className="py-3 px-4">
                                                        <input type="checkbox" className="rounded border-gray-300" />
                                                    </td>
                                                    <td className="py-3 px-4">
                                                        <span className="text-sm text-gray-900">{new URL(link.sourceUrl).hostname}</span>
                                                    </td>
                                                    <td className="py-3 px-4">
                                                        <span className="text-sm text-gray-600">Low quality domain, spammy content</span>
                                                    </td>
                                                    <td className="py-3 px-4">
                                                        <span className="inline-flex px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                            High
                                                        </span>
                                                    </td>
                                                    <td className="py-3 px-4">
                                                        <button className="text-red-600 hover:text-red-800 text-sm">
                                                            Add to Disavow
                                                        </button>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Backlink Building Tips */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-4">Backlink Building Best Practices</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                    <div>
                        <h4 className="font-medium text-blue-800 mb-2">Quality over Quantity</h4>
                        <p className="text-blue-700">Focus on earning high-quality backlinks from authoritative, relevant websites rather than pursuing many low-quality links.</p>
                    </div>
                    <div>
                        <h4 className="font-medium text-blue-800 mb-2">Natural Link Building</h4>
                        <p className="text-blue-700">Create valuable content that naturally attracts links. Guest posting, resource pages, and broken link building are effective strategies.</p>
                    </div>
                    <div>
                        <h4 className="font-medium text-blue-800 mb-2">Monitor Regularly</h4>
                        <p className="text-blue-700">Regularly monitor your backlink profile to identify new opportunities, track lost links, and spot potentially harmful links.</p>
                    </div>
                </div>
            </div>
        </div>
    );
}
