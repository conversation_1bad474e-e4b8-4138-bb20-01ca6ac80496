'use client';

import { useState, useEffect } from 'react';
import { 
    DocumentTextIcon, 
    CheckCircleIcon,
    ExclamationTriangleIcon,
    XCircleIcon,
    ArrowDownTrayIcon,
    ArrowPathIcon,
    GlobeAltIcon,
    ClockIcon,
    EyeIcon
} from '@heroicons/react/24/outline';

export default function SitemapManager() {
    const [sitemapData, setSitemapData] = useState({
        mainSitemap: {
            url: '/sitemap.xml',
            status: 'active',
            lastGenerated: '2024-01-18',
            totalUrls: 1247,
            submitted: true,
            indexed: 1156,
            errors: 0
        },
        sitemaps: [
            {
                id: 1,
                name: 'Products Sitemap',
                url: '/sitemap-products.xml',
                type: 'products',
                status: 'active',
                lastGenerated: '2024-01-18',
                totalUrls: 856,
                submitted: true,
                indexed: 823,
                errors: 2,
                priority: 'high'
            },
            {
                id: 2,
                name: 'Categories Sitemap',
                url: '/sitemap-categories.xml',
                type: 'categories',
                status: 'active',
                lastGenerated: '2024-01-18',
                totalUrls: 45,
                submitted: true,
                indexed: 45,
                errors: 0,
                priority: 'high'
            },
            {
                id: 3,
                name: 'Brands Sitemap',
                url: '/sitemap-brands.xml',
                type: 'brands',
                status: 'active',
                lastGenerated: '2024-01-18',
                totalUrls: 127,
                submitted: true,
                indexed: 119,
                errors: 0,
                priority: 'medium'
            },
            {
                id: 4,
                name: 'Static Pages Sitemap',
                url: '/sitemap-pages.xml',
                type: 'pages',
                status: 'active',
                lastGenerated: '2024-01-15',
                totalUrls: 12,
                submitted: true,
                indexed: 12,
                errors: 0,
                priority: 'medium'
            },
            {
                id: 5,
                name: 'Blog Sitemap',
                url: '/sitemap-blog.xml',
                type: 'blog',
                status: 'pending',
                lastGenerated: '2024-01-10',
                totalUrls: 0,
                submitted: false,
                indexed: 0,
                errors: 1,
                priority: 'low'
            }
        ],
        searchEngines: [
            {
                name: 'Google Search Console',
                status: 'submitted',
                lastSubmitted: '2024-01-18',
                indexed: 1156,
                errors: 2
            },
            {
                name: 'Bing Webmaster Tools',
                status: 'submitted',
                lastSubmitted: '2024-01-18',
                indexed: 1089,
                errors: 1
            },
            {
                name: 'Yandex Webmaster',
                status: 'not_submitted',
                lastSubmitted: null,
                indexed: 0,
                errors: 0
            }
        ]
    });

    const [selectedSitemap, setSelectedSitemap] = useState(null);

    const getStatusColor = (status) => {
        switch (status) {
            case 'active': return 'bg-green-100 text-green-800';
            case 'pending': return 'bg-yellow-100 text-yellow-800';
            case 'error': return 'bg-red-100 text-red-800';
            case 'submitted': return 'bg-blue-100 text-blue-800';
            case 'not_submitted': return 'bg-gray-100 text-gray-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusIcon = (status) => {
        switch (status) {
            case 'active':
            case 'submitted':
                return <CheckCircleIcon className="h-4 w-4" />;
            case 'pending':
                return <ClockIcon className="h-4 w-4" />;
            case 'error':
                return <XCircleIcon className="h-4 w-4" />;
            case 'not_submitted':
                return <ExclamationTriangleIcon className="h-4 w-4" />;
            default:
                return <CheckCircleIcon className="h-4 w-4" />;
        }
    };

    const getPriorityColor = (priority) => {
        switch (priority) {
            case 'high': return 'bg-red-100 text-red-800';
            case 'medium': return 'bg-yellow-100 text-yellow-800';
            case 'low': return 'bg-green-100 text-green-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const generateSitemap = (type) => {
        // Simulate sitemap generation
        setSitemapData(prev => ({
            ...prev,
            sitemaps: prev.sitemaps.map(sitemap => 
                sitemap.type === type 
                    ? { ...sitemap, lastGenerated: new Date().toISOString().split('T')[0], status: 'active' }
                    : sitemap
            )
        }));
    };

    const submitToSearchEngine = (engine) => {
        // Simulate submission to search engine
        setSitemapData(prev => ({
            ...prev,
            searchEngines: prev.searchEngines.map(se => 
                se.name === engine 
                    ? { ...se, status: 'submitted', lastSubmitted: new Date().toISOString().split('T')[0] }
                    : se
            )
        }));
    };

    return (
        <div className="p-6 bg-gray-50 min-h-screen">
            {/* Header */}
            <div className="flex justify-between items-center mb-8">
                <div>
                    <h1 className="text-3xl font-bold text-gray-900">Sitemap Management</h1>
                    <p className="text-gray-600 mt-2">Generate, manage, and submit sitemaps to search engines</p>
                </div>
                <div className="flex items-center space-x-4">
                    <button className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors flex items-center">
                        <ArrowPathIcon className="h-4 w-4 mr-2" />
                        Regenerate All
                    </button>
                </div>
            </div>

            {/* Main Sitemap Overview */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">Main Sitemap Overview</h3>
                    <GlobeAltIcon className="h-6 w-6 text-blue-500" />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="text-center">
                        <p className="text-2xl font-bold text-gray-900">{sitemapData.mainSitemap.totalUrls}</p>
                        <p className="text-sm text-gray-600">Total URLs</p>
                    </div>
                    <div className="text-center">
                        <p className="text-2xl font-bold text-green-600">{sitemapData.mainSitemap.indexed}</p>
                        <p className="text-sm text-gray-600">Indexed</p>
                    </div>
                    <div className="text-center">
                        <p className="text-2xl font-bold text-red-600">{sitemapData.mainSitemap.errors}</p>
                        <p className="text-sm text-gray-600">Errors</p>
                    </div>
                    <div className="text-center">
                        <p className="text-2xl font-bold text-blue-600">
                            {Math.round((sitemapData.mainSitemap.indexed / sitemapData.mainSitemap.totalUrls) * 100)}%
                        </p>
                        <p className="text-sm text-gray-600">Index Rate</p>
                    </div>
                </div>
                <div className="mt-4 flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(sitemapData.mainSitemap.status)}`}>
                            {getStatusIcon(sitemapData.mainSitemap.status)}
                            {sitemapData.mainSitemap.status}
                        </span>
                        <span className="text-sm text-gray-600">
                            Last generated: {sitemapData.mainSitemap.lastGenerated}
                        </span>
                    </div>
                    <div className="flex space-x-2">
                        <button className="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                            <EyeIcon className="h-4 w-4 mr-1" />
                            View
                        </button>
                        <button className="text-green-600 hover:text-green-800 text-sm flex items-center">
                            <ArrowDownTrayIcon className="h-4 w-4 mr-1" />
                            Download
                        </button>
                    </div>
                </div>
            </div>

            {/* Individual Sitemaps */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
                <div className="p-6 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-900">Individual Sitemaps</h3>
                    <p className="text-sm text-gray-600 mt-1">Manage specific sitemaps for different content types</p>
                </div>
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead className="bg-gray-50 border-b border-gray-200">
                            <tr>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Sitemap</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Type</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">URLs</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Indexed</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Status</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Priority</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Last Generated</th>
                                <th className="text-left py-4 px-6 text-sm font-medium text-gray-600">Actions</th>
                            </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                            {sitemapData.sitemaps.map((sitemap) => (
                                <tr key={sitemap.id} className="hover:bg-gray-50">
                                    <td className="py-4 px-6">
                                        <div>
                                            <p className="text-sm font-medium text-gray-900">{sitemap.name}</p>
                                            <p className="text-xs text-blue-600">{sitemap.url}</p>
                                        </div>
                                    </td>
                                    <td className="py-4 px-6">
                                        <span className="inline-flex px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 capitalize">
                                            {sitemap.type}
                                        </span>
                                    </td>
                                    <td className="py-4 px-6">
                                        <span className="text-sm text-gray-900">{sitemap.totalUrls}</span>
                                    </td>
                                    <td className="py-4 px-6">
                                        <div className="flex items-center">
                                            <span className="text-sm text-gray-900">{sitemap.indexed}</span>
                                            {sitemap.totalUrls > 0 && (
                                                <span className="ml-2 text-xs text-gray-500">
                                                    ({Math.round((sitemap.indexed / sitemap.totalUrls) * 100)}%)
                                                </span>
                                            )}
                                        </div>
                                    </td>
                                    <td className="py-4 px-6">
                                        <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(sitemap.status)}`}>
                                            {getStatusIcon(sitemap.status)}
                                            {sitemap.status}
                                        </span>
                                        {sitemap.errors > 0 && (
                                            <span className="ml-2 text-xs text-red-600">
                                                {sitemap.errors} errors
                                            </span>
                                        )}
                                    </td>
                                    <td className="py-4 px-6">
                                        <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(sitemap.priority)}`}>
                                            {sitemap.priority}
                                        </span>
                                    </td>
                                    <td className="py-4 px-6">
                                        <span className="text-sm text-gray-900">{sitemap.lastGenerated}</span>
                                    </td>
                                    <td className="py-4 px-6">
                                        <div className="flex items-center space-x-2">
                                            <button
                                                onClick={() => generateSitemap(sitemap.type)}
                                                className="text-blue-600 hover:text-blue-800 text-sm"
                                            >
                                                Generate
                                            </button>
                                            <button className="text-green-600 hover:text-green-800 text-sm">
                                                View
                                            </button>
                                            <button
                                                onClick={() => setSelectedSitemap(sitemap)}
                                                className="text-gray-600 hover:text-gray-800 text-sm"
                                            >
                                                Details
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Search Engine Submission */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
                <div className="p-6 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-900">Search Engine Submission</h3>
                    <p className="text-sm text-gray-600 mt-1">Submit sitemaps to major search engines</p>
                </div>
                <div className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        {sitemapData.searchEngines.map((engine, index) => (
                            <div key={index} className="border border-gray-200 rounded-lg p-4">
                                <div className="flex items-center justify-between mb-3">
                                    <h4 className="font-medium text-gray-900">{engine.name}</h4>
                                    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(engine.status)}`}>
                                        {getStatusIcon(engine.status)}
                                        {engine.status.replace('_', ' ')}
                                    </span>
                                </div>
                                
                                <div className="space-y-2 text-sm mb-4">
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Indexed URLs:</span>
                                        <span className="text-gray-900">{engine.indexed}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Errors:</span>
                                        <span className="text-gray-900">{engine.errors}</span>
                                    </div>
                                    {engine.lastSubmitted && (
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Last Submitted:</span>
                                            <span className="text-gray-900">{engine.lastSubmitted}</span>
                                        </div>
                                    )}
                                </div>

                                <button
                                    onClick={() => submitToSearchEngine(engine.name)}
                                    className={`w-full px-3 py-2 rounded text-sm font-medium transition-colors ${
                                        engine.status === 'submitted'
                                            ? 'bg-green-100 text-green-800 hover:bg-green-200'
                                            : 'bg-orange-500 text-white hover:bg-orange-600'
                                    }`}
                                >
                                    {engine.status === 'submitted' ? 'Resubmit' : 'Submit Sitemap'}
                                </button>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            {/* Sitemap Detail Modal */}
            {selectedSitemap && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
                        <div className="p-6 border-b border-gray-200">
                            <div className="flex items-center justify-between">
                                <h3 className="text-lg font-semibold text-gray-900">{selectedSitemap.name} Details</h3>
                                <button
                                    onClick={() => setSelectedSitemap(null)}
                                    className="text-gray-400 hover:text-gray-600"
                                >
                                    <XCircleIcon className="h-6 w-6" />
                                </button>
                            </div>
                        </div>
                        <div className="p-6">
                            <div className="grid grid-cols-2 gap-4 mb-6">
                                <div>
                                    <h4 className="font-medium text-gray-900 mb-3">Sitemap Information</h4>
                                    <div className="space-y-2 text-sm">
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">URL:</span>
                                            <span className="text-blue-600">{selectedSitemap.url}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Type:</span>
                                            <span className="text-gray-900 capitalize">{selectedSitemap.type}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Priority:</span>
                                            <span className="text-gray-900 capitalize">{selectedSitemap.priority}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Last Generated:</span>
                                            <span className="text-gray-900">{selectedSitemap.lastGenerated}</span>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <h4 className="font-medium text-gray-900 mb-3">Performance Metrics</h4>
                                    <div className="space-y-2 text-sm">
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Total URLs:</span>
                                            <span className="text-gray-900">{selectedSitemap.totalUrls}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Indexed:</span>
                                            <span className="text-green-600">{selectedSitemap.indexed}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Errors:</span>
                                            <span className="text-red-600">{selectedSitemap.errors}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Index Rate:</span>
                                            <span className="text-gray-900">
                                                {selectedSitemap.totalUrls > 0 
                                                    ? Math.round((selectedSitemap.indexed / selectedSitemap.totalUrls) * 100)
                                                    : 0}%
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="flex space-x-3">
                                <button
                                    onClick={() => generateSitemap(selectedSitemap.type)}
                                    className="flex-1 bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors"
                                >
                                    Regenerate Sitemap
                                </button>
                                <button className="flex-1 border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                                    View XML
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Sitemap Tips */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-4">Sitemap Best Practices</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                    <div>
                        <h4 className="font-medium text-blue-800 mb-2">Regular Updates</h4>
                        <p className="text-blue-700">Update sitemaps automatically when new content is added or existing content is modified.</p>
                    </div>
                    <div>
                        <h4 className="font-medium text-blue-800 mb-2">Size Limits</h4>
                        <p className="text-blue-700">Keep sitemaps under 50MB and 50,000 URLs. Use sitemap index files for larger sites.</p>
                    </div>
                    <div>
                        <h4 className="font-medium text-blue-800 mb-2">Submit to Search Engines</h4>
                        <p className="text-blue-700">Submit sitemaps to Google Search Console, Bing Webmaster Tools, and other relevant search engines.</p>
                    </div>
                </div>
            </div>
        </div>
    );
}
