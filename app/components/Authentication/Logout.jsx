'use client';

import { useRouter } from 'next/navigation';
import { FaSignOutAlt } from 'react-icons/fa';

export default function Logout({ className = '', iconOnly = false }) {
    const router = useRouter();

    const handleLogout = async () => {
        try {
            // Get current user role from tokens
            const superAdminToken = localStorage.getItem('superAdminAuthToken');
            const partnerToken = localStorage.getItem('partnerAuthToken');
            const userToken = localStorage.getItem('userAuthToken');

            // Clear all auth tokens from localStorage
            localStorage.removeItem('superAdminAuthToken');
            localStorage.removeItem('partnerAuthToken');
            localStorage.removeItem('userAuthToken');
            localStorage.removeItem('cartId');

            // Clear all auth tokens from cookies
            document.cookie = 'superAdminAuthToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
            document.cookie = 'partnerAuthToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
            document.cookie = 'userAuthToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';

            // Redirect based on role with page reload to refresh context
            if (superAdminToken) {
                window.location.href = '/login/super-admin';
            } else if (partnerToken) {
                // Partners should go to home page with reload to refresh context
                window.location.href = '/';
            } else if (userToken) {
                // Customers should go to home page with reload to refresh context
                window.location.href = '/';
            } else {
                window.location.href = '/';
            }
        } catch (error) {
            console.error('Logout error:', error);
            // If there's an error, still try to redirect to home with page reload
            window.location.href = '/';
        }
    };

    if (iconOnly) {
        return (
            <button
                onClick={handleLogout}
                className={`p-2 text-gray-600 hover:text-orange-500 transition-colors duration-200 ${className}`}
                title="Logout"
            >
                <FaSignOutAlt className="w-5 h-5" />
            </button>
        );
    }

    return (
        <button
            onClick={handleLogout}
            className={`flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-orange-500 transition-colors duration-200 ${className}`}
        >
            <FaSignOutAlt className="w-5 h-5" />
            <span>Logout</span>
        </button>
    );
} 