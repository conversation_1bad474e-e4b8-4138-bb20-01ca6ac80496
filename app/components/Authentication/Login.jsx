'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/app/context/AuthContext';

export const PostLogin = async function (email, password) {
    try {
        console.log('Sending login data:', { email, password });

        const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({ email, password }),
        });

        const data = await response.json();
        console.log('Login response:', data);

        if (!response.ok) {
            throw new Error(data.message || 'Login failed');
        }

        return {
            success: true,
            data: data
        };
    } catch (error) {
        console.error('Login error:', error);
        throw error;
    }
};

// Add new function to create cart
const createCart = async (token) => {
    try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/carts`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        });

        const data = await response.json();
        if (data.success && data.data?.cart?.id) {
            localStorage.setItem('cartId', data.data.cart.id);
            return true;
        }
        return false;
    } catch (error) {
        console.error('Error creating cart:', error);
        return false;
    }
};

// title: string, subHeading: string, description: string, partner: boolean, user: boolean
export default function Login({ title, subHeading, description, partner, user, superAdmin, admin }) {
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [logged, setLogged] = useState(false);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [shake, setShake] = useState(false);
    const { setUser } = useAuth();

    const url = usePathname();
    const searchParams = useSearchParams();
    // console.log(url.split('/'));

    const [, authType, userRole] = url.split('/');
    console.log(authType, userRole);

    const router = useRouter();

    // Get redirect parameter from URL
    const redirectParam = searchParams.get('redirect');


    useEffect(() => {
        if (error) {
            setShake(true);
            const timer = setTimeout(() => setShake(false), 500);
            return () => clearTimeout(timer);
        }
    }, [error]);

    function velidate() {
        let isValid = true;
        let errorMessage = '';

        // Email validation
        if (!email?.trim()) {
            errorMessage = "Email is required";
            isValid = false;
        } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(email.trim())) {
            errorMessage = "Invalid email address";
            isValid = false;
        }

        // Password validation
        if (!password) {
            errorMessage = "Password is required";
            isValid = false;
        } else if (password.length < 8) {
            errorMessage = "Password must be at least 8 characters";
            isValid = false;
        }/*  else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
            errorMessage = "Password must contain at least one uppercase letter, one lowercase letter, and one number";
            isValid = false;
        } */

        setError(errorMessage);
        return isValid;
    }

    async function handleSubmit(e) {
        e.preventDefault();
        setError('');
        setLoading(true);

        try {
            if (!velidate()) {
                setLoading(false);
                return;
            }

            const response = await PostLogin(email, password);
            console.log(response);

            if (response.success) {
                const { token, user } = response.data;
                const userRole = user.role.slug.toLowerCase();
                setUser(response.data);

                // Store token in both localStorage and cookies
                if (userRole === 'superadmin') {
                    localStorage.setItem('superAdminAuthToken', token);
                    document.cookie = `superAdminAuthToken=${token}; path=/; max-age=86400`; // 24 hours
                } else if (userRole === 'vendor') {
                    localStorage.setItem('partnerAuthToken', token);
                    document.cookie = `partnerAuthToken=${token}; path=/; max-age=86400`; // 24 hours
                    // Create cart for partner after successful login (partners can now buy products)
                    await createCart(token);
                } else if (userRole === 'buyer') {
                    localStorage.setItem('userAuthToken', token);
                    document.cookie = `userAuthToken=${token}; path=/; max-age=86400`; // 24 hours
                    // Create cart for buyer after successful login
                    await createCart(token);
                }

                setLogged(true);
                setTimeout(() => {
                    // Check if there's a redirect parameter
                    if (redirectParam) {
                        try {
                            const decodedRedirect = decodeURIComponent(redirectParam);
                            console.log('🔄 Redirecting with page reload to:', decodedRedirect);
                            // Use window.location.href for full page reload to refresh all contexts
                            window.location.href = decodedRedirect;
                            return;
                        } catch (error) {
                            console.error('Error decoding redirect URL:', error);
                            // Fall through to default redirect
                        }
                    }

                    // Default redirect based on role if no redirect parameter
                    if (userRole === 'buyer') {
                        // Redirect normal users to products page instead of customer-admin
                        router.push('/products');
                    } else if (userRole === 'vendor') {
                        router.push('/partner-admin');
                    } else if (userRole === 'superadmin') {
                        router.push('/super-admin');
                    } else {
                        router.push(`/${userRole}`);
                    }
                }, 2000);
            } else {
                setError(response.message || 'Login failed. Please check your credentials.');
            }
        } catch (error) {
            setError(error.message || 'Login failed. Please try again.');
        } finally {
            setLoading(false);
        }
    }

    // task! login as superadmin (post) | review

    // task! vendor login (post) | review

    return (
        <section className="py-32 relative overflow-hidden min-h-svh">
            {/* Enhanced background with more vibrant gradients */}
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-100 via-indigo-100 to-orange-100"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_top_right,rgba(79,70,229,0.2),transparent_60%)]"></div>
            <div className="absolute -top-24 -left-24 w-96 h-96 bg-yellow-200 rounded-full filter blur-3xl opacity-30"></div>
            <div className="absolute bottom-0 right-0 w-80 h-80 bg-orange-200 rounded-full filter blur-3xl opacity-30"></div>

            {/* Decorative elements */}
            <motion.div
                className="absolute top-20 right-20 h-12 w-12 rounded-full bg-orange-500/20 hidden lg:block"
                animate={{ y: [0, 15, 0] }}
                transition={{ repeat: Infinity, duration: 3, ease: "easeInOut" }}
                aria-hidden="true"
            />

            <motion.div
                className="absolute bottom-20 left-20 h-8 w-8 rounded-full bg-indigo-500/20 hidden lg:block"
                animate={{ y: [0, -15, 0] }}
                transition={{ repeat: Infinity, duration: 2.5, ease: "easeInOut" }}
                aria-hidden="true"
            />

            <div className="container max-w-[500px] mx-auto px-6 md:px-12 relative z-10 bg-white/30 backdrop-blur-lg p-8 rounded-2xl sm:shadow-2xl border border-white/20 sm:shadow-red-500/40">
                <motion.div
                    className="max-w-3xl mx-auto text-center"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.8, ease: "easeOut" }}
                >
                    <div className="mb-8">
                        <motion.div
                            className="w-20 h-20 rounded-2xl bg-gradient-to-r from-yellow-300 to-orange-400 flex items-center justify-center mx-auto shadow-xl shadow-red-300/40"
                            whileHover={{ scale: 1.05, rotate: 5 }}
                            transition={{ type: "spring", stiffness: 400, damping: 10 }}
                            aria-hidden="true"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                        </motion.div>
                    </div>

                    <motion.h1
                        className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 tracking-tight leading-tight"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.8, delay: 0.1, ease: "easeOut" }}>
                        <span className="text-indigo-600">
                            {title}
                        </span>
                    </motion.h1>

                    <motion.p
                        className="text-base sm:text-lg text-indigo-500 mb-8 md:mb-12 leading-relaxed font-medium"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}>
                        {subHeading}
                    </motion.p>

                    <AnimatePresence mode="wait">
                        {!logged ? (
                            <motion.div
                                key="form"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                exit={{ opacity: 0 }}
                                transition={{ duration: 0.5 }}>
                                {/* Form */}
                                <motion.form
                                    onSubmit={handleSubmit}
                                    className="flex flex-col gap-3 max-w-xl mx-auto"
                                    animate={{ x: shake ? [-10, 10, -7, 7, -3, 3, 0] : 0 }}
                                    transition={{ duration: 0.5 }}>
                                    <div className="relative w-full">
                                        <label htmlFor="email" className="sr-only">Email address</label>
                                        <input
                                            id="email"
                                            name="email"
                                            type="email"
                                            value={email}
                                            onChange={(e) => setEmail(e.target.value)}
                                            placeholder="<EMAIL>"
                                            className="w-full px-4 sm:px-10 py-4 sm:py-5 rounded-xl bg-white/90 border-2 border-orange-200 focus:outline-none focus:ring-3 focus:ring-orange-500/50 focus:border-orange-500 shadow-lg shadow-red-100/30 transition-all duration-300 text-base font-medium placeholder:text-gray-400 text-gray-600"
                                            disabled={loading}
                                            required
                                            aria-invalid={error ? "true" : "false"}
                                            aria-describedby={error ? "login-error" : undefined}
                                        />
                                    </div>

                                    <div className="relative w-full">
                                        <label htmlFor="password" className="sr-only">Password</label>
                                        <input
                                            id="password"
                                            name="password"
                                            type="password"
                                            value={password}
                                            onChange={(e) => setPassword(e.target.value)}
                                            placeholder="Enter your password here..."
                                            className="w-full px-4 sm:px-10 py-4 sm:py-5 rounded-xl bg-white/90 border-2 border-orange-200 focus:outline-none focus:ring-3 focus:ring-orange-500/50 focus:border-orange-500 shadow-lg shadow-red-100/30 transition-all duration-300 text-base font-medium placeholder:text-gray-400 text-gray-600"
                                            disabled={loading}
                                            required
                                            minLength={8}
                                            aria-invalid={error ? "true" : "false"}
                                            aria-describedby={error ? "login-error" : undefined}
                                        />
                                    </div>

                                    {error && <p id="login-error" className="text-sm font-medium text-red-500 my-2 text-center" role="alert">{error}</p>}

                                    <motion.button
                                        type="submit"
                                        disabled={loading}
                                        whileHover={{ scale: loading ? 1 : 1.03 }}
                                        whileTap={{ scale: loading ? 1 : 0.98 }}
                                        className={`px-8 py-3 sm:py-4 bg-gradient-to-r from-yellow-300 to-orange-500 text-white font-bold text-base sm:text-lg rounded-xl shadow-xl shadow-red-500/20 hover:shadow-red-500/40 transition duration-300 whitespace-nowrap ${loading ? 'opacity-80 cursor-not-allowed' : ''}`}>
                                        {loading ? (
                                            <div className="flex items-center justify-center">
                                                <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" aria-hidden="true">
                                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                </svg>
                                                <span>Logging in...</span>
                                            </div>
                                        ) : 'Submit'}
                                    </motion.button>
                                </motion.form>
                            </motion.div>
                        ) : (
                            <motion.div
                                key="success"
                                initial={{ opacity: 0, scale: 0.9 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.5 }}
                                className="bg-gradient-to-r from-green-50 to-emerald-50 border-l-4 border-green-500 p-6 sm:p-8 rounded-xl shadow-2xl"
                                role="alert"
                                aria-live="polite"
                            >
                                <div className="flex items-center">
                                    <div className="bg-green-100 rounded-full p-3 mr-5">
                                        <svg className="h-8 w-8 sm:h-10 sm:w-10 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                        </svg>
                                    </div>
                                    <div className="text-left">
                                        <h2 className="font-bold text-xl sm:text-2xl text-gray-900 mb-2">Successfully Logged In</h2>
                                        <p className="text-gray-700 text-base sm:text-lg">Check your email for confirmation and exclusive offers.</p>
                                    </div>
                                </div>
                            </motion.div>
                        )}
                    </AnimatePresence>

                    <motion.div
                        className="my-4 text-sm sm:text-base text-gray-500 flex items-center justify-center gap-2"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.8, delay: 0.3 }}>
                        <p>Forgot Password? Please <Link href={`/forgot-password/${partner ? 'partner' : 'user'}`} className="text-orange-400 hover:text-orange-600 focus:outline-none focus:underline">Reset Password</Link>.</p>
                    </motion.div>

                    <motion.div
                        className="mt-6 sm:mt-10 text-sm sm:text-base text-gray-500 flex items-center justify-center gap-2"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.8, delay: 0.3 }}
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-orange-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                        <p>Don't have an account? Please <Link href={`/signup/${userRole}`} className="text-orange-400 hover:text-orange-600 focus:outline-none focus:underline">Register here.</Link></p>
                    </motion.div>
                </motion.div>
            </div>
        </section>
    );
}
