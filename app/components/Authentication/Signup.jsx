'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { <PERSON>aGoogle, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON>ye, FaEyeSlash } from "react-icons/fa";
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import config from '@/config';

const PostSignup = async (formData) => {
    try {
        // Log the exact data being sent
        console.log('Sending registration data:', JSON.stringify(formData, null, 2));
        
        const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/auth/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                name: formData.name,
                email: formData.email,
                password: formData.password,
                password_confirmation: formData.password_confirmation,
                phone: formData.phone,
                address: formData.address,
                user_type: formData.user_type === 'partner' ? 'vendor' : 'buyer',
                company_name: formData.company_name,
                company_description: formData.company_description,
                business_registration_number: formData.business_registration_number,
                business_type: formData.business_type,
                tax_identification_number: formData.tax_identification_number,
                industry: formData.industry
            }),
        });

        const data = await response.json();
        console.log('Registration response:', data);

        if (!response.ok) {
            // Handle validation errors
            if (response.status === 422) {
                const validationErrors = data.errors || {};
                // Convert validation errors to a more user-friendly format
                const formattedErrors = {};
                Object.entries(validationErrors).forEach(([key, value]) => {
                    if (Array.isArray(value)) {
                        formattedErrors[key] = value[0];
                    } else {
                        formattedErrors[key] = value;
                    }
                });
                throw { validationErrors: formattedErrors };
            }
            // Handle other errors
            throw new Error(data.message || `Registration failed with status: ${response.status}`);
        }

        // Store the token in localStorage
        if (data.token) {
            localStorage.setItem('auth_token', data.token);
        }

        return {
            success: true,
            data: data
        };
    } catch (error) {
        console.error('Registration error details:', {
            message: error.message,
            validationErrors: error.validationErrors,
            stack: error.stack
        });
        throw error;
    }
};

// title: string,subHeading: string, description: string, partner: boolean, user: boolean
export default function Signup({ title, subHeading, description, partner, user, userType }) {
    const [data, setData] = useState({
        name: "",
        email: "",
        password: "",
        password_confirmation: "",
        phone: "",
        address: "",
        company_name: "",
        company_description: "",
        business_registration_number: "",
        business_type: "",
        tax_identification_number: "",
        industry: ""
    });
    const [logged, setLogged] = useState(false);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState({});
    const [shake, setShake] = useState(false);
    const user_typeProp = config.USER_TYPE;
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [showOptionalInfo, setShowOptionalInfo] = useState(false);

    const pathName = usePathname();
    const searchParams = useSearchParams();
    // console.log(pathName.split('/'));

    const route = useRouter();

    // Get redirect parameter from URL
    const redirectParam = searchParams.get('redirect');

    useEffect(() => {
        if (error) {
            setShake(true);
            const timer = setTimeout(() => setShake(false), 500);
            return () => clearTimeout(timer);
        }
    }, [error]);

    function handleChange(e) {
        const { name, value } = e.target;
        setData(prevData => ({
            ...prevData,
            [name]: value
        }));
    };

    // Simplified validation - only required fields: name, email, password, password_confirmation
    const validate = () => {
        let isValid = true;
        let errors = {};

        // Name validation
        if (!data.name?.trim()) {
            errors.name = "Name is required";
            isValid = false;
        } else if (data.name.trim().length < 2) {
            errors.name = "Name must be at least 2 characters";
            isValid = false;
        }

        // Email validation
        if (!data.email?.trim()) {
            errors.email = "Email is required";
            isValid = false;
        } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(data.email.trim())) {
            errors.email = "Invalid email address";
            isValid = false;
        }

        // Simplified password validation - only check length
        if (!data.password) {
            errors.password = "Password is required";
            isValid = false;
        } else if (data.password.length < 8) {
            errors.password = "Password must be at least 8 characters";
            isValid = false;
        }

        // Password confirmation validation
        if (!data.password_confirmation) {
            errors.password_confirmation = "Please confirm your password";
            isValid = false;
        } else if (data.password !== data.password_confirmation) {
            errors.password_confirmation = "Passwords do not match";
            isValid = false;
        }

        // Optional field validations - only validate format if provided
        if (data.phone?.trim() && !/^[\d\s-+()]{10,}$/.test(data.phone.trim())) {
            errors.phone = "Invalid phone number format";
            isValid = false;
        }

        setError(errors);
        return isValid;
    }

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError({});

        try {
            // Validate form before submission
            if (!validate()) {
                setLoading(false);
                return;
            }

            const formData = {
                name: data.name?.trim() || "",
                email: data.email?.trim() || "",
                password: data.password || "",
                password_confirmation: data.password_confirmation || "",
                phone: data.phone?.trim() || "",
                address: data.address?.trim() || "",
                user_type: userType || user_typeProp[pathName.split('/')[2]],
                company_name: data.company_name?.trim() || "",
                company_description: data.company_description?.trim() || "",
                business_registration_number: data.business_registration_number?.trim() || "",
                business_type: data.business_type?.trim() || "",
                tax_identification_number: data.tax_identification_number?.trim() || "",
                industry: data.industry?.trim() || ""
            };

            const response = await PostSignup(formData);
            
            if (response.success) {
                setLogged(true);
                // Get the user type from the URL path
                const userType = pathName.split('/')[2]; // 'user' or 'partner'
                setTimeout(() => {
                    // Pass redirect parameter to login page if it exists
                    const loginUrl = redirectParam
                        ? `/login/${userType}?redirect=${redirectParam}`
                        : `/login/${userType}`;
                    route.push(loginUrl);
                }, 2000);
            } else {
                setError({ general: response.message || 'Registration failed' });
            }
        } catch (error) {
            console.error('Form submission error:', error);
            if (error.validationErrors) {
                setError(error.validationErrors);
            } else {
                setError({ general: error.message || 'Registration failed. Please try again.' });
            }
        } finally {
            setLoading(false);
        }
    };

    // Cleanup effect for component unmount
    useEffect(() => {
        return () => {
            setLoading(false);
            setError({});
        };
    }, []);

    // task!! vendor resgistration (post) | review

    return (
        <section className="py-32 relative overflow-hidden min-h-svh">
            {/* Enhanced background with more vibrant gradients */}
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-100 via-indigo-100 to-orange-100"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_top_right,rgba(79,70,229,0.2),transparent_60%)]"></div>
            <div className="absolute -top-24 -left-24 w-96 h-96 bg-yellow-200 rounded-full filter blur-3xl opacity-30"></div>
            <div className="absolute bottom-0 right-0 w-80 h-80 bg-orange-200 rounded-full filter blur-3xl opacity-30"></div>

            {/* Decorative elements */}
            <motion.div
                className="absolute top-20 right-20 h-12 w-12 rounded-full bg-orange-500/20 hidden lg:block"
                animate={{ y: [0, 15, 0] }}
                transition={{ repeat: Infinity, duration: 3, ease: "easeInOut" }}
            />

            <motion.div
                className="absolute bottom-20 left-20 h-8 w-8 rounded-full bg-indigo-500/20 hidden lg:block"
                animate={{ y: [0, -15, 0] }}
                transition={{ repeat: Infinity, duration: 2.5, ease: "easeInOut" }}
            />

            <div className="container max-w-[500px] mx-auto px-6 md:px-12 relative z-10 bg-white/30 backdrop-blur-lg p-6 sm:p-8 rounded-2xl sm:shadow-2xl border border-white/20 sm:shadow-red-500/40">
                <motion.div
                    className="max-w-3xl mx-auto text-center"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.8, ease: "easeOut" }}
                >
                    <div className="mb-6 md:mb-8">
                        <motion.div
                            className="w-16 h-16 md:w-20 md:h-20 rounded-2xl bg-gradient-to-r from-yellow-300 to-orange-400 flex items-center justify-center mx-auto shadow-xl shadow-red-300/40"
                            whileHover={{ scale: 1.05, rotate: 5 }}
                            transition={{ type: "spring", stiffness: 400, damping: 10 }}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 md:h-10 md:w-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                        </motion.div>
                    </div>

                    <motion.h2
                        className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 md:mb-6 tracking-tight leading-tight"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.8, delay: 0.1, ease: "easeOut" }}>
                        <span className="text-indigo-600">
                            {title}
                        </span>
                    </motion.h2>

                    <motion.p
                        className="text-lg sm:text-xl text-indigo-500 mb-8 md:mb-12 leading-relaxed font-medium"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}>
                        {subHeading}
                    </motion.p>

                    <AnimatePresence mode="wait">
                        {!logged ? (
                            <motion.div
                                key="form"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                exit={{ opacity: 0 }}
                                transition={{ duration: 0.5 }}>
                                {/* Form */}
                                <motion.form
                                    onSubmit={handleSubmit}
                                    className="flex flex-col sm:flex-row gap-3 max-w-xl mx-auto"
                                    animate={{ x: shake ? [-10, 10, -7, 7, -3, 3, 0] : 0 }}
                                    transition={{ duration: 0.5 }}>
                                    <div className="relative flex-grow w-full">
                                        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                        </div>

                                        <div className='flex flex-col space-y-4'>
                                            {/* Required Fields */}
                                            <div className="input-group">
                                                <label htmlFor="name" className="sr-only">Full Name</label>
                                                <input
                                                    id="name"
                                                    type="text"
                                                    name="name"
                                                    value={data.name}
                                                    onChange={handleChange}
                                                    placeholder="Enter your full name *"
                                                    className="w-full px-4 sm:px-6 py-3 sm:py-4 rounded-xl bg-white/90 border-2 border-orange-200 focus:outline-none focus:ring-3 focus:ring-orange-500/50 focus:border-orange-500 shadow-lg shadow-red-100/30 transition-all duration-300 text-sm sm:text-base font-medium text-gray-700 placeholder:text-gray-400"
                                                    disabled={loading}
                                                    aria-required="true"
                                                    aria-invalid={error.name ? "true" : "false"}
                                                    aria-describedby={error.name ? "name-error" : undefined} />
                                                {error.name && <p id="name-error" className="text-xs sm:text-sm font-medium text-red-500 mt-1 ml-2" role="alert">{error.name}</p>}
                                            </div>

                                            <div className="input-group">
                                                <label htmlFor="email" className="sr-only">Email Address</label>
                                                <input
                                                    id="email"
                                                    type="email"
                                                    name="email"
                                                    value={data.email}
                                                    onChange={handleChange}
                                                    placeholder="Enter your email address *"
                                                    className="w-full px-4 sm:px-6 py-3 sm:py-4 rounded-xl bg-white/90 border-2 border-orange-200 focus:outline-none focus:ring-3 focus:ring-orange-500/50 focus:border-orange-500 shadow-lg shadow-red-100/30 transition-all duration-300 text-sm sm:text-base font-medium text-gray-700 placeholder:text-gray-400"
                                                    disabled={loading}
                                                    aria-required="true"
                                                    aria-invalid={error.email ? "true" : "false"}
                                                    aria-describedby={error.email ? "email-error" : undefined} />
                                                {error.email && <p id="email-error" className="text-xs sm:text-sm font-medium text-red-500 mt-1 ml-2" role="alert">{error.email}</p>}
                                            </div>

                                            <div className="input-group">
                                                <label htmlFor="password" className="sr-only">Password</label>
                                                <div className="relative">
                                                    <input
                                                        id="password"
                                                        type={showPassword ? "text" : "password"}
                                                        name="password"
                                                        value={data.password}
                                                        onChange={handleChange}
                                                        placeholder="Enter your password (min 8 characters) *"
                                                        className="w-full px-4 sm:px-6 py-3 sm:py-4 rounded-xl bg-white/90 border-2 border-orange-200 focus:outline-none focus:ring-3 focus:ring-orange-500/50 focus:border-orange-500 shadow-lg shadow-red-100/30 transition-all duration-300 text-sm sm:text-base font-medium text-gray-700 placeholder:text-gray-400"
                                                        disabled={loading}
                                                        aria-required="true"
                                                        minLength="8"
                                                        aria-invalid={error.password ? "true" : "false"}
                                                        aria-describedby={error.password ? "password-error" : undefined} />
                                                    <button
                                                        type="button"
                                                        onClick={() => setShowPassword(!showPassword)}
                                                        className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
                                                        tabIndex="-1">
                                                        {showPassword ? <FaEyeSlash className="h-5 w-5" /> : <FaEye className="h-5 w-5" />}
                                                    </button>
                                                </div>
                                                {error.password && <p id="password-error" className="text-xs sm:text-sm font-medium text-red-500 mt-1 ml-2" role="alert">{error.password}</p>}
                                            </div>

                                            <div className="input-group">
                                                <label htmlFor="password_confirmation" className="sr-only">Confirm Password</label>
                                                <div className="relative">
                                                    <input
                                                        id="password_confirmation"
                                                        type={showConfirmPassword ? "text" : "password"}
                                                        name="password_confirmation"
                                                        value={data.password_confirmation}
                                                        onChange={handleChange}
                                                        placeholder="Confirm your password *"
                                                        className="w-full px-4 sm:px-6 py-3 sm:py-4 rounded-xl bg-white/90 border-2 border-orange-200 focus:outline-none focus:ring-3 focus:ring-orange-500/50 focus:border-orange-500 shadow-lg shadow-red-100/30 transition-all duration-300 text-sm sm:text-base font-medium text-gray-700 placeholder:text-gray-400"
                                                        disabled={loading}
                                                        aria-required="true"
                                                        aria-invalid={error.password_confirmation ? "true" : "false"}
                                                        aria-describedby={error.password_confirmation ? "password-confirmation-error" : undefined} />
                                                    <button
                                                        type="button"
                                                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                                        className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
                                                        tabIndex="-1">
                                                        {showConfirmPassword ? <FaEyeSlash className="h-5 w-5" /> : <FaEye className="h-5 w-5" />}
                                                    </button>
                                                </div>
                                                {error.password_confirmation && <p id="password-confirmation-error" className="text-xs sm:text-sm font-medium text-red-500 mt-1 ml-2" role="alert">{error.password_confirmation}</p>}
                                            </div>

                                            {/* Optional Information Dropdown */}
                                            <div className="mt-6">
                                                <button
                                                    type="button"
                                                    onClick={() => setShowOptionalInfo(!showOptionalInfo)}
                                                    className="w-full flex items-center justify-between px-4 py-3 bg-orange-50 border-2 border-orange-200 rounded-xl hover:bg-orange-100 transition-all duration-300 text-orange-700 font-medium"
                                                    disabled={loading}
                                                >
                                                    <span>Optional Information</span>
                                                    <motion.div
                                                        animate={{ rotate: showOptionalInfo ? 180 : 0 }}
                                                        transition={{ duration: 0.3 }}
                                                    >
                                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                                        </svg>
                                                    </motion.div>
                                                </button>
                                            </div>

                                            {/* Collapsible Optional Fields */}
                                            <AnimatePresence>
                                                {showOptionalInfo && (
                                                    <motion.div
                                                        initial={{ opacity: 0, height: 0 }}
                                                        animate={{ opacity: 1, height: "auto" }}
                                                        exit={{ opacity: 0, height: 0 }}
                                                        transition={{ duration: 0.3 }}
                                                        className="space-y-4 overflow-hidden"
                                                    >
                                                        <div className="input-group">
                                                            <label htmlFor="phone" className="sr-only">Phone Number</label>
                                                            <input
                                                                id="phone"
                                                                type="tel"
                                                                name="phone"
                                                                value={data.phone}
                                                                onChange={handleChange}
                                                                placeholder="Enter your phone number (optional)"
                                                                className="w-full px-4 sm:px-6 py-3 sm:py-4 rounded-xl bg-white/90 border-2 border-orange-200 focus:outline-none focus:ring-3 focus:ring-orange-500/50 focus:border-orange-500 shadow-lg shadow-red-100/30 transition-all duration-300 text-sm sm:text-base font-medium text-gray-700 placeholder:text-gray-400"
                                                                disabled={loading}
                                                                aria-invalid={error.phone ? "true" : "false"}
                                                                aria-describedby={error.phone ? "phone-error" : undefined} />
                                                            {error.phone && <p id="phone-error" className="text-xs sm:text-sm font-medium text-red-500 mt-1 ml-2" role="alert">{error.phone}</p>}
                                                        </div>

                                                        <div className="input-group">
                                                            <label htmlFor="address" className="sr-only">Address</label>
                                                            <input
                                                                id="address"
                                                                type="text"
                                                                name="address"
                                                                value={data.address}
                                                                onChange={handleChange}
                                                                placeholder="Enter your address (optional)"
                                                                className="w-full px-4 sm:px-6 py-3 sm:py-4 rounded-xl bg-white/90 border-2 border-orange-200 focus:outline-none focus:ring-3 focus:ring-orange-500/50 focus:border-orange-500 shadow-lg shadow-red-100/30 transition-all duration-300 text-sm sm:text-base font-medium text-gray-700 placeholder:text-gray-400"
                                                                disabled={loading}
                                                                aria-invalid={error.address ? "true" : "false"}
                                                                aria-describedby={error.address ? "address-error" : undefined} />
                                                            {error.address && <p id="address-error" className="text-xs sm:text-sm font-medium text-red-500 mt-1 ml-2" role="alert">{error.address}</p>}
                                                        </div>

                                                        <div className="input-group">
                                                            <label htmlFor="company_name" className="sr-only">Company Name</label>
                                                            <input
                                                                id="company_name"
                                                                type="text"
                                                                name="company_name"
                                                                value={data.company_name}
                                                                onChange={handleChange}
                                                                placeholder="Enter company name (optional)"
                                                                className="w-full px-4 sm:px-6 py-3 sm:py-4 rounded-xl bg-white/90 border-2 border-orange-200 focus:outline-none focus:ring-3 focus:ring-orange-500/50 focus:border-orange-500 shadow-lg shadow-red-100/30 transition-all duration-300 text-sm sm:text-base font-medium text-gray-700 placeholder:text-gray-400"
                                                                disabled={loading}
                                                                aria-invalid={error.company_name ? "true" : "false"}
                                                                aria-describedby={error.company_name ? "company-name-error" : undefined} />
                                                            {error.company_name && <p id="company-name-error" className="text-xs sm:text-sm font-medium text-red-500 mt-1 ml-2" role="alert">{error.company_name}</p>}
                                                        </div>

                                                        <div className="input-group">
                                                            <label htmlFor="company_description" className="sr-only">Company Description</label>
                                                            <textarea
                                                                id="company_description"
                                                                name="company_description"
                                                                value={data.company_description}
                                                                onChange={handleChange}
                                                                placeholder="Enter company description (optional)"
                                                                className="w-full px-4 sm:px-6 py-3 sm:py-4 rounded-xl bg-white/90 border-2 border-orange-200 focus:outline-none focus:ring-3 focus:ring-orange-500/50 focus:border-orange-500 shadow-lg shadow-red-100/30 transition-all duration-300 text-sm sm:text-base font-medium text-gray-700 placeholder:text-gray-400"
                                                                disabled={loading}
                                                                rows="3"
                                                                aria-invalid={error.company_description ? "true" : "false"}
                                                                aria-describedby={error.company_description ? "company-description-error" : undefined} />
                                                            {error.company_description && <p id="company-description-error" className="text-xs sm:text-sm font-medium text-red-500 mt-1 ml-2" role="alert">{error.company_description}</p>}
                                                        </div>

                                                        <div className="input-group">
                                                            <label htmlFor="business_registration_number" className="sr-only">Business Registration Number</label>
                                                            <input
                                                                id="business_registration_number"
                                                                type="text"
                                                                name="business_registration_number"
                                                                value={data.business_registration_number}
                                                                onChange={handleChange}
                                                                placeholder="Enter business registration number (optional)"
                                                                className="w-full px-4 sm:px-6 py-3 sm:py-4 rounded-xl bg-white/90 border-2 border-orange-200 focus:outline-none focus:ring-3 focus:ring-orange-500/50 focus:border-orange-500 shadow-lg shadow-red-100/30 transition-all duration-300 text-sm sm:text-base font-medium text-gray-700 placeholder:text-gray-400"
                                                                disabled={loading}
                                                                aria-invalid={error.business_registration_number ? "true" : "false"}
                                                                aria-describedby={error.business_registration_number ? "business-registration-error" : undefined} />
                                                            {error.business_registration_number && <p id="business-registration-error" className="text-xs sm:text-sm font-medium text-red-500 mt-1 ml-2" role="alert">{error.business_registration_number}</p>}
                                                        </div>

                                                        <div className="input-group">
                                                            <label htmlFor="business_type" className="sr-only">Business Type</label>
                                                            <input
                                                                id="business_type"
                                                                type="text"
                                                                name="business_type"
                                                                value={data.business_type}
                                                                onChange={handleChange}
                                                                placeholder="Enter your business type (optional)"
                                                                className="w-full px-4 sm:px-6 py-3 sm:py-4 rounded-xl bg-white/90 border-2 border-orange-200 focus:outline-none focus:ring-3 focus:ring-orange-500/50 focus:border-orange-500 shadow-lg shadow-red-100/30 transition-all duration-300 text-sm sm:text-base font-medium text-gray-700 placeholder:text-gray-400"
                                                                disabled={loading}
                                                                aria-invalid={error.business_type ? "true" : "false"}
                                                                aria-describedby={error.business_type ? "business-type-error" : undefined} />
                                                            {error.business_type && <p id="business-type-error" className="text-xs sm:text-sm font-medium text-red-500 mt-1 ml-2" role="alert">{error.business_type}</p>}
                                                        </div>

                                                        <div className="input-group">
                                                            <label htmlFor="tax_identification_number" className="sr-only">Tax Identification Number</label>
                                                            <input
                                                                id="tax_identification_number"
                                                                type="text"
                                                                name="tax_identification_number"
                                                                value={data.tax_identification_number}
                                                                onChange={handleChange}
                                                                placeholder="Enter your tax identification number (optional)"
                                                                className="w-full px-4 sm:px-6 py-3 sm:py-4 rounded-xl bg-white/90 border-2 border-orange-200 focus:outline-none focus:ring-3 focus:ring-orange-500/50 focus:border-orange-500 shadow-lg shadow-red-100/30 transition-all duration-300 text-sm sm:text-base font-medium text-gray-700 placeholder:text-gray-400"
                                                                disabled={loading}
                                                                aria-invalid={error.tax_identification_number ? "true" : "false"}
                                                                aria-describedby={error.tax_identification_number ? "tax-id-error" : undefined} />
                                                            {error.tax_identification_number && <p id="tax-id-error" className="text-xs sm:text-sm font-medium text-red-500 mt-1 ml-2" role="alert">{error.tax_identification_number}</p>}
                                                        </div>

                                                        <div className="input-group">
                                                            <label htmlFor="industry" className="sr-only">Industry</label>
                                                            <input
                                                                id="industry"
                                                                type="text"
                                                                name="industry"
                                                                value={data.industry}
                                                                onChange={handleChange}
                                                                placeholder="Enter your industry (optional)"
                                                                className="w-full px-4 sm:px-6 py-3 sm:py-4 rounded-xl bg-white/90 border-2 border-orange-200 focus:outline-none focus:ring-3 focus:ring-orange-500/50 focus:border-orange-500 shadow-lg shadow-red-100/30 transition-all duration-300 text-sm sm:text-base font-medium text-gray-700 placeholder:text-gray-400"
                                                                disabled={loading}
                                                                aria-invalid={error.industry ? "true" : "false"}
                                                                aria-describedby={error.industry ? "industry-error" : undefined} />
                                                            {error.industry && <p id="industry-error" className="text-xs sm:text-sm font-medium text-red-500 mt-1 ml-2" role="alert">{error.industry}</p>}
                                                        </div>
                                                    </motion.div>
                                                )}
                                            </AnimatePresence>

                                            {loading ? (
                                                <div
                                                    className="flex items-center justify-center bg-gradient-to-r from-yellow-300 to-orange-500 text-white font-bold text-base sm:text-lg rounded-xl shadow-xl shadow-red-500/20 hover:shadow-red-500/40 transition duration-300 whitespace-nowrap px-6 py-3 sm:px-8 sm:py-4 animate-pulse"
                                                    role="status"
                                                    aria-live="polite">
                                                    <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" aria-hidden="true">
                                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                    </svg>
                                                    <span>Loading...</span>
                                                </div>
                                            ) : <motion.button
                                                type="submit"
                                                disabled={loading}
                                                whileHover={{ scale: loading ? 1 : 1.03 }}
                                                whileTap={{ scale: loading ? 1 : 0.98 }}
                                                className={`px-6 py-3 sm:px-8 sm:py-4 bg-gradient-to-r from-yellow-300 to-orange-500 text-white font-bold text-base sm:text-lg rounded-xl shadow-xl shadow-red-500/20 hover:shadow-red-500/40 transition duration-300 whitespace-nowrap ${loading ? 'opacity-80 cursor-not-allowed' : ''}`}
                                                aria-label="Submit registration form">
                                                Submit
                                            </motion.button>}
                                        </div>
                                    </div>
                                </motion.form>
                            </motion.div>
                        ) : (
                            <motion.div
                                key="success"
                                initial={{ opacity: 0, scale: 0.9 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.5 }}
                                className="bg-gradient-to-r from-green-50 to-emerald-50 border-l-4 border-green-500 p-6 sm:p-8 rounded-xl shadow-2xl"
                            >
                                <div className="flex flex-col sm:flex-row sm:items-center">
                                    <div className="bg-green-100 rounded-full p-3 mx-auto sm:mx-0 sm:mr-5 mb-4 sm:mb-0">
                                        <svg className="h-8 w-8 sm:h-10 sm:w-10 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                        </svg>
                                    </div>
                                    <div className="text-center sm:text-left">
                                        <h3 className="font-bold text-xl sm:text-2xl text-gray-900 mb-2">Successfully Registered.</h3>
                                        <p className="text-gray-700 text-base sm:text-lg">Check your email for confirmation and exclusive offers. Try to login now.</p>
                                    </div>
                                </div>
                            </motion.div>
                        )}
                    </AnimatePresence>

                    <motion.div
                        className="my-2 text-xs sm:text-sm text-gray-500 flex items-center justify-center gap-2"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.8, delay: 0.3 }}>
                        <p>Already have an account? Please <Link href={`/login/${pathName.split("/")[2]}`} className="text-orange-400">Login</Link> here.</p>
                    </motion.div>

                    <motion.div
                        className="mt-8 sm:mt-10 text-xs sm:text-sm text-gray-500 flex items-center justify-center gap-2"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.8, delay: 0.3 }}
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 text-orange-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                        <p>We respect your privacy. Unsubscribe at any time.</p>
                    </motion.div>
                </motion.div>
            </div>
        </section>
    );
}
