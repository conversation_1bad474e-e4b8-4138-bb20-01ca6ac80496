import { 
  CurrencyDollarIcon, ShoppingCartIcon, 
  UsersIcon, ChartBarIcon 
} from '@heroicons/react/24/outline';

const stats = [
  {
    id: 1,
    name: 'Total Sales',
    value: '$34,580',
    change: '+12.5%',
    positive: true,
    icon: CurrencyDollarIcon,
    bgColor: 'bg-indigo-500',
  },
  {
    id: 2,
    name: 'Orders',
    value: '450',
    change: '+5.7%',
    positive: true,
    icon: ShoppingCartIcon,
    bgColor: 'bg-purple-500',
  },
  {
    id: 3,
    name: 'Revenue',
    value: '$21,450',
    change: '+18.3%',
    positive: true,
    icon: ChartBarIcon,
    bgColor: 'bg-blue-500',
  },
  {
    id: 4,
    name: 'Customers',
    value: '2,340',
    change: '-3.2%',
    positive: false,
    icon: UsersIcon,
    bgColor: 'bg-pink-500',
  },
];

export default function StatsCards() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat) => (
        <div 
          key={stat.id} 
          className="bg-white rounded-lg shadow-sm p-5 transition-transform duration-300 transform hover:-translate-y-1 hover:shadow-md"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">{stat.name}</p>
              <p className="mt-1 text-2xl font-semibold text-gray-800">{stat.value}</p>
            </div>
            <div className={`p-3 rounded-full ${stat.bgColor} bg-opacity-10`}>
              <stat.icon className={`h-6 w-6 ${stat.bgColor.replace('bg', 'text')}`} />
            </div>
          </div>
          <div className="mt-3">
            <span className={`text-xs font-medium ${stat.positive ? 'text-green-600' : 'text-red-600'}`}>
              {stat.change}
            </span>
            <span className="text-xs font-medium text-gray-500 ml-1">from last month</span>
          </div>
        </div>
      ))}
    </div>
  );
}
