"use client";

import React, { useEffect, useState } from 'react'
import { motion } from "framer-motion";
import { FiEdit, FiTrash2, FiPackage, FiGlobe, FiStar, FiCheck, FiX } from 'react-icons/fi';
import { BsCalendarDate, BsPersonCircle } from 'react-icons/bs';
import Link from 'next/link';
import Swal from 'sweetalert2';

export const getAllBrands = async function () {
    const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/admin/brands`, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${localStorage.getItem("superAdminAuthToken")}`
        },
    });

    const data = await response.json();
    return data;
};

async function deleteBrand(id) {
    const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/admin/brands/${id}`, {
        method: "DELETE",
        headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${localStorage.getItem("superAdminAuthToken")}`
        },
    });

    const data = await response.json();
    return data;
}

// task! get all brands (get) || reviwe

export default function Brands() {
    const [brands, setBrands] = useState([]);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState(null);

    useEffect(() => {
        const fetchBrands = async () => {
            try {
                const data = await getAllBrands();
                if (data.success) {
                    setBrands(data.data.brands.data);
                    setPagination({
                        currentPage: data.data.brands.current_page,
                        lastPage: data.data.brands.last_page,
                        total: data.data.brands.total,
                        links: data.data.brands.links
                    });
                }
            } catch (error) {
                setError("Failed to connect to the server");
                console.error("Error fetching brands:", error);
            }
        };

        fetchBrands();
    }, []);

    const handlePageChange = async (url) => {
        if (!url) return;
        try {
            const response = await fetch(url, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem("superAdminAuthToken")}`
                }
            });
            const data = await response.json();
            if (data.success) {
                setBrands(data.data.brands.data);
                setPagination({
                    currentPage: data.data.brands.current_page,
                    lastPage: data.data.brands.last_page,
                    total: data.data.brands.total,
                    links: data.data.brands.links
                });
            }
        } catch (error) {
            setError("Failed to fetch page");
        }
    };

    // task! delete a brand (delete) || review

    const handleDelete = async (id) => {
        // make sure to use sweet alert 2
        const result = await Swal.fire({
            title: "Are you sure?",
            text: "You won't be able to revert this!",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes, delete it!",
        });

        if (!result.isConfirmed) {
            return;
        }

        const response = await deleteBrand(id);
        console.log(response);

        if (response.success === true) {
            // Remove the deleted brand from the brands state
            setBrands(brands.filter((brand) => brand.id !== id));
            setPagination({
                currentPage: pagination.currentPage,
                lastPage: pagination.lastPage,
                total: pagination.total - 1,
                links: pagination.links
            });

            Swal.fire({
                title: response.message,
                icon: "success",
                timer: 1500,
                showConfirmButton: false,
            });
        } else {
            setError(response);
            Swal.fire({
                title: response.message,
                text: response.details,
                icon: "error",
                timer: 1500,
                showConfirmButton: false,
            });
        }
    }

    return (
        <div className="p-4 text-gray-700">
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white p-6 rounded-2xl shadow-md"
            >
                {error && (
                    <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg mb-4 text-base">
                        {error}
                    </div>
                )}

                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-2xl font-semibold text-orange-400">Brands</h2>
                    <Link href={`/super-admin/manage-brands-categories/brands/add`}>
                        <button className='flex items-center gap-2 text-white bg-gradient-to-r from-orange-400 to-orange-500 hover:from-orange-500 hover:to-orange-600 px-4 py-2 rounded-xl shadow-md transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1'>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                            </svg>
                            <span className="font-medium">Add New Brand</span>
                        </button>
                    </Link>
                </div>
                {pagination && (
                    <span className="text-base text-gray-600">
                        Showing {brands.length} of {pagination.total} brands
                    </span>
                )}

                <div className="space-y-3 mb-4 overflow-y-auto">
                    {brands.map((brand) => (
                        <motion.div
                            key={brand.id}
                            whileHover={{ scale: 1.02 }}
                            className="flex flex-col md:flex-row justify-between items-start md:items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 m-5"
                        >
                            <div className="flex-1 w-full">
                                <div className="flex flex-wrap items-center gap-2 mb-2">
                                    <h3 className="font-medium text-xl">{brand.name}</h3>
                                    <span className="text-base text-gray-500">({brand.slug})</span>
                                    <div className="flex flex-wrap gap-2">
                                        {brand.is_featured && (
                                            <span className="px-2 py-1 bg-orange-100 text-orange-600 text-sm rounded-full flex items-center gap-1">
                                                <FiStar className="w-4 h-4" />
                                                Featured
                                            </span>
                                        )}
                                        {!brand.is_active && (
                                            <span className="px-2 py-1 bg-red-100 text-red-600 text-sm rounded-full flex items-center gap-1">
                                                <FiX className="w-4 h-4" />
                                                Inactive
                                            </span>
                                        )}
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 gap-2 text-base">
                                    <div>
                                        {brand.description && (
                                            <p className="text-gray-600 mb-2 break-words">{brand.description}</p>
                                        )}
                                        <div className="flex flex-wrap items-center gap-2 text-gray-500">
                                            <div className="flex items-center gap-1">
                                                <FiPackage className="w-5 h-5 text-purple-600" />
                                                <span>Products: {brand.product_count}</span>
                                            </div>
                                            {brand.website && (
                                                <a
                                                    href={brand.website}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-orange-500 hover:text-orange-600 underline flex items-center gap-1"
                                                >
                                                    <FiGlobe className="w-5 h-5" />
                                                    Visit Website
                                                </a>
                                            )}
                                        </div>
                                    </div>

                                    <div className="text-gray-500 mt-2">
                                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-1">
                                            <p className="flex items-center gap-1">
                                                <BsPersonCircle className="w-5 h-5 text-orange-400" />
                                                <span className="truncate">Created by: {brand.creator.name}</span>
                                            </p>
                                            <p className="flex items-center gap-1">
                                                <BsCalendarDate className="w-5 h-5 text-blue-600" />
                                                Created: {new Date(brand.created_at).toLocaleDateString()}
                                            </p>
                                            <p className="flex items-center gap-1 sm:col-span-2">
                                                <BsCalendarDate className="w-5 h-5 text-blue-600" />
                                                Updated: {new Date(brand.updated_at).toLocaleDateString()}
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                {brand.logo ? (
                                    <div className="mt-2">
                                        <img
                                            src={brand.logo}
                                            alt={`${brand.name} logo`}
                                            className="h-12 w-auto object-contain"
                                        />
                                    </div>
                                ) : (
                                    <div className="mt-2 h-12 w-12 bg-orange-400 rounded-lg">
                                    </div>
                                )}
                            </div>

                            <div className="flex flex-row md:flex-col gap-2 mt-3 md:mt-0 w-full md:w-auto justify-end">
                                <Link href={`/super-admin/manage-brands-categories/brands/${brand.id}/update`} className="inline-block">
                                    <button
                                        className="text-blue-500 hover:text-blue-700 p-3 bg-blue-100 rounded-full"
                                    >
                                        <FiEdit className="w-5 h-5" />
                                    </button>
                                </Link>

                                <button
                                    onClick={() => handleDelete(brand.id)}
                                    className="text-red-500 hover:text-red-700 p-3 bg-red-100 rounded-full"
                                >
                                    <FiTrash2 className="w-5 h-5" />
                                </button>
                            </div>
                        </motion.div>
                    ))}
                </div>

                {pagination && pagination.lastPage > 1 && (
                    <div className="flex justify-center gap-2 mt-6">
                        {pagination.links.map((link, index) => (
                            <button
                                key={index}
                                onClick={() => handlePageChange(link.url)}
                                disabled={!link.url || link.active}
                                className={`px-4 py-2 rounded text-base ${link.active
                                    ? 'bg-orange-400 text-white'
                                    : link.url
                                        ? 'bg-gray-100 hover:bg-gray-200'
                                        : 'bg-gray-50 text-gray-400 cursor-not-allowed'
                                    }`}
                                dangerouslySetInnerHTML={{ __html: link.label }}
                            />
                        ))}
                    </div>
                )}
            </motion.div>
        </div>
    );
}
