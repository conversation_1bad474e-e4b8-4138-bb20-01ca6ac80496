"use client";

import React from 'react'

import { useState } from "react";
import { motion } from "framer-motion";

// Dummy data
const dummyCategories = [
    { id: 1, name: "Electronics" },
    { id: 2, name: "Clothing" },
    { id: 3, name: "Books" },
    { id: 4, name: "Sports" },
    { id: 5, name: "<PERSON>s" },
    { id: 6, name: "Health" },
    { id: 7, name: "Beauty" },
    { id: 8, name: "Home" },
    { id: 9, name: "Grocery" },
    { id: 10, name: "Furniture" },
    { id: 11, name: "Jewelry" },
];

export default function ManageCategories() {
    const [categories, setCategories] = useState([...dummyCategories]);

    const [newCategory, setNewCategory] = useState("");

    // Simulated API call for category
    const handleAddCategory = () => {
        const name = newCategory.trim();
        if (!name) return;
        const exists = categories.some(
            (cat) => cat.name.toLowerCase() === name.toLowerCase()
        );
        if (exists) return;

        const newItem = { id: Date.now(), name };
        setCategories([...categories, newItem]);
        console.log("📦 Category API call:", newItem); // Simulate API
        setNewCategory("");
    };

    const handleDeleteCategory = (id) => {
        setCategories(categories.filter((cat) => cat.id !== id));
    };

    const filteredCategorySuggestions = categories.filter(
        (cat) =>
            newCategory &&
            cat.name.toLowerCase().includes(newCategory.toLowerCase())
    );
    return (
        <div>
            {/* Categories Section */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white p-6 rounded-2xl shadow-md"
            >
                <h2 className="text-xl font-semibold mb-4">Categories</h2>
                <div className="space-y-3 mb-4 max-h-dvh overflow-y-auto">
                    {categories.map((cat) => (
                        <motion.div
                            key={cat.id}
                            whileHover={{ scale: 1.02 }}
                            className="flex justify-between items-center p-2 bg-gray-100 rounded m-2"
                        >
                            <span>{cat.name}</span>
                            <button
                                onClick={() => handleDeleteCategory(cat.id)}
                                className="text-red-500 hover:text-red-700 text-lg"
                            >
                                ✕
                            </button>
                        </motion.div>
                    ))}
                </div>
                <div className="relative">
                    <div className="flex">
                        <input
                            value={newCategory}
                            onChange={(e) => setNewCategory(e.target.value)}
                            placeholder="Add category"
                            className="w-full px-4 py-2 border border-orange-400 rounded-md"
                        />
                        <button
                            onClick={handleAddCategory}
                            className="bg-orange-400 hover:bg-orange-500 text-white px-4 py-2 rounded-md min-w-20 ml-2"
                        >
                            Add Category
                        </button>
                    </div>
                    {filteredCategorySuggestions.length > 0 && (
                        <div className="absolute bg-white border w-full rounded shadow z-10">
                            {filteredCategorySuggestions.map((cat) => (
                                <div
                                    key={cat.id}
                                    onClick={() => setNewCategory(cat.name)}
                                    className="px-3 py-2 hover:bg-orange-100 cursor-pointer"
                                >
                                    {cat.name}
                                </div>
                            ))}
                        </div>
                    )}
                </div>

            </motion.div>
        </div>
    )
}
