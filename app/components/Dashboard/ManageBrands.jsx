"use client";

import React, { useState, useEffect } from 'react'
import { motion } from "framer-motion";
import { FiEdit, FiTrash2, FiPlus, FiPackage, FiGlobe, FiStar, FiCheck, FiX } from 'react-icons/fi';
import { MdFeaturedPlayList } from 'react-icons/md';
import { BsCalendarDate, BsPersonCircle } from 'react-icons/bs';

export const getAllBrands = async function () {
    const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/admin/brands`, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${localStorage.getItem("superAdminAuthToken")}`
        },
    });

    const data = await response.json();
    return data;
};

export default function ManageBrands() {
    const [brands, setBrands] = useState([]);
    const [newBrand, setNewBrand] = useState("");
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState(null);

    useEffect(() => {
        const fetchBrands = async () => {
            try {
                const data = await getAllBrands();
                if (data.success) {
                    setBrands(data.data.brands.data);
                    setPagination({
                        currentPage: data.data.brands.current_page,
                        lastPage: data.data.brands.last_page,
                        total: data.data.brands.total,
                        links: data.data.brands.links
                    });
                }
            } catch (error) {
                setError("Failed to connect to the server");
                console.error("Error fetching brands:", error);
            }
        };

        fetchBrands();
    }, []);

    // Simulated API call for brand
    const handleAddBrand = () => {
        const name = newBrand.trim();
        if (!name) return;
        const exists = brands.some(
            (brand) => brand.name.toLowerCase() === name.toLowerCase()
        );
        if (exists) return;

        const newItem = { id: Date.now(), name };
        setBrands([...brands, newItem]);
        console.log("🏷️ Brand API call:", newItem); // Simulate API
        setNewBrand("");
    };

    const handleDeleteBrand = (id) => {
        // delete api call

        // delete from brands array
        setBrands(brands.filter((brand) => brand.id !== id));

        // show success message
    };

    const filteredBrandSuggestions = brands.filter(
        (brand) =>
            newBrand && brand.name.toLowerCase().includes(newBrand.toLowerCase())
    );

    return (
        <div className='text-gray-600'>
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white p-6 rounded-2xl shadow-md"
            >
                {error && (
                    <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg mb-4 flex items-center gap-2">
                        <FiX className="w-5 h-5" />
                        {error}
                    </div>
                )}

                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold flex items-center gap-2">
                        <MdFeaturedPlayList className="w-6 h-6 text-orange-500" />
                        Brands
                    </h2>
                    {pagination && (
                        <span className="text-sm text-gray-600 flex items-center gap-1">
                            <FiPackage className="w-4 h-4" />
                            Showing {brands.length} of {pagination.total} brands
                        </span>
                    )}
                </div>

                <div className="space-y-3 mb-4 max-h-[60vh] overflow-y-auto">
                    {brands.map((brand) => (
                        <motion.div
                            key={brand.id}
                            whileHover={{ scale: 1.02 }}
                            className="flex justify-between items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 mx-3"
                        >
                            <div className="flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                    <h3 className="font-medium text-lg">{brand.name}</h3>
                                    <span className="text-sm text-gray-500">({brand.slug})</span>
                                    {brand.is_featured && (
                                        <span className="px-2 py-1 bg-orange-100 text-orange-600 text-xs rounded-full flex items-center gap-1">
                                            <FiStar className="w-3 h-3" />
                                            Featured
                                        </span>
                                    )}
                                    {!brand.is_active && (
                                        <span className="px-2 py-1 bg-red-100 text-red-600 text-xs rounded-full flex items-center gap-1">
                                            <FiX className="w-3 h-3" />
                                            Inactive
                                        </span>
                                    )}
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                                    <div>
                                        {brand.description && (
                                            <p className="text-gray-600 mb-2">{brand.description}</p>
                                        )}
                                        <div className="flex items-center gap-2 text-gray-500">
                                            <FiPackage className="w-4 h-4" />
                                            <span>Products: {brand.product_count}</span>
                                            {brand.website && (
                                                <a
                                                    href={brand.website}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-orange-500 hover:text-orange-600 underline flex items-center gap-1"
                                                >
                                                    <FiGlobe className="w-4 h-4" />
                                                    Visit Website
                                                </a>
                                            )}
                                        </div>
                                    </div>

                                    <div className="text-gray-500">
                                        <p className="flex items-center gap-1">
                                            <BsPersonCircle className="w-4 h-4" />
                                            Created by: {brand.creator.name}
                                        </p>
                                        <p className="flex items-center gap-1">
                                            <BsCalendarDate className="w-4 h-4" />
                                            Created: {new Date(brand.created_at).toLocaleDateString()}
                                        </p>
                                        <p className="flex items-center gap-1">
                                            <BsCalendarDate className="w-4 h-4" />
                                            Updated: {new Date(brand.updated_at).toLocaleDateString()}
                                        </p>
                                    </div>
                                </div>

                                {brand.logo && (
                                    <div className="mt-2">
                                        <img
                                            src={brand.logo}
                                            alt={`${brand.name} logo`}
                                            className="h-10 w-auto object-contain"
                                        />
                                    </div>
                                )}
                            </div>

                            <div className="flex flex-col gap-2">
                                <button
                                    onClick={() => handleDeleteBrand(brand.id)}
                                    className="text-red-500 hover:text-red-700 p-2"
                                >
                                    <FiTrash2 className="w-5 h-5" />
                                </button>
                            </div>
                        </motion.div>
                    ))}
                </div>

                <div className="relative">
                    <div className="flex">
                        <form onSubmit={handleAddBrand} className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <label className="block text-sm font-medium">
                                    Brand Name <span className="text-red-500">*</span>
                                </label>
                                <input
                                    type="text"
                                    value={newBrand}
                                    onChange={(e) => setNewBrand(e.target.value)}
                                    placeholder="Enter brand name"
                                    className="w-full px-3 py-2 border border-orange-400 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-300"
                                    required
                                />
                            </div>

                            <div className="space-y-2">
                                <label className="block text-sm font-medium">
                                    Description
                                </label>
                                <textarea
                                    placeholder="Enter brand description"
                                    className="w-full px-3 py-2 border border-orange-400 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-300"
                                    rows="2"
                                />
                            </div>

                            <div className="space-y-2">
                                <label className="block text-sm font-medium">
                                    Website URL
                                </label>
                                <input
                                    type="url"
                                    placeholder="https://example.com"
                                    className="w-full px-3 py-2 border border-orange-400 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-300"
                                />
                            </div>

                            <div className="flex items-center gap-4">
                                <div className="flex items-center gap-2">
                                    <input
                                        type="checkbox"
                                        id="is_featured"
                                        className="w-4 h-4 text-orange-500 border-orange-400 rounded focus:ring-orange-300"
                                    />
                                    <label htmlFor="is_featured" className="text-sm font-medium">
                                        Featured Brand
                                    </label>
                                </div>

                                <div className="flex items-center gap-2">
                                    <input
                                        type="checkbox"
                                        id="is_active"
                                        className="w-4 h-4 text-orange-500 border-orange-400 rounded focus:ring-orange-300"
                                        defaultChecked
                                    />
                                    <label htmlFor="is_active" className="text-sm font-medium">
                                        Active
                                    </label>
                                </div>
                            </div>

                            <div className="md:col-span-2">
                                <button
                                    type="submit"
                                    className="bg-orange-400 hover:bg-orange-500 text-white px-4 py-2 rounded-md min-w-20 flex items-center gap-2 justify-center"
                                >
                                    <FiPlus className="w-5 h-5" />
                                    Add Brand
                                </button>
                            </div>
                        </form>
                    </div>
                    {filteredBrandSuggestions.length > 0 && (
                        <div className="absolute bg-white border w-full rounded shadow z-10">
                            {filteredBrandSuggestions.map((brand) => (
                                <div
                                    key={brand.id}
                                    onClick={() => setNewBrand(brand.name)}
                                    className="px-3 py-2 hover:bg-orange-100 cursor-pointer"
                                >
                                    {brand.name}
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                {pagination && pagination.lastPage > 1 && (
                    <div className="flex justify-center gap-2 mt-6">
                        {pagination.links.map((link, index) => (
                            <button
                                key={index}
                                onClick={() => handlePageChange(link.url)}
                                disabled={!link.url || link.active}
                                className={`px-3 py-1 rounded ${link.active
                                    ? 'bg-orange-400 text-white'
                                    : link.url
                                        ? 'bg-gray-100 hover:bg-gray-200'
                                        : 'bg-gray-50 text-gray-400 cursor-not-allowed'
                                    }`}
                                dangerouslySetInnerHTML={{ __html: link.label }}
                            />
                        ))}
                    </div>
                )}
            </motion.div>
        </div>
    );
}
