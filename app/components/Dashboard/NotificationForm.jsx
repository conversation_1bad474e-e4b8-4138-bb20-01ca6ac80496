"use client";

import { useState } from 'react';
import { motion } from 'framer-motion';
import { usePathname } from 'next/navigation';

const samplePartners = [
    { name: '<PERSON>', email: '<EMAIL>' },
    { name: '<PERSON>', email: '<EMAIL>' },
    { name: 'Acme Corp', email: '<EMAIL>' },
    { name: 'Global Inc', email: '<EMAIL>' },
];

export default function NotificationForm() {
    const [subject, setSubject] = useState('');
    const [message, setMessage] = useState('');
    const [sendTo, setSendTo] = useState('all');
    const [partnerInput, setPartnerInput] = useState('');
    const [partnerEmails, setPartnerEmails] = useState([]);
    const pathname = usePathname();


    const filteredPartners = samplePartners.filter(
        (p) =>
            p.name.toLowerCase().includes(partnerInput.toLowerCase()) ||
            p.email.toLowerCase().includes(partnerInput.toLowerCase())
    );

    const handleAddPartner = (email) => {
        if (!partnerEmails.includes(email)) {
            setPartnerEmails([...partnerEmails, email]);
        }
        setPartnerInput('');
    };

    const handleRemoveEmail = (email) => {
        setPartnerEmails(partnerEmails.filter((e) => e !== email));
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        console.log({ subject, message, sendTo, emails: partnerEmails });
    };

    return (
        pathname.includes('super-admin') && (
            <motion.div
                className="max-w-xl mx-auto p-6 bg-white rounded-xl shadow-md text-gray-700"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
            >
                <h2 className="text-xl font-bold mb-4">Send Notification</h2>
                <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium mb-1">Subject</label>
                        <input
                            type="text"
                            className="w-full border border-gray-300 rounded p-2"
                            value={subject}
                            onChange={(e) => setSubject(e.target.value)}
                            required
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium mb-1">Message</label>
                        <textarea
                            className="w-full border border-gray-300 rounded p-2"
                            rows={4}
                            value={message}
                            onChange={(e) => setMessage(e.target.value)}
                            required
                        ></textarea>
                    </div>
                    <div>
                        <label className="block text-sm font-medium mb-1">Send To</label>
                        <select
                            className="w-full border border-gray-300 rounded p-2"
                            value={sendTo}
                            onChange={(e) => setSendTo(e.target.value)}
                        >
                            <option value="all">All Partners</option>
                            <option value="specific">Specific Partners</option>
                        </select>
                    </div>
                    {sendTo === 'specific' && (
                        <div>
                            <label className="block text-sm font-medium mb-1">Partner Emails</label>
                            <input
                                type="text"
                                className="w-full border border-gray-300 rounded p-2"
                                value={partnerInput}
                                onChange={(e) => setPartnerInput(e.target.value)}
                                placeholder="Type partner name or email"
                            />
                            {partnerInput && (
                                <ul className="mt-1 border border-gray-200 rounded bg-white">
                                    {filteredPartners.map((p) => (
                                        <li
                                            key={p.email}
                                            onClick={() => handleAddPartner(p.email)}
                                            className="cursor-pointer px-4 py-2 hover:bg-gray-100"
                                        >
                                            {p.name} ({p.email})
                                        </li>
                                    ))}
                                </ul>
                            )}
                            <div className="mt-2 flex flex-wrap gap-2">
                                {partnerEmails.map((email) => (
                                    <span
                                        key={email}
                                        className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm flex items-center gap-1"
                                    >
                                        {email}
                                        <button
                                            type="button"
                                            onClick={() => handleRemoveEmail(email)}
                                            className="ml-1 text-red-500 hover:text-red-700"
                                        >
                                            ×
                                        </button>
                                    </span>
                                ))}
                            </div>
                        </div>
                    )}
                    <motion.button
                        type="submit"
                        className="bg-orange-400 text-white px-4 py-2 rounded hover:bg-orange-500"
                        whileTap={{ scale: 0.95 }}
                    >
                        Send Notification
                    </motion.button>
                </form>
            </motion.div>
        )
    );
}
