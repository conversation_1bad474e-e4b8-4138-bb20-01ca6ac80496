'use client';

import StatsCards from './StatsCards';
import RevenueChart from './RevenueChart';
import TransactionsTable from './TransactionsTable';

export default function Dashboard() {
  return (
    <section className="m-5">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-gray-800">Dashboard</h1>
        <p className="text-gray-600">Welcome back, Admin!</p>
      </div>

      <StatsCards />

      <div className="mt-6 grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm lg:col-span-2">
          <div className="p-4 border-b border-gray-200">
            <h2 className="font-semibold text-gray-800">Revenue Overview</h2>
          </div>
          <div className="p-4">
            <RevenueChart />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-4 border-b border-gray-200">
            <h2 className="font-semibold text-gray-800">Recent Activity</h2>
          </div>
          <div className="p-4">
            <ul className="space-y-4">
              {[1, 2, 3].map((item) => (
                <li key={item} className="flex items-center space-x-3">
                  <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600">
                    U
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-800">New user registered</p>
                    <p className="text-xs text-gray-500">{item} hour{item > 1 ? 's' : ''} ago</p>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      <div className="mt-6 bg-white rounded-lg shadow-sm">
        <div className="p-4 border-b border-gray-200">
          <h2 className="font-semibold text-gray-800">Latest Transactions</h2>
        </div>
        <TransactionsTable />
      </div>
    </section>
  );
}
