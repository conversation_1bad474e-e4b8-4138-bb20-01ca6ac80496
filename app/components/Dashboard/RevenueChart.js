'use client';

import { useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';

export default function RevenueChart() {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);
  
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const revenueData = [18500, 22000, 19000, 26000, 24000, 33000, 26000, 30000, 28000, 35000, 29000, 36000];
  
  useEffect(() => {
    const initializeChart = () => {
      const ctx = chartRef.current.getContext('2d');
      
      // Create gradient background
      const gradient = ctx.createLinearGradient(0, 0, 0, 400);
      gradient.addColorStop(0, 'rgba(102, 126, 234, 0.5)');
      gradient.addColorStop(1, 'rgba(102, 126, 234, 0.0)');
      
      // Destroy existing chart if it exists
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
      
      chartInstance.current = new Chart(ctx, {
        type: 'line',
        data: {
          labels: months,
          datasets: [{
            label: 'Revenue',
            data: revenueData,
            borderColor: '#667eea',
            backgroundColor: gradient,
            tension: 0.4,
            fill: true,
            pointBackgroundColor: '#667eea',
            pointBorderColor: '#fff',
            pointBorderWidth: 2,
            pointRadius: 4,
            pointHoverRadius: 6,
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              backgroundColor: '#1f2937',
              titleFont: {
                size: 14,
                weight: 'bold',
              },
              bodyFont: {
                size: 14
              },
              padding: 12,
              cornerRadius: 6,
              callbacks: {
                label: function(context) {
                  return `Revenue: $${context.parsed.y.toLocaleString()}`;
                }
              }
            }
          },
          scales: {
            x: {
              grid: {
                display: false
              }
            },
            y: {
              beginAtZero: true,
              grid: {
                color: 'rgba(0, 0, 0, 0.05)'
              },
              ticks: {
                callback: function(value) {
                  return '$' + value / 1000 + 'k';
                }
              }
            }
          }
        }
      });
    };

    initializeChart();
    
    // Cleanup function to destroy chart when component unmounts
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, []); // Empty dependency array means this runs once on mount

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-lg font-medium mb-4">Revenue Overview</h2>
      <div className="h-80">
        <canvas ref={chartRef}></canvas>
      </div>
    </div>
  );
}
