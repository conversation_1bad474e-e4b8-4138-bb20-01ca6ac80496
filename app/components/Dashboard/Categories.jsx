"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import Swal from 'sweetalert2';
import { FiEdit, FiTrash2, FiStar, FiX, FiPackage, FiImage, FiPlus } from 'react-icons/fi';
import { BsPersonCircle, BsCalendarDate } from 'react-icons/bs';
import Image from 'next/image';

export const getAllCategories = async function () {
    const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/categories`, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${localStorage.getItem("superAdminAuthToken")}`
        },
    });

    const data = await response.json();
    return data;
};

export const deleteCategory = async function (id) {
    const response = await fetch(`${process.env.NEXT_PUBLIC_WEB_SERVER_URL}/categories/${id}`, {
        method: "DELETE",
        headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${localStorage.getItem("superAdminAuthToken")}`
        },
    });

    const data = await response.json();
    return data;
};

// task !! list categories (get) | need to check UI

export default function Categories() {
    const [fetchCategories, setFetchCategories] = useState([]);
    const [categories, setCategories] = useState([]);
    const [pagination, setPagination] = useState(null);
    const [error, setError] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchCategories = async () => {
            try {
                setLoading(true);
                const data = await getAllCategories();
                console.log(data);

                if (data.success) {
                    setFetchCategories(data.data.categories.data);
                    setCategories(structuredClone(data.data.categories.data));
                    setPagination({
                        currentPage: data.data.categories.current_page,
                        lastPage: data.data.categories.last_page,
                        total: data.data.categories.total,
                        links: data.data.categories.links
                    });
                } else {
                    setError(data.message || "Failed to fetch categories");
                }
            } catch (error) {
                setError("Failed to connect to the server");
                console.error("Error fetching categories:", error);
            } finally {
                setLoading(false);
            }
        };

        fetchCategories();
    }, []);

    const handleDelete = async (id) => {
        const result = await Swal.fire({
            title: "Are you sure?",
            text: "You won't be able to revert this!",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes, delete it!",
        });

        if (!result.isConfirmed) {
            return;
        }

        try {
            const response = await deleteCategory(id);

            if (response.success) {
                setCategories(categories.filter((category) => category.id !== id));
                setPagination({
                    ...pagination,
                    total: pagination.total - 1
                });

                Swal.fire({
                    title: response.message || "Category deleted successfully",
                    icon: "success",
                    timer: 1500,
                    showConfirmButton: false,
                });
            } else {
                Swal.fire({
                    title: response.message || "Failed to delete category",
                    text: response.details,
                    icon: "error",
                    timer: 1500,
                    showConfirmButton: false,
                });
            }
        } catch (error) {
            console.error("Error deleting category:", error);
            Swal.fire({
                title: "Error",
                text: "An unexpected error occurred",
                icon: "error",
                timer: 1500,
                showConfirmButton: false,
            });
        }
    };

    return (
        <div className="p-4 text-gray-700">
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white p-6 rounded-2xl shadow-md"
            >
                {error && (
                    <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg mb-4 text-base">
                        {error}
                    </div>
                )}

                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                    <h2 className="text-2xl font-semibold text-orange-400">Categories</h2>
                    <Link href={`/super-admin/manage-brands-categories/categories/add`}>
                        <button className='flex items-center gap-2 text-white bg-gradient-to-r from-orange-400 to-orange-500 hover:from-orange-500 hover:to-orange-600 px-4 py-2 rounded-xl shadow-md transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1'>
                            <FiPlus className="text-3xl" />
                            <span className="font-medium">Add New Category</span>
                        </button>
                    </Link>
                </div>

                {pagination && (
                    <span className="text-base text-gray-600">
                        Showing {categories.length} of {pagination.total} categories
                    </span>
                )}

                {loading ? (
                    <div className="flex justify-center items-center py-10">
                        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
                    </div>
                ) : (
                    <div className="space-y-4 mt-4">
                        {categories.length === 0 ? (
                            <div className="text-center py-8 bg-gray-50 rounded-lg">
                                <p className="text-gray-500 text-lg">No categories found</p>
                            </div>
                        ) : (
                            categories.map((category) => (
                                <motion.div
                                    key={category.id}
                                    whileHover={{ scale: 1.01 }}
                                    className="flex flex-col md:flex-row justify-between items-start p-4 bg-gray-50 rounded-lg hover:bg-gray-100 border border-gray-100"
                                >
                                    <div className="flex-1 w-full">
                                        <div className="flex flex-wrap items-center gap-2 mb-2">
                                            <h3 className="font-medium text-xl">{category.name}</h3>
                                            <span className="text-sm text-gray-500">({category.slug})</span>
                                            <div className="flex flex-wrap gap-2">
                                                {category.is_featured && (
                                                    <span className="px-2 py-1 bg-orange-100 text-orange-600 text-xs rounded-full flex items-center gap-1">
                                                        <FiStar className="text-3xl" />
                                                        Featured
                                                    </span>
                                                )}
                                                {!category.is_active && (
                                                    <span className="px-2 py-1 bg-red-100 text-red-600 text-xs rounded-full flex items-center gap-1">
                                                        <FiX className="text-3xl" />
                                                        Inactive
                                                    </span>
                                                )}
                                                {category.is_active && (
                                                    <span className="px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full flex items-center gap-1">
                                                        <svg xmlns="http://www.w3.org/2000/svg" className="w-8 h-8" viewBox="0 0 20 20" fill="currentColor">
                                                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                        </svg>
                                                        Active
                                                    </span>
                                                )}
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-1 gap-2 text-sm">
                                            {category.description && (
                                                <p className="text-gray-600 mb-2 break-words">{category.description}</p>
                                            )}

                                            <div className="flex flex-wrap items-center gap-3 text-gray-500">
                                                <div className="flex items-center gap-1">
                                                    <FiPackage className="text-3xl text-purple-600" />
                                                    <span>Products: {category.product_count}</span>
                                                </div>

                                                {category.parent_id && (
                                                    <div className="flex items-center gap-1">
                                                        <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                                                            <path fillRule="evenodd" d="M5 4a1 1 0 011-1h8a1 1 0 011 1v1h1a2 2 0 012 2v10a2 2 0 01-2 2H4a2 2 0 01-2-2V7a2 2 0 012-2h1V4zm2 0v1h6V4H7z" clipRule="evenodd" />
                                                        </svg>
                                                        <span>Parent: {category.parent_id}</span>
                                                    </div>
                                                )}

                                                <div className="flex items-center gap-1">
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="w-8 h-8 text-sky-500" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fillRule="evenodd" d="M10 2a1 1 0 00-1 1v1a1 1 0 002 0V3a1 1 0 00-1-1zM4 4h3a3 3 0 006 0h3a2 2 0 012 2v9a2 2 0 01-2 2H4a2 2 0 01-2-2V6a2 2 0 012-2zm2.5 7a1.5 1.5 0 100-3 1.5 1.5 0 000 3zm2.45 4a2.5 2.5 0 10-4.9 0h4.9zM12 9a1 1 0 100 2h3a1 1 0 100-2h-3zm-1 4a1 1 0 011-1h2a1 1 0 110 2h-2a1 1 0 01-1-1z" clipRule="evenodd" />
                                                    </svg>
                                                    <span>ID: {category.id}</span>
                                                </div>
                                            </div>

                                            <div className="text-gray-500 mt-2">
                                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-1 text-xs">
                                                    <p className="flex items-center gap-1">
                                                        <BsCalendarDate className="text-3xl text-blue-600" />
                                                        Created: {new Date(category.created_at).toLocaleDateString()}
                                                    </p>
                                                    <p className="flex items-center gap-1">
                                                        <BsCalendarDate className="text-3xl text-blue-600" />
                                                        Updated: {new Date(category.updated_at).toLocaleDateString()}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>

                                        {category.image ? (
                                            <div className="mt-2">
                                                <div className="flex items-center gap-1 text-xs text-gray-500 mb-1">
                                                    <FiImage className="text-3xl text-pink-500" />
                                                    Category Image:
                                                </div>
                                                <Image
                                                    src={category.image}
                                                    alt={`${category.name} image`}
                                                    width={150}
                                                    height={150}
                                                    className="h-40 w-auto object-contain rounded-md border border-gray-200"
                                                    priority
                                                />
                                            </div>
                                        ) : (
                                            <div className="mt-2 bg-orange-50 h-40 w-auto p-2 rounded-lg text-white">
                                                <div className="flex items-center gap-1 text-xs text-gray-500 mb-1">
                                                    <FiImage className="text-3xl text-pink-500" />
                                                    Category Image Not Found
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    <div className="flex flex-row md:flex-col gap-2 mt-3 md:mt-0 w-full md:w-auto justify-end">
                                        <Link href={`/super-admin/manage-brands-categories/categories/${category.id}/update`} className="inline-block">
                                            <button
                                                className="text-blue-500 hover:text-blue-700 p-2 bg-blue-50 hover:bg-blue-100 rounded-full transition-colors duration-200"
                                                title="Edit Category"
                                            >
                                                <FiEdit className="text-3xl" />
                                            </button>
                                        </Link>

                                        <button
                                            onClick={() => handleDelete(category.id)}
                                            className="text-red-500 hover:text-red-700 p-2 bg-red-50 hover:bg-red-100 rounded-full transition-colors duration-200"
                                            title="Delete Category"
                                        >
                                            <FiTrash2 className="text-3xl" />
                                        </button>
                                    </div>
                                </motion.div>
                            ))
                        )}
                    </div>
                )}
            </motion.div>
        </div>
    );
}
