'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
    FaBuilding,
    FaStar,
    FaGlobe,
    FaBox,
    FaCheckCircle,
    FaTimesCircle
} from 'react-icons/fa';

// task!! need to review later
const BrandCard = ({ brand, viewMode = 'grid', index = 0 }) => {
    // Sanitize brand data to prevent XSS
    const sanitizedBrand = {
        id: brand?.id || '',
        name: brand?.name || 'Unknown Brand',
        slug: brand?.slug || '',
        description: brand?.description || '',
        image: brand?.image || null,
        is_featured: <PERSON><PERSON>an(brand?.is_featured),
        is_active: <PERSON><PERSON><PERSON>(brand?.is_active),
        website: brand?.website || null,
        product_count: parseInt(brand?.product_count) || 0
    };

    const cardVariants = {
        hidden: { opacity: 0, scale: 0.9 },
        visible: {
            opacity: 1,
            scale: 1,
            transition: { delay: index * 0.05, duration: 0.3 }
        }
    };

    if (viewMode === 'list') {
        return (
            <motion.div
                variants={cardVariants}
                initial="hidden"
                animate="visible"
                className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 p-3 sm:p-4 border border-gray-200"
            >
                {/* Mobile Layout (< sm) */}
                <div className="block sm:hidden">
                    <div className="flex items-start gap-3 mb-3">
                        {/* Brand Image - Smaller on mobile */}
                        <div className="relative w-12 h-12 flex-shrink-0">
                            {sanitizedBrand.image ? (
                                <Image
                                    src={`https://b2b.instinctfusionx.xyz/public${sanitizedBrand.image}`}
                                    alt={sanitizedBrand.name}
                                    fill
                                    className="object-cover rounded-lg"
                                    sizes="48px"
                                    onError={(e) => {
                                        e.target.style.display = 'none';
                                        e.target.nextSibling.style.display = 'flex';
                                    }}
                                />
                            ) : null}
                            <div className={`w-full h-full bg-gradient-to-br from-orange-100 to-orange-200 rounded-lg flex items-center justify-center ${sanitizedBrand.image ? 'hidden' : 'flex'}`}>
                                <FaBuilding className="text-orange-400 text-sm" />
                            </div>
                        </div>

                        {/* Brand Info - Compact mobile layout */}
                        <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between mb-2">
                                <div className="flex-1 min-w-0">
                                    <h3 className="font-semibold text-gray-900 text-base leading-tight mb-1">
                                        {sanitizedBrand.name}
                                    </h3>
                                    {sanitizedBrand.is_featured && (
                                        <span className="bg-orange-100 text-orange-600 px-2 py-1 rounded-full text-xs font-medium inline-flex items-center gap-1">
                                            <FaStar className="text-xs" />
                                            Featured
                                        </span>
                                    )}
                                </div>
                            </div>

                            {/* Compact stats for mobile */}
                            <div className="flex items-center gap-3 text-xs text-gray-500 mb-2">
                                <span>ID: {sanitizedBrand.id}</span>
                                <span className="flex items-center gap-1">
                                    <FaBox className="text-gray-400" />
                                    {sanitizedBrand.product_count}
                                </span>
                                <div className="flex items-center gap-1">
                                    {sanitizedBrand.is_active ? (
                                        <FaCheckCircle className="text-green-500" title="Active" />
                                    ) : (
                                        <FaTimesCircle className="text-red-500" title="Inactive" />
                                    )}
                                    {sanitizedBrand.website && (
                                        <FaGlobe className="text-orange-400" title="Has Website" />
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Full-width button on mobile */}
                    <Link
                        href={`/products/brands/${sanitizedBrand.id}?per_page=12`}
                        className="block w-full bg-orange-500 text-white py-2.5 px-4 rounded-lg text-sm font-medium hover:bg-orange-600 transition-colors text-center"
                    >
                        View Products
                    </Link>
                </div>

                {/* Desktop/Tablet Layout (>= sm) */}
                <div className="hidden sm:flex items-center gap-4">
                    {/* Brand Image */}
                    <div className="relative w-16 h-16 flex-shrink-0">
                        {sanitizedBrand.image ? (
                            <Image
                                src={`https://b2b.instinctfusionx.xyz/public${sanitizedBrand.image}`}
                                alt={sanitizedBrand.name}
                                fill
                                className="object-cover rounded-lg"
                                sizes="64px"
                                onError={(e) => {
                                    e.target.style.display = 'none';
                                    e.target.nextSibling.style.display = 'flex';
                                }}
                            />
                        ) : null}
                        <div className={`w-full h-full bg-gradient-to-br from-orange-100 to-orange-200 rounded-lg flex items-center justify-center ${sanitizedBrand.image ? 'hidden' : 'flex'}`}>
                            <FaBuilding className="text-orange-400 text-xl" />
                        </div>
                    </div>

                    {/* Brand Info */}
                    <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                    <h3 className="font-semibold text-gray-900 truncate text-lg">
                                        {sanitizedBrand.name}
                                    </h3>
                                    {sanitizedBrand.is_featured && (
                                        <span className="bg-orange-100 text-orange-600 px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                                            <FaStar className="text-xs" />
                                            Featured
                                        </span>
                                    )}
                                </div>
                                <p className="text-xs text-gray-500 mb-1">
                                    ID: {sanitizedBrand.id} • Slug: {sanitizedBrand.slug}
                                </p>
                                {sanitizedBrand.description && (
                                    <p className="text-sm text-gray-600 line-clamp-1 mb-2">
                                        {sanitizedBrand.description}
                                    </p>
                                )}
                                <div className="flex items-center gap-4">
                                    <div className="flex items-center gap-1">
                                        <FaBox className="text-gray-400 text-xs" />
                                        <span className="text-xs text-gray-600">
                                            {sanitizedBrand.product_count} products
                                        </span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        {sanitizedBrand.is_active ? (
                                            <FaCheckCircle className="text-green-500 text-sm" title="Active" />
                                        ) : (
                                            <FaTimesCircle className="text-red-500 text-sm" title="Inactive" />
                                        )}
                                        {sanitizedBrand.website && (
                                            <FaGlobe className="text-orange-400 text-sm" title="Has Website" />
                                        )}
                                    </div>
                                </div>
                            </div>

                            <div className="text-right ml-4">
                                <Link
                                    href={`/products/brand/${sanitizedBrand.id}?per_page=12`}
                                    className="bg-orange-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-orange-600 transition-colors inline-flex items-center gap-2"
                                >
                                    <FaBox className="text-xs" />
                                    View Products
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </motion.div>
        );
    }

    // Grid View
    return (
        <motion.div
            variants={cardVariants}
            initial="hidden"
            animate="visible"
            className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 p-6 border border-gray-200 group"
        >
            {/* Brand Image */}
            <div className="relative mb-4 aspect-square">
                {sanitizedBrand.image ? (
                    <Image
                        src={`https://b2b.instinctfusionx.xyz/public${sanitizedBrand.image}`}
                        alt={sanitizedBrand.name}
                        fill
                        className="object-cover rounded-lg"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                        onError={(e) => {
                            e.target.style.display = 'none';
                            e.target.nextSibling.style.display = 'flex';
                        }}
                    />
                ) : null}
                <div className={`w-full h-full bg-gradient-to-br from-orange-100 to-orange-200 rounded-lg flex items-center justify-center ${sanitizedBrand.image ? 'hidden' : 'flex'}`}>
                    <FaBuilding className="text-orange-400 text-4xl" />
                </div>

                {/* Featured Badge */}
                {sanitizedBrand.is_featured && (
                    <div className="absolute top-2 right-2 bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center gap-1">
                        <FaStar className="text-xs" />
                        Featured
                    </div>
                )}
            </div>

            {/* Brand Info */}
            <div className="space-y-3">
                <div>
                    <h3 className="font-semibold text-gray-900 line-clamp-2 group-hover:text-orange-500 transition-colors text-lg">
                        {sanitizedBrand.name}
                    </h3>
                    <p className="text-xs text-gray-500 mt-1">
                        ID: {sanitizedBrand.id} • Slug: {sanitizedBrand.slug}
                    </p>
                </div>

                {sanitizedBrand.description && (
                    <p className="text-sm text-gray-600 line-clamp-2">
                        {sanitizedBrand.description}
                    </p>
                )}

                {/* Stats */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <FaBox className="text-gray-400 text-sm" />
                        <span className="text-sm text-gray-600">
                            {sanitizedBrand.product_count} products
                        </span>
                    </div>
                    <div className="flex items-center gap-2">
                        <span className={`text-xs px-2 py-1 rounded-full ${sanitizedBrand.is_active
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                            }`}>
                            {sanitizedBrand.is_active ? 'Active' : 'Inactive'}
                        </span>
                        {sanitizedBrand.website && (
                            <FaGlobe className="text-orange-400 text-sm" title="Has Website" />
                        )}
                    </div>
                </div>

                {/* Action Button */}
                <Link
                    href={`/products/brand/${sanitizedBrand.id}?per_page=12`}
                    className="block w-full bg-orange-500 text-white px-4 py-3 rounded-lg text-sm font-medium hover:bg-orange-600 transition-colors text-center mt-4"
                >
                    View Products
                </Link>
            </div>
        </motion.div>
    );
};

export default BrandCard;
