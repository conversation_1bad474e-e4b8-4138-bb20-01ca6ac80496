'use client';

import React, { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
    FaBuilding,
    FaTh,
    FaList,
    FaSearch,
    FaFilter,
    FaSort,
    FaTimes,
    FaChevronLeft,
    FaChevronRight
} from 'react-icons/fa';
import { HiSortAscending, HiSortDescending } from 'react-icons/hi';
import BrandCard from './BrandCard';

const AllBrands = ({ initialData, searchParams }) => {
    const router = useRouter();
    const urlSearchParams = useSearchParams();

    // State Management
    const [searchQuery, setSearchQuery] = useState(searchParams?.search || '');
    const [viewMode, setViewMode] = useState(searchParams?.view || 'grid');
    const [sortBy, setSortBy] = useState(searchParams?.sort || 'name');
    const [sortOrder, setSortOrder] = useState(searchParams?.order || 'asc');
    const [filters, setFilters] = useState({
        is_featured: searchParams?.is_featured || '',
        is_active: searchParams?.is_active || '',
        has_products: searchParams?.has_products || ''
    });
    const [showFilters, setShowFilters] = useState(false);

    // Extract data from initial server response
    const brandsData = initialData?.success ? initialData.data.brands : null;
    const brands = brandsData?.data || [];
    const pagination = brandsData ? {
        current_page: brandsData.current_page,
        last_page: brandsData.last_page,
        per_page: brandsData.per_page,
        total: brandsData.total,
        from: brandsData.from,
        to: brandsData.to,
        links: brandsData.links || []
    } : null;

    // Update URL with new parameters
    const updateURL = (newParams) => {
        const params = new URLSearchParams(urlSearchParams.toString());

        Object.entries(newParams).forEach(([key, value]) => {
            if (value && value !== '') {
                params.set(key, value);
            } else {
                params.delete(key);
            }
        });

        // Reset to page 1 when changing filters/search/sort
        if (newParams.search !== undefined || newParams.sort !== undefined ||
            newParams.order !== undefined || Object.keys(newParams).some(key => key.startsWith('is_'))) {
            params.set('page', '1');
        }

        router.push(`/brands?${params.toString()}`);
    };

    // Handle search
    const handleSearch = (query) => {
        setSearchQuery(query);
        updateURL({ search: query });
    };

    // Handle sorting
    const handleSort = (field) => {
        const newOrder = sortBy === field && sortOrder === 'asc' ? 'desc' : 'asc';
        setSortBy(field);
        setSortOrder(newOrder);
        updateURL({ sort: field, order: newOrder });
    };

    // Handle filtering
    const handleFilterChange = (filterKey, value) => {
        const newFilters = { ...filters, [filterKey]: value };
        setFilters(newFilters);
        updateURL({ [filterKey]: value });
    };

    // Handle view mode change
    const handleViewModeChange = (mode) => {
        setViewMode(mode);
        updateURL({ view: mode });
    };

    // Handle pagination
    const handlePageChange = (page) => {
        updateURL({ page: page.toString() });
    };

    // Clear all filters
    const clearFilters = () => {
        setSearchQuery('');
        setFilters({
            is_featured: '',
            is_active: '',
            has_products: ''
        });
        setSortBy('name');
        setSortOrder('asc');
        router.push('/brands');
    };

    // Get pagination links from API response
    const getPaginationLinks = () => {
        if (!pagination?.links) return [];

        return pagination.links.filter(link =>
            link.label !== '&laquo; Previous' &&
            link.label !== 'Next &raquo;'
        );
    };

    const paginationLinks = getPaginationLinks();

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header Section */}
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white shadow-sm border-b"
            >
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <div className="text-center">
                        <div className="relative">
                            <h1 className="text-5xl md:text-6xl font-extrabold bg-gradient-to-r from-gray-900 via-orange-600 to-gray-900 bg-clip-text text-transparent mb-6 leading-tight">
                                Partner Brands
                            </h1>

                            {/* Enhanced decorative elements */}
                            <div className="flex items-center justify-center gap-3 mb-6">
                                <div className="h-0.5 w-12 bg-gradient-to-r from-transparent to-orange-400 rounded-full"></div>
                                <div className="h-2 w-2 bg-orange-400 rounded-full animate-pulse"></div>
                                <div className="h-0.5 w-24 bg-orange-400 rounded-full"></div>
                                <div className="h-2 w-2 bg-orange-400 rounded-full animate-pulse"></div>
                                <div className="h-0.5 w-12 bg-gradient-to-l from-transparent to-orange-400 rounded-full"></div>
                            </div>

                            <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium">
                                Discover our trusted partner brands and their exceptional product offerings.
                                <span className="text-orange-500 font-semibold"> Quality you can trust, partnerships you can rely on.</span>
                            </p>

                            {/* Subtle background decoration */}
                            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 w-32 h-32 bg-orange-100 rounded-full opacity-20 blur-3xl -z-10"></div>
                        </div>

                        {/* Enhanced Stats Card */}
                        <div className="mt-8 relative max-w-md mx-auto">
                            <div className="bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600 rounded-3xl p-8 text-white shadow-2xl transform hover:scale-105 transition-all duration-300">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-orange-100 text-sm font-medium uppercase tracking-wider">Total Brands</p>
                                        <p className="text-4xl font-bold mt-2 bg-gradient-to-r from-white to-orange-100 bg-clip-text text-transparent">
                                            {pagination?.total || 0}
                                        </p>
                                        <p className="text-orange-200 text-xs mt-1">Trusted partners</p>
                                    </div>
                                    <div className="relative">
                                        <div className="absolute inset-0 bg-white/20 rounded-full blur-xl"></div>
                                        <FaBuilding className="h-12 w-12 text-white/90 relative z-10" />
                                    </div>
                                </div>

                                {/* Decorative elements */}
                                <div className="absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full blur-2xl"></div>
                                <div className="absolute bottom-4 left-4 w-16 h-16 bg-orange-300/20 rounded-full blur-xl"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </motion.div>

            {/* Search and Filter Section */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                    className="bg-white/80 backdrop-blur-sm rounded-2xl sm:rounded-3xl shadow-xl border border-gray-100 p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8"
                >
                    {/* Search and Controls - Mobile Responsive */}
                    <div className="flex flex-col gap-4 sm:gap-6 mb-6 sm:mb-8">
                        {/* Mobile: Search and View Toggle in optimized layout */}
                        <div className="flex flex-col sm:flex-row gap-4 sm:gap-6">
                            {/* Search Bar */}
                            <div className="flex-1">
                                <label className="block text-sm font-semibold text-gray-700 mb-2 sm:mb-3">
                                    Search Brands
                                </label>
                                <div className="relative group">
                                    <div className="absolute inset-0 bg-gradient-to-r from-orange-400 to-orange-500 rounded-xl sm:rounded-2xl blur opacity-25 group-hover:opacity-40 transition-opacity"></div>
                                    <div className="relative">
                                        <FaSearch className="absolute left-3 sm:left-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-hover:text-orange-500 transition-colors z-10 text-sm" />
                                        <input
                                            type="text"
                                            placeholder="Search brands..."
                                            value={searchQuery}
                                            onChange={(e) => handleSearch(e.target.value)}
                                            className="w-full pl-10 sm:pl-12 pr-10 sm:pr-12 py-3 sm:py-4 bg-white border-2 border-gray-200 rounded-xl sm:rounded-2xl focus:outline-none focus:ring-4 focus:ring-orange-100 focus:border-orange-400 transition-all duration-300 text-gray-700 placeholder-gray-400 shadow-sm text-sm sm:text-base"
                                            aria-label="Search brands"
                                        />
                                        {searchQuery && (
                                            <button
                                                onClick={() => handleSearch('')}
                                                className="absolute right-3 sm:right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500 transition-colors z-10 p-1 hover:bg-red-50 rounded-full"
                                                aria-label="Clear search"
                                            >
                                                <FaTimes className="text-sm" />
                                            </button>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {/* View Toggle - Compact on mobile */}
                            <div className="flex flex-col gap-2 sm:gap-3 sm:min-w-0">
                                <label className="text-sm font-semibold text-gray-700 hidden sm:block">
                                    View Mode
                                </label>
                                <div className="flex items-center gap-1 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl sm:rounded-2xl p-1 sm:p-1.5 shadow-inner self-start sm:self-auto">
                                    <button
                                        onClick={() => handleViewModeChange('grid')}
                                        className={`p-2 sm:p-3 rounded-lg sm:rounded-xl transition-all duration-300 flex items-center gap-1 sm:gap-2 ${viewMode === 'grid'
                                            ? 'bg-gradient-to-r from-orange-400 to-orange-500 text-white shadow-lg transform scale-105'
                                            : 'text-gray-600 hover:bg-white hover:shadow-md'
                                            }`}
                                        aria-label="Grid view"
                                    >
                                        <FaTh className="text-xs sm:text-sm" />
                                        <span className="text-xs font-medium hidden sm:block">Grid</span>
                                    </button>
                                    <button
                                        onClick={() => handleViewModeChange('list')}
                                        className={`p-2 sm:p-3 rounded-lg sm:rounded-xl transition-all duration-300 flex items-center gap-1 sm:gap-2 ${viewMode === 'list'
                                            ? 'bg-gradient-to-r from-orange-400 to-orange-500 text-white shadow-lg transform scale-105'
                                            : 'text-gray-600 hover:bg-white hover:shadow-md'
                                            }`}
                                        aria-label="List view"
                                    >
                                        <FaList className="text-xs sm:text-sm" />
                                        <span className="text-xs font-medium hidden sm:block">List</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Sort and Filter Controls - Mobile Responsive */}
                    <div className="flex flex-col gap-3 sm:gap-4">
                        {/* Mobile: Stack sort and filter controls */}
                        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-between items-start sm:items-center">
                            {/* Sort Dropdown */}
                            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4 w-full sm:w-auto">
                                <div className="flex items-center gap-2">
                                    <FaSort className="text-gray-500 text-sm" />
                                    <label className="text-sm font-semibold text-gray-700">Sort by:</label>
                                </div>
                                <select
                                    value={`${sortBy}-${sortOrder}`}
                                    onChange={(e) => {
                                        const [field, order] = e.target.value.split('-');
                                        handleSort(field);
                                        setSortOrder(order);
                                    }}
                                    className="w-full sm:w-auto px-3 sm:px-4 py-2 border border-gray-300 rounded-lg sm:rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-orange-400 bg-white text-gray-700 shadow-sm text-sm"
                                >
                                    <option value="name-asc">Name (A-Z)</option>
                                    <option value="name-desc">Name (Z-A)</option>
                                    <option value="id-asc">ID (Low to High)</option>
                                    <option value="id-desc">ID (High to Low)</option>
                                    <option value="slug-asc">Slug (A-Z)</option>
                                    <option value="slug-desc">Slug (Z-A)</option>
                                </select>
                            </div>

                            {/* Filter Toggle and Clear */}
                            <div className="flex items-center gap-2 sm:gap-3 w-full sm:w-auto">
                                <button
                                    onClick={() => setShowFilters(!showFilters)}
                                    className={`flex-1 sm:flex-none px-3 sm:px-4 py-2 rounded-lg sm:rounded-xl font-medium transition-all flex items-center justify-center gap-2 text-sm ${showFilters
                                        ? 'bg-orange-500 text-white shadow-lg'
                                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                        }`}
                                >
                                    <FaFilter className="text-xs sm:text-sm" />
                                    <span className="hidden sm:inline">Filters</span>
                                    <span className="sm:hidden">Filter</span>
                                </button>

                                {(searchQuery || Object.values(filters).some(f => f) || sortBy !== 'name' || sortOrder !== 'asc') && (
                                    <button
                                        onClick={clearFilters}
                                        className="flex-1 sm:flex-none px-3 sm:px-4 py-2 bg-red-100 text-red-700 rounded-lg sm:rounded-xl font-medium hover:bg-red-200 transition-colors flex items-center justify-center gap-2 text-sm"
                                    >
                                        <FaTimes className="text-xs sm:text-sm" />
                                        <span className="hidden sm:inline">Clear All</span>
                                        <span className="sm:hidden">Clear</span>
                                    </button>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Filters Panel */}
                    <AnimatePresence>
                        {showFilters && (
                            <motion.div
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: 'auto' }}
                                exit={{ opacity: 0, height: 0 }}
                                className="mt-4 sm:mt-6 p-4 sm:p-6 bg-gray-50 rounded-xl sm:rounded-2xl border border-gray-200"
                            >
                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Featured Status
                                        </label>
                                        <select
                                            value={filters.is_featured}
                                            onChange={(e) => handleFilterChange('is_featured', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-orange-400"
                                        >
                                            <option value="">All</option>
                                            <option value="1">Featured Only</option>
                                            <option value="0">Non-Featured</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Active Status
                                        </label>
                                        <select
                                            value={filters.is_active}
                                            onChange={(e) => handleFilterChange('is_active', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-orange-400"
                                        >
                                            <option value="">All</option>
                                            <option value="1">Active Only</option>
                                            <option value="0">Inactive</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Has Products
                                        </label>
                                        <select
                                            value={filters.has_products}
                                            onChange={(e) => handleFilterChange('has_products', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-orange-400"
                                        >
                                            <option value="">All</option>
                                            <option value="1">With Products</option>
                                            <option value="0">No Products</option>
                                        </select>
                                    </div>
                                </div>
                            </motion.div>
                        )}
                    </AnimatePresence>
                </motion.div>

                {/* Results Info */}
                {pagination && (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                        className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6"
                    >
                        <div className="text-gray-600">
                            Showing {pagination.from || 0} to {pagination.to || 0} of {pagination.total || 0} brands
                        </div>
                        <div className="flex items-center gap-2">
                            <label htmlFor="perPage" className="text-sm text-gray-600">
                                Items per page:
                            </label>
                            <select
                                id="perPage"
                                value={pagination.per_page}
                                onChange={(e) => updateURL({ per_page: e.target.value, page: '1' })}
                                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-orange-400 bg-white text-gray-700"
                            >
                                <option value={12}>12</option>
                                <option value={15}>15</option>
                                <option value={24}>24</option>
                                <option value={48}>48</option>
                                <option value={96}>96</option>
                            </select>
                        </div>
                    </motion.div>
                )}

                {/* Brands Grid */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className={`grid gap-6 mb-8 ${viewMode === 'grid'
                        ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                        : 'grid-cols-1'
                        }`}
                >
                    <AnimatePresence mode="wait">
                        {brands.length > 0 ? (
                            brands.map((brand, index) => (
                                <BrandCard
                                    key={brand.id}
                                    brand={brand}
                                    viewMode={viewMode}
                                    index={index}
                                />
                            ))
                        ) : (
                            <motion.div
                                initial={{ opacity: 0, scale: 0.9 }}
                                animate={{ opacity: 1, scale: 1 }}
                                className="col-span-full flex flex-col items-center justify-center py-16 text-center"
                            >
                                <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6">
                                    <FaBuilding className="w-12 h-12 text-gray-400" />
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">No brands found</h3>
                                <p className="text-gray-600 mb-6 max-w-md">
                                    {searchQuery || Object.values(filters).some(f => f)
                                        ? "Try adjusting your search or filters to find what you're looking for."
                                        : "There are no brands available at the moment."
                                    }
                                </p>
                                {(searchQuery || Object.values(filters).some(f => f)) && (
                                    <button
                                        onClick={clearFilters}
                                        className="bg-orange-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-orange-600 transition-colors"
                                    >
                                        Clear Filters
                                    </button>
                                )}
                            </motion.div>
                        )}
                    </AnimatePresence>
                </motion.div>

                {/* API-based Pagination */}
                {pagination && pagination.last_page > 1 && (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 }}
                        className="flex flex-col sm:flex-row justify-between items-center gap-4 bg-white rounded-2xl shadow-lg p-6"
                    >
                        <div className="text-gray-600 text-sm">
                            Page {pagination.current_page} of {pagination.last_page}
                        </div>

                        <div className="flex items-center gap-2">
                            {/* Previous Button */}
                            <button
                                onClick={() => handlePageChange(pagination.current_page - 1)}
                                disabled={pagination.current_page === 1}
                                className={`px-4 py-2 rounded-lg font-medium transition-all flex items-center gap-2 ${pagination.current_page === 1
                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                    : 'bg-orange-500 text-white hover:bg-orange-600'
                                    }`}
                                aria-label="Previous page"
                            >
                                <FaChevronLeft className="w-4 h-4" />
                                <span className="hidden sm:inline">Previous</span>
                            </button>

                            {/* Page Numbers */}
                            <div className="flex items-center gap-1">
                                {paginationLinks.map((link, index) => (
                                    <button
                                        key={index}
                                        onClick={() => {
                                            if (link.url) {
                                                const url = new URL(link.url);
                                                const page = url.searchParams.get('page');
                                                if (page) handlePageChange(parseInt(page));
                                            }
                                        }}
                                        disabled={!link.url}
                                        className={`w-10 h-10 rounded-lg font-medium transition-all ${link.active
                                            ? 'bg-orange-500 text-white'
                                            : link.url
                                                ? 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                                : 'bg-gray-50 text-gray-400 cursor-not-allowed'
                                            }`}
                                        aria-label={`Go to page ${link.label}`}
                                    >
                                        {link.label}
                                    </button>
                                ))}
                            </div>

                            {/* Next Button */}
                            <button
                                onClick={() => handlePageChange(pagination.current_page + 1)}
                                disabled={pagination.current_page === pagination.last_page}
                                className={`px-4 py-2 rounded-lg font-medium transition-all flex items-center gap-2 ${pagination.current_page === pagination.last_page
                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                    : 'bg-orange-500 text-white hover:bg-orange-600'
                                    }`}
                                aria-label="Next page"
                            >
                                <span className="hidden sm:inline">Next</span>
                                <FaChevronRight className="w-4 h-4" />
                            </button>
                        </div>
                    </motion.div>
                )}
            </div>
        </div>
    );
};

export default AllBrands;