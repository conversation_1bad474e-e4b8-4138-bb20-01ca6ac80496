'use client';

import React, { useState, useEffect } from 'react';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import {
    MagnifyingGlassIcon,
    Bars3BottomLeftIcon,
    CheckCircleIcon,
    ExclamationTriangleIcon,
    ArrowPathIcon
} from '@heroicons/react/24/outline';
import Image from 'next/image';
import Swal from 'sweetalert2';

export default function ProductRanking() {
    const [categories, setCategories] = useState([]);
    const [selectedCategory, setSelectedCategory] = useState('');
    const [products, setProducts] = useState([]);
    const [sortedProducts, setSortedProducts] = useState([]);
    const [loading, setLoading] = useState(false);
    const [loadingSorted, setLoadingSorted] = useState(false);
    const [saving, setSaving] = useState(false);
    const [hasChanges, setHasChanges] = useState(false);

    // Fetch categories on component mount
    useEffect(() => {
        fetchCategories();
    }, []);

    const fetchCategories = async () => {
        try {
            const response = await fetch('/api/categories');
            const data = await response.json();

            if (data.success !== false && data.data?.categories?.data) {
                setCategories(data.data.categories.data);
            } else if (data.success !== false && data.data?.categories) {
                setCategories(data.data.categories);
            } else {
                setCategories([]);
            }
        } catch (error) {
            console.error('Error fetching categories:', error);
            Swal.fire({
                title: 'Error',
                text: 'Failed to fetch categories',
                icon: 'error',
                timer: 3000,
                showConfirmButton: false
            });
        }
    };

    const fetchCategoryProducts = async (categoryId) => {
        if (!categoryId) return;

        setLoading(true);
        try {
            const response = await fetch(`/api/categories/${categoryId}/products`);
            const data = await response.json();

            if (data.success !== false && data.data?.products) {
                // Add current rank to products (using array index + 1 as default)
                const productsWithRank = data.data.products.map((product, index) => ({
                    ...product,
                    currentRank: index + 1
                }));
                setProducts(productsWithRank);
                setHasChanges(false);
            } else {
                setProducts([]);
                Swal.fire({
                    title: 'Info',
                    text: 'No products found in this category',
                    icon: 'info',
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        } catch (error) {
            console.error('Error fetching products:', error);
            Swal.fire({
                title: 'Error',
                text: 'Failed to fetch products',
                icon: 'error',
                timer: 3000,
                showConfirmButton: false
            });
        } finally {
            setLoading(false);
        }
    };

    const fetchSortedProducts = async (categoryId) => {
        if (!categoryId) return;

        setLoadingSorted(true);
        try {
            const response = await fetch(`/api/products/category/${categoryId}/sorted`);
            const data = await response.json();

            if (data.success !== false && data.data?.products) {
                setSortedProducts(data.data.products);
            } else {
                setSortedProducts([]);
            }
        } catch (error) {
            console.error('Error fetching sorted products:', error);
            setSortedProducts([]);
        } finally {
            setLoadingSorted(false);
        }
    };

    const handleCategoryChange = (categoryId) => {
        setSelectedCategory(categoryId);
        if (categoryId) {
            fetchCategoryProducts(categoryId);
            fetchSortedProducts(categoryId);
        } else {
            setProducts([]);
            setSortedProducts([]);
        }
    };

    const onDragEnd = (result) => {
        if (!result.destination) return;

        const items = Array.from(products);
        const [reorderedItem] = items.splice(result.source.index, 1);
        items.splice(result.destination.index, 0, reorderedItem);

        // Update ranks based on new positions
        const updatedItems = items.map((item, index) => ({
            ...item,
            currentRank: index + 1
        }));

        setProducts(updatedItems);
        setHasChanges(true);
    };

    const saveRankings = async () => {
        if (!selectedCategory || products.length === 0) return;

        setSaving(true);
        try {
            const rankings = products.map((product, index) => ({
                product_id: product.id,
                rank: index + 1
            }));

            const token = localStorage.getItem('superAdminAuthToken');
            const response = await fetch(`/api/admin/products/category/${selectedCategory}/rankings`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({ rankings })
            });

            const data = await response.json();

            if (data.success !== false) {
                setHasChanges(false);
                // Refresh the sorted products to show updated order
                fetchSortedProducts(selectedCategory);
                Swal.fire({
                    title: 'Success',
                    text: 'Product rankings updated successfully',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });
            } else {
                throw new Error(data.message || 'Failed to update rankings');
            }
        } catch (error) {
            console.error('Error saving rankings:', error);
            Swal.fire({
                title: 'Error',
                text: error.message || 'Failed to save rankings',
                icon: 'error',
                timer: 3000,
                showConfirmButton: false
            });
        } finally {
            setSaving(false);
        }
    };

    const resetRankings = () => {
        if (selectedCategory) {
            fetchCategoryProducts(selectedCategory);
        }
    };

    return (
        <section className="m-5">
            <div className="mb-6">
                <h1 className="text-2xl font-bold text-black flex items-center">
                    <Bars3BottomLeftIcon className="h-6 w-6 mr-2 text-orange-500" />
                    Product Ranking Management
                </h1>
                <p className="text-gray-700 mt-2">Drag and drop products to change their ranking within categories</p>
            </div>

            {/* Category Selection */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
                <div className="flex flex-col md:flex-row md:items-center gap-4">
                    <div className="flex-1">
                        <label htmlFor="category" className="block text-sm font-medium text-black mb-2">
                            Select Category
                        </label>
                        <select
                            id="category"
                            value={selectedCategory}
                            onChange={(e) => handleCategoryChange(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-black bg-white"
                        >
                            <option value="">Choose a category...</option>
                            {categories.map((category) => (
                                <option key={category.id} value={category.id}>
                                    {category.name} ({category.product_count} products)
                                </option>
                            ))}
                        </select>
                    </div>
                    
                    {selectedCategory && (
                        <div className="flex gap-2">
                            <button
                                onClick={resetRankings}
                                disabled={loading}
                                className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 disabled:opacity-50 flex items-center"
                            >
                                <ArrowPathIcon className="h-4 w-4 mr-1" />
                                Reset
                            </button>
                            <button
                                onClick={saveRankings}
                                disabled={!hasChanges || saving}
                                className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 disabled:opacity-50 flex items-center"
                            >
                                {saving ? (
                                    <ArrowPathIcon className="h-4 w-4 mr-1 animate-spin" />
                                ) : (
                                    <CheckCircleIcon className="h-4 w-4 mr-1" />
                                )}
                                {saving ? 'Saving...' : 'Save Rankings'}
                            </button>
                        </div>
                    )}
                </div>
                
                {hasChanges && (
                    <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md flex items-center">
                        <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mr-2" />
                        <span className="text-yellow-900 text-sm font-medium">You have unsaved changes. Don't forget to save your rankings!</span>
                    </div>
                )}
            </div>

            {/* Products List */}
            {loading ? (
                <div className="bg-white rounded-lg shadow-sm p-8 text-center">
                    <ArrowPathIcon className="h-8 w-8 animate-spin mx-auto text-orange-500 mb-4" />
                    <p className="text-black font-medium">Loading products...</p>
                </div>
            ) : products.length > 0 ? (
                <div className="bg-white rounded-lg shadow-sm p-6">
                    <h2 className="text-lg font-bold text-black mb-4">
                        Products in Category ({products.length} items)
                    </h2>
                    
                    <DragDropContext onDragEnd={onDragEnd}>
                        <Droppable droppableId="products">
                            {(provided) => (
                                <div
                                    {...provided.droppableProps}
                                    ref={provided.innerRef}
                                    className="space-y-2"
                                >
                                    {products.map((product, index) => (
                                        <Draggable
                                            key={product.id}
                                            draggableId={product.id.toString()}
                                            index={index}
                                        >
                                            {(provided, snapshot) => (
                                                <div
                                                    ref={provided.innerRef}
                                                    {...provided.draggableProps}
                                                    {...provided.dragHandleProps}
                                                    className={`p-4 border rounded-lg transition-all ${
                                                        snapshot.isDragging
                                                            ? 'shadow-lg bg-orange-50 border-orange-300'
                                                            : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                                                    }`}
                                                >
                                                    <div className="flex items-center gap-4">
                                                        <div className="flex-shrink-0">
                                                            <div className="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center font-semibold">
                                                                {index + 1}
                                                            </div>
                                                        </div>
                                                        
                                                        <div className="flex-shrink-0">
                                                            {product.images && product.images.length > 0 && (product.images[0].image_url || product.images[0].full_image_url) ? (
                                                                <img
                                                                    src={product.images[0].image_url || product.images[0].full_image_url}
                                                                    alt={product.name}
                                                                    width={60}
                                                                    height={60}
                                                                    className="rounded-lg object-cover w-15 h-15"
                                                                    onError={(e) => {
                                                                        e.target.style.display = 'none';
                                                                        e.target.nextSibling.style.display = 'flex';
                                                                    }}
                                                                />
                                                            ) : null}
                                                            <div className="w-15 h-15 bg-gray-200 rounded-lg flex items-center justify-center" style={{display: product.images && product.images.length > 0 && (product.images[0].image_url || product.images[0].full_image_url) ? 'none' : 'flex'}}>
                                                                <span className="text-gray-400 text-xs">No Image</span>
                                                            </div>
                                                        </div>
                                                        
                                                        <div className="flex-1 min-w-0">
                                                            <h3 className="font-semibold text-black truncate">
                                                                {product.name}
                                                            </h3>
                                                            <p className="text-sm text-gray-700">
                                                                SKU: {product.sku}
                                                            </p>
                                                            <p className="text-sm text-gray-700">
                                                                Price: {product.currency_code} {product.current_price}
                                                            </p>
                                                        </div>
                                                        
                                                        <div className="flex-shrink-0 text-right">
                                                            <div className="text-sm font-semibold text-black">
                                                                Rank #{index + 1}
                                                            </div>
                                                            <div className="text-xs text-gray-600">
                                                                Drag to reorder
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            )}
                                        </Draggable>
                                    ))}
                                    {provided.placeholder}
                                </div>
                            )}
                        </Droppable>
                    </DragDropContext>
                </div>
            ) : selectedCategory ? (
                <div className="bg-white rounded-lg shadow-sm p-8 text-center">
                    <MagnifyingGlassIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <p className="text-black font-medium">No products found in this category</p>
                </div>
            ) : (
                <div className="bg-white rounded-lg shadow-sm p-8 text-center">
                    <Bars3BottomLeftIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <p className="text-black font-medium">Select a category to start managing product rankings</p>
                </div>
            )}

            {/* Current Website Order Section */}
            {selectedCategory && (
                <div className="bg-white rounded-lg shadow-sm p-6 mt-6">
                    <h2 className="text-lg font-bold text-black mb-4 flex items-center">
                        <CheckCircleIcon className="h-5 w-5 mr-2 text-green-500" />
                        Current Website Order ({sortedProducts.length} products)
                    </h2>
                    <p className="text-gray-700 mb-4">This shows how products currently appear on the website for customers</p>

                    {loadingSorted ? (
                        <div className="text-center py-8">
                            <ArrowPathIcon className="h-6 w-6 animate-spin mx-auto text-orange-500 mb-2" />
                            <p className="text-black">Loading current order...</p>
                        </div>
                    ) : sortedProducts.length > 0 ? (
                        <div className="space-y-3">
                            {sortedProducts.map((product, index) => (
                                <div
                                    key={product.id}
                                    className="p-4 border border-gray-200 rounded-lg bg-gray-50"
                                >
                                    <div className="flex items-center gap-4">
                                        <div className="flex-shrink-0">
                                            <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center font-semibold text-sm">
                                                {index + 1}
                                            </div>
                                        </div>

                                        <div className="flex-shrink-0">
                                            {product.images && product.images.length > 0 && (product.images[0].image_url || product.images[0].full_image_url) ? (
                                                <img
                                                    src={product.images[0].image_url || product.images[0].full_image_url}
                                                    alt={product.name}
                                                    width={50}
                                                    height={50}
                                                    className="rounded-lg object-cover w-12 h-12"
                                                    onError={(e) => {
                                                        e.target.style.display = 'none';
                                                        e.target.nextSibling.style.display = 'flex';
                                                    }}
                                                />
                                            ) : null}
                                            <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center" style={{display: product.images && product.images.length > 0 && (product.images[0].image_url || product.images[0].full_image_url) ? 'none' : 'flex'}}>
                                                <span className="text-gray-400 text-xs">No Image</span>
                                            </div>
                                        </div>

                                        <div className="flex-1 min-w-0">
                                            <h3 className="font-semibold text-black truncate">
                                                {product.name}
                                            </h3>
                                            <p className="text-sm text-gray-700">
                                                SKU: {product.sku}
                                            </p>
                                            <p className="text-sm text-gray-700">
                                                Price: {product.currency_code} {product.current_price}
                                            </p>
                                        </div>

                                        <div className="flex-shrink-0 text-right">
                                            <div className="text-sm font-semibold text-green-600">
                                                Website Rank #{index + 1}
                                            </div>
                                            <div className="text-xs text-gray-600">
                                                Live order
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="text-center py-8">
                            <MagnifyingGlassIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                            <p className="text-black font-medium">No products found in current website order</p>
                        </div>
                    )}
                </div>
            )}
        </section>
    );
}
