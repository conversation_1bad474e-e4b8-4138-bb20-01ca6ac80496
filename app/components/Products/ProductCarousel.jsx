import React, { useRef, useState, useMemo } from 'react'
import Link from 'next/link';
import PrimaryGradientBtn from '../Buttons/PrimaryGradientBtn';
import PublicProductCard from '../Cards/PublicProductCard';

export default function ProductCarousel({ products, category }) {
    const sliderRef = useRef(null);
    const [canScrollLeft, setCanScrollLeft] = useState(false);
    const [canScrollRight, setCanScrollRight] = useState(true);

    // Fisher-Yates shuffle algorithm for randomizing products array
    const shuffleArray = (array) => {
        if (!array || array.length === 0) return [];

        const shuffled = [...array]; // Create a copy to avoid mutating original array
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    };

    const shuffledProducts = useMemo(() => {
        console.log(`🔀 Randomizing ${products?.length || 0} products for ${category?.name || category} carousel`);
        return shuffleArray(products);
    }, [products, category]); // Re-shuffle when products or category changes

    function checkScrollability() {
        if (sliderRef.current) {
            const { scrollLeft, scrollWidth, clientWidth } = sliderRef.current;
            setCanScrollLeft(scrollLeft > 0);
            setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 10);
        }
    };

    function scroll(direction) {
        if (sliderRef.current) {
            const { scrollLeft, clientWidth } = sliderRef.current;
            const scrollTo = direction === 'left'
                ? scrollLeft - clientWidth + 100
                : scrollLeft + clientWidth - 100;

            sliderRef.current.scrollTo({
                left: scrollTo,
                behavior: 'smooth'
            });

            // Update scroll buttons after scrolling
            setTimeout(checkScrollability, 500);
        }
    };

    // Don't render if no products available
    if (!shuffledProducts || shuffledProducts.length === 0) {
        return (
            <section>
                <h2 className='text-3xl font-semibold m-4 text-orange-500'>{category?.name || category}</h2>
                <div className="flex items-center justify-center py-12">
                    <p className="text-gray-500 text-lg">No products available in this category.</p>
                </div>
            </section>
        );
    }

    return (
        <section>
            <h2 className='text-3xl font-semibold m-4 text-orange-500'>{category?.name || category}</h2>
            <div className="relative">
                <button
                    onClick={() => scroll('left')}
                    className={`absolute -left-4 md:left-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full p-3 shadow-lg hover:bg-gray-50 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-orange-400 ${canScrollLeft ? 'opacity-100' : 'opacity-0 pointer-events-none'
                        }`}
                >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                </button>

                <div
                    ref={sliderRef}
                    className="flex overflow-x-scroll pb-8 hide-scrollbar snap-mandatory snap-x scroll-smooth"
                    onScroll={checkScrollability}
                >
                    <ul className="flex gap-6 px-2 list-none">
                        {shuffledProducts.map((product) => <PublicProductCard key={product.id} product={product} viewMode={'grid'} />
                        )}

                        <Link href={`/products/categories/${category?.id || ''}`} className="bg-gradient-to-br from-yellow-300 to-orange-400 w-96 h-full text-2xl rounded-2xl font-bold flex items-center justify-center hover:from-orange-400 hover:to-yellow-300 transition-colors duration-300">View All</Link>
                    </ul>
                </div>
                <button
                    onClick={() => scroll('right')}
                    className={`absolute -right-4 md:right-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full p-3 shadow-lg hover:bg-gray-50 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 ${canScrollRight ? 'opacity-100' : 'opacity-0 pointer-events-none'
                        }`}>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                </button>
            </div>

            <p className="text-end text-orange-500 mx-5 font-semibold ">
                <PrimaryGradientBtn text={'View All'} navLink={`/products/categories/${category?.id || ''}`} />
            </p>
        </section>
    )
}
