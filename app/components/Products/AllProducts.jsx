'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import {
    FaTh,
    FaList,
    FaSort,
    FaSortUp,
    FaSortDown,
    FaFilter,
    FaTimes,
    FaSearch,
    FaShoppingCart,
    FaEye,
    FaChevronLeft,
    FaChevronRight,
    FaGrid3X3,
    FaColumns,
    FaSpinner,
    FaStar,
    FaStarHalfAlt,
    FaRegStar
} from 'react-icons/fa';
import toast from 'react-hot-toast';
import PublicProductCard from '../Cards/PublicProductCard';


// API function - replace with actual backend call
export async function getAllProducts(page = 1, perPage = 12, search = '', sortBy = 'name', sortOrder = 'asc', filters = {}) {
    const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/products/public/all/view?page=${page}&per_page=${perPage}&search=${search}&sort=${sortBy}&order=${sortOrder}&brand=${filters.brand || ''}&min_price=${filters.minPrice || ''}`);
    return await response.json();
}

export default function AllProducts() {
    const router = useRouter();
    const searchParams = useSearchParams();

    // State management
    const [products, setProducts] = useState([]);
    const [pagination, setPagination] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // UI state
    const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
    const [gridColumns, setGridColumns] = useState(3); // for grid view
    const [showFilters, setShowFilters] = useState(false);

    // Search and filter state
    const [searchQuery, setSearchQuery] = useState('');
    const [sortBy, setSortBy] = useState('name');
    const [sortOrder, setSortOrder] = useState('asc');
    const [filters, setFilters] = useState({
        brand: 'all',
        minPrice: '',
        maxPrice: ''
    });

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const [perPage, setPerPage] = useState(12);

    // Get unique brands for filters
    const brands = useMemo(() => {
        const allBrands = products.map(p => p.brand);
        return ['all', ...new Set(allBrands)];
    }, [products]);

    // Fetch products
    const fetchProducts = useCallback(async () => {
        setLoading(true);
        setError(null);

        try {
            // Validate current page before API call
            const validPage = Math.max(1, currentPage);

            const response = await getAllProducts(
                validPage,
                perPage,
                searchQuery,
                sortBy,
                sortOrder,
                filters
            );

            if (response.success && response.data && response.data.products) {
                const productsData = response.data.products;

                // Validate pagination data
                if (productsData.data && Array.isArray(productsData.data)) {
                    setProducts(productsData.data);
                    setPagination(productsData);

                    // Debug pagination
                    console.log('Pagination Debug:', {
                        currentPage: validPage,
                        lastPage: productsData.last_page,
                        total: productsData.total,
                        hasLinks: !!productsData.links,
                        linksCount: productsData.links?.length || 0
                    });

                    // If current page is greater than last page, redirect to last page
                    if (validPage > productsData.last_page && productsData.last_page > 0) {
                        setCurrentPage(productsData.last_page);
                        const params = new URLSearchParams(window.location.search);
                        params.set('page', productsData.last_page.toString());
                        window.history.replaceState({}, '', `?${params.toString()}`);
                        return;
                    }
                } else {
                    setError('Invalid product data received');
                }
            } else {
                setError('Failed to fetch products');
            }
        } catch (err) {
            setError('An error occurred while fetching products');
            console.error('Error fetching products:', err);
        } finally {
            setLoading(false);
        }
    }, [currentPage, perPage, searchQuery, sortBy, sortOrder, filters]);

    // Initial load and URL parameter handling
    useEffect(() => {
        const urlSearch = searchParams.get('search') || '';
        const urlPage = parseInt(searchParams.get('page')) || 1;
        const urlSort = searchParams.get('sort') || 'name';
        const urlOrder = searchParams.get('order') || 'asc';
        const urlBrand = searchParams.get('brand') || 'all';

        // Validate page number
        const validPage = Math.max(1, urlPage);

        // Update state only if different
        if (urlSearch !== searchQuery) setSearchQuery(urlSearch);
        if (validPage !== currentPage) setCurrentPage(validPage);
        if (urlSort !== sortBy) setSortBy(urlSort);
        if (urlOrder !== sortOrder) setSortOrder(urlOrder);
        if (urlBrand !== filters.brand) {
            setFilters(prev => ({ ...prev, brand: urlBrand }));
        }
    }, [searchParams]); // Remove fetchProducts dependency to prevent infinite loop

    // Separate effect for fetching products
    useEffect(() => {
        fetchProducts();
    }, [fetchProducts]);

    // Handle search
    const handleSearch = useCallback((query) => {
        setSearchQuery(query);
        setCurrentPage(1);
        const params = new URLSearchParams(searchParams);
        if (query) {
            params.set('search', query);
        } else {
            params.delete('search');
        }
        params.set('page', '1');
        router.push(`?${params.toString()}`);
    }, [router, searchParams]);

    // Handle sort
    const handleSort = useCallback((field) => {
        const newOrder = sortBy === field && sortOrder === 'asc' ? 'desc' : 'asc';
        setSortBy(field);
        setSortOrder(newOrder);
        setCurrentPage(1);

        const params = new URLSearchParams(searchParams);
        params.set('sort', field);
        params.set('order', newOrder);
        params.set('page', '1');
        router.push(`?${params.toString()}`);
    }, [sortBy, sortOrder, router, searchParams]);

    // Handle filter change
    const handleFilterChange = useCallback((filterType, value) => {
        setFilters(prev => ({ ...prev, [filterType]: value }));
        setCurrentPage(1);

        const params = new URLSearchParams(searchParams);
        if (value && value !== 'all') {
            params.set(filterType, value);
        } else {
            params.delete(filterType);
        }
        params.set('page', '1');
        router.push(`?${params.toString()}`);
    }, [router, searchParams]);

    // Handle page change
    const handlePageChange = useCallback((page) => {
        // Validate page number
        if (!page || page < 1) {
            page = 1;
        }

        // Don't change if it's the same page
        if (page === currentPage) {
            return;
        }

        // Check if page exists in pagination
        if (pagination && page > pagination.last_page) {
            page = pagination.last_page;
        }

        setCurrentPage(page);
        const params = new URLSearchParams(searchParams);
        params.set('page', page.toString());
        router.push(`?${params.toString()}`);
    }, [router, searchParams, currentPage, pagination]);

    // Clear all filters
    const clearFilters = useCallback(() => {
        setSearchQuery('');
        setFilters({ brand: 'all', minPrice: '', maxPrice: '' });
        setSortBy('name');
        setSortOrder('asc');
        setCurrentPage(1);
        router.push('/products');
    }, [router]);

    return (
        <div className="min-h-screen bg-gray-50 text-gray-600">
            {/* Header Section */}
            <div className="bg-white border-b border-gray-100">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <div className="text-center">
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">
                            All Products
                        </h1>
                        <p className="text-gray-500">
                            {pagination ? `${pagination.total} products available` : 'Loading products...'}
                        </p>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* Search and Controls */}
                {!loading && !error && (
                    <div className="mb-8">
                        {/* Search Bar */}
                        <div className="max-w-2xl mx-auto mb-8">
                            <div className="relative">
                                <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                                <input
                                    type="text"
                                    placeholder="Search products..."
                                    value={searchQuery}
                                    onChange={(e) => handleSearch(e.target.value)}
                                    className="w-full pl-12 pr-12 py-4 text-lg border-0 rounded-2xl bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:bg-white transition-all"
                                    aria-label="Search products"
                                />
                                {searchQuery && (
                                    <button
                                        onClick={() => handleSearch('')}
                                        className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500 transition-colors"
                                        aria-label="Clear search"
                                    >
                                        <FaTimes />
                                    </button>
                                )}
                            </div>
                        </div>

                        {/* Controls Bar */}
                        <div className="flex flex-col sm:flex-row gap-4 justify-between items-center mb-6">
                            {/* View Toggle */}
                            <div className="flex items-center bg-gray-100 rounded-xl p-1">
                                <button
                                    onClick={() => setViewMode('grid')}
                                    className={`px-4 py-2 rounded-lg transition-all ${viewMode === 'grid'
                                        ? 'bg-white text-gray-900 shadow-sm'
                                        : 'text-gray-600 hover:text-gray-900'
                                        }`}
                                    aria-label="Grid view"
                                >
                                    <FaTh className="mr-2" />
                                    Grid
                                </button>
                                <button
                                    onClick={() => setViewMode('list')}
                                    className={`px-4 py-2 rounded-lg transition-all ${viewMode === 'list'
                                        ? 'bg-white text-gray-900 shadow-sm'
                                        : 'text-gray-600 hover:text-gray-900'
                                        }`}
                                    aria-label="List view"
                                >
                                    <FaList className="mr-2" />
                                    List
                                </button>
                            </div>

                            {/* Right Controls */}
                            <div className="flex items-center gap-3">
                                {/* Grid Column Selector */}
                                {viewMode === 'grid' && (
                                    <select
                                        value={gridColumns}
                                        onChange={(e) => setGridColumns(parseInt(e.target.value))}
                                        className="px-3 py-2 border-0 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:bg-white text-sm transition-all"
                                        aria-label="Grid columns"
                                    >
                                        <option value={2}>2 Columns</option>
                                        <option value={3}>3 Columns</option>
                                        <option value={4}>4 Columns</option>
                                    </select>
                                )}

                                {/* Sort Dropdown */}
                                <select
                                    value={`${sortBy}-${sortOrder}`}
                                    onChange={(e) => {
                                        const [field, order] = e.target.value.split('-');
                                        handleSort(field);
                                        setSortOrder(order);
                                    }}
                                    className="px-3 py-2 border-0 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:bg-white text-sm transition-all"
                                >
                                    <option value="name-asc">Name A-Z</option>
                                    <option value="name-desc">Name Z-A</option>
                                    <option value="price-asc">Price Low-High</option>
                                    <option value="price-desc">Price High-Low</option>
                                    <option value="brand-asc">Brand A-Z</option>
                                    <option value="brand-desc">Brand Z-A</option>
                                </select>

                                {/* Filter Toggle */}
                                <button
                                    onClick={() => setShowFilters(!showFilters)}
                                    className={`px-4 py-2 rounded-lg font-medium transition-all flex items-center gap-2 ${showFilters
                                        ? 'bg-orange-500 text-white'
                                        : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                                        }`}
                                >
                                    <FaFilter className="text-sm" />
                                    Filters
                                </button>
                            </div>
                        </div>

                        {/* Clear Filters */}
                        {(searchQuery || filters.brand !== 'all' || filters.minPrice || filters.maxPrice || sortBy !== 'name' || sortOrder !== 'asc') && (
                            <div className="text-center mb-6">
                                <button
                                    onClick={clearFilters}
                                    className="px-6 py-2 bg-red-50 text-red-600 rounded-full font-medium hover:bg-red-100 transition-colors inline-flex items-center gap-2"
                                >
                                    <FaTimes className="text-sm" />
                                    Clear All Filters
                                </button>
                            </div>
                        )}

                        {/* Filter Panel */}
                        {showFilters && (
                            <motion.div
                                initial={{ opacity: 0, y: -20 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -20 }}
                                className="mb-8 p-6 bg-white rounded-2xl shadow-sm border border-gray-100"
                            >
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    {/* Brand Filter */}
                                    <div>
                                        <label className="block text-sm font-semibold text-gray-900 mb-3">
                                            Brand
                                        </label>
                                        <select
                                            value={filters.brand}
                                            onChange={(e) => handleFilterChange('brand', e.target.value)}
                                            className="w-full px-4 py-3 border-0 rounded-xl bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:bg-white transition-all"
                                        >
                                            {brands.map(brand => (
                                                <option key={brand} value={brand}>
                                                    {brand === 'all' ? 'All Brands' : brand}
                                                </option>
                                            ))}
                                        </select>
                                    </div>

                                    {/* Price Range */}
                                    <div>
                                        <label className="block text-sm font-semibold text-gray-900 mb-3">
                                            Min Price
                                        </label>
                                        <input
                                            type="number"
                                            placeholder="0.00"
                                            value={filters.minPrice}
                                            onChange={(e) => handleFilterChange('minPrice', e.target.value)}
                                            className="w-full px-4 py-3 border-0 rounded-xl bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:bg-white transition-all"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-semibold text-gray-900 mb-3">
                                            Max Price
                                        </label>
                                        <input
                                            type="number"
                                            placeholder="999.99"
                                            value={filters.maxPrice}
                                            onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
                                            className="w-full px-4 py-3 border-0 rounded-xl bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:bg-white transition-all"
                                        />
                                    </div>
                                </div>
                            </motion.div>
                        )}
                    </div>
                )}

                {/* Loading State */}
                {loading && (
                    <div className="flex items-center justify-center py-12">
                        <FaSpinner className="animate-spin text-orange-400 text-2xl mr-3" />
                        <span className="text-gray-600">Loading products...</span>
                    </div>
                )}

                {/* Error State */}
                {error && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                        <div className="flex items-center">
                            <FaTimes className="text-red-400 mr-2" />
                            <span className="text-red-700">{error}</span>
                        </div>
                    </div>
                )}

                {/* Products Display */}
                {!loading && !error && products.length > 0 && (
                    <>
                        {/* Grid View */}
                        {viewMode === 'grid' && (
                            <ul className={`grid gap-6 list-none mb-8 ${gridColumns === 2 ? 'grid-cols-1 sm:grid-cols-2' :
                                gridColumns === 3 ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' :
                                    'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                                }`}>
                                {products.map((product, index) =>
                                    < PublicProductCard
                                        key={product.id}
                                        product={product}
                                        index={index}
                                        viewMode={viewMode}
                                    />

                                )}
                            </ul>
                        )}

                        {/* List View */}
                        {viewMode === 'list' && (
                            <ul className="space-y-4 mb-8 list-none">
                                {products.map((product, index) => (
                                    <PublicProductCard
                                        key={product.id}
                                        product={product}
                                        index={index}
                                        viewMode={viewMode}
                                    />
                                ))}
                            </ul>
                        )}


                        {/* Pagination */}
                        {pagination && pagination.last_page > 1 && (
                            <div className="flex flex-col items-center gap-6 mt-12">
                                {/* Pagination Controls */}
                                <div className="flex items-center gap-2">
                                    {/* Previous Button */}
                                    <button
                                        onClick={() => handlePageChange(currentPage - 1)}
                                        disabled={currentPage <= 1 || !pagination.prev_page_url}
                                        className="px-4 py-2 rounded-xl bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-all"
                                    >
                                        <FaChevronLeft className="text-xs" />
                                        Previous
                                    </button>

                                    {/* Page Numbers using API links */}
                                    <div className="flex items-center gap-1 mx-4">
                                        {pagination.links && pagination.links
                                            .filter(link => {
                                                // Filter out Previous/Next and invalid labels
                                                return link.label !== '&laquo; Previous' &&
                                                    link.label !== 'Next &raquo;' &&
                                                    !isNaN(parseInt(link.label));
                                            })
                                            .map((link, index) => {
                                                const pageNum = parseInt(link.label);

                                                return (
                                                    <button
                                                        key={`page-${pageNum}-${index}`}
                                                        onClick={() => handlePageChange(pageNum)}
                                                        className={`w-10 h-10 rounded-xl text-sm font-medium transition-all ${pageNum === currentPage || link.active
                                                            ? 'bg-orange-400 text-white shadow-lg'
                                                            : 'bg-white border border-gray-200 text-gray-700 hover:bg-gray-50'
                                                            }`}
                                                        disabled={pageNum === currentPage}
                                                    >
                                                        {pageNum}
                                                    </button>
                                                );
                                            })}
                                    </div>

                                    {/* Next Button */}
                                    <button
                                        onClick={() => handlePageChange(currentPage + 1)}
                                        disabled={currentPage >= pagination.last_page || !pagination.next_page_url}
                                        className="px-4 py-2 rounded-xl bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-all"
                                    >
                                        Next
                                        <FaChevronRight className="text-xs" />
                                    </button>
                                </div>

                                {/* Pagination Info */}
                                <div className="text-sm text-gray-500">
                                    Showing {pagination.from || 1} to {pagination.to || pagination.per_page} of {pagination.total || 0} products
                                    {/* Debug info - remove in production */}
                                    <span className="ml-4 text-xs text-gray-400">
                                        (Page {currentPage} of {pagination.last_page})
                                    </span>
                                </div>
                            </div>
                        )}
                    </>
                )}

                {/* No Products Found */}
                {!loading && !error && products.length === 0 && (
                    <div className="text-center py-12">
                        <FaSearch className="text-gray-400 text-4xl mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                        <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
                    </div>
                )}
            </div>
        </div >
    );
}
