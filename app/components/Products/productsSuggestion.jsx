'use client';
import { useState } from 'react';
import { motion } from 'framer-motion';

const categories = [
    'Best Sellers',
    'Hot Products',
    'DipTouch Choice',
    'Recommended for You',
    'Time is Running Out',
    'Women',
    'Men',
    'Baby',
    'Drinks',
    'Electronics',
    'Grocery',
    'Gadgets',
];

const recommendedProducts = [
    {
        id: 'rec-1',
        name: 'Wireless Noise Cancelling Headphones',
        category: 'Recommended for You',
        image: 'https://via.placeholder.com/200?text=Headphones',
    },
    {
        id: 'rec-2',
        name: 'Smart Fitness Watch',
        category: 'Recommended for You',
        image: 'https://via.placeholder.com/200?text=Fitness+Watch',
    },
    {
        id: 'rec-3',
        name: 'Eco-Friendly Water Bottle',
        category: 'Recommended for You',
        image: 'https://via.placeholder.com/200?text=Water+Bottle',
    },
    {
        id: 'rec-4',
        name: 'Organic Skincare Set',
        category: 'Recommended for You',
        image: 'https://via.placeholder.com/200?text=Skincare+Set',
    },
    {
        id: 'rec-5',
        name: 'Portable Bluetooth Speaker',
        category: 'Recommended for You',
        image: 'https://via.placeholder.com/200?text=Bluetooth+Speaker',
    },
    {
        id: 'rec-6',
        name: 'Ergonomic Office Chair',
        category: 'Recommended for You',
        image: 'https://via.placeholder.com/200?text=Office+Chair',
    },
];

const createDummyData = (category, count = 6) => {
    return Array.from({ length: count }, (_, i) => ({
        id: `${category}-${i}`,
        name: `${category} Product ${i + 1}`,
        category,
        image: `https://via.placeholder.com/200?text=${encodeURIComponent(category + ' ' + (i + 1))}`,
    }));
};

const products = categories.flatMap((category) =>
    category === 'Recommended for You'
        ? recommendedProducts
        : createDummyData(category, 6)
);

const ProductShowcase = () => {
    const [searchTerm, setSearchTerm] = useState('');

    const filteredProducts = searchTerm
        ? products.filter((product) =>
            product.name.toLowerCase().includes(searchTerm.toLowerCase())
        )
        : products;

    const grouped = filteredProducts.reduce((acc, product) => {
        acc[product.category] = acc[product.category] || [];
        acc[product.category].push(product);
        return acc;
    }, {});

    return (
        <div className="text-orange-400 p-5">
            {Object.entries(grouped).map(([category, items]) => (
                <div key={category} className="mb-8">
                    <h2 className="text-2xl font-bold mb-2">{category}</h2>
                    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        {items.map((product) => (
                            <motion.div
                                key={product.id}
                                whileHover={{ scale: 1.05 }}
                                className="bg-white rounded-lg shadow p-2"
                            >
                                <img
                                    // src={product.image}
                                    src='/images/productListImage/drinks.png'
                                    alt={product.name}
                                    className="w-full h-32 object-cover rounded mb-2"
                                />
                                <p className="text-sm font-medium text-gray-800">
                                    {product.name}
                                </p>
                            </motion.div>
                        ))}
                    </div>
                </div>
            ))}
        </div>
    );
};

export default ProductShowcase;
