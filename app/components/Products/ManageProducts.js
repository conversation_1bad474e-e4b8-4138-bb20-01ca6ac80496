'use client';

import { useState, useEffect } from 'react';
import {
  PencilIcon, TrashIcon, EyeIcon,
  ChevronUpIcon, ChevronDownIcon,
  MagnifyingGlassIcon, AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

export default function ManagePage() {

  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState({ key: 'name', direction: 'ascending' });
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [productsPerPage, setProductsPerPage] = useState(5);
  const [loading, setLoading] = useState(false);

  const pathname = usePathname();
  const [, userType] = pathname.split('/');
  console.log(userType);


  async function fetchProducts() {
    try {
      setLoading(true);
      const url = (userType === 'super-admin' && `https://b2b.instinctfusionx.xyz/public/api/v1/superadmin/products`) ||
        (userType === 'partner-admin' && `https://b2b.instinctfusionx.xyz/public/api/v1/vendor/products`);
      console.log({ url });

      const token = (userType === 'super-admin' && localStorage.getItem('superAdminAuthToken')) ||
        (userType === 'partner-admin' && localStorage.getItem('partnerAuthToken'));

      if (!token) {
        console.error('No authentication token found');
        return;
      }

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        if (response.status === 401) {
          // Handle authentication error as per user's preference
          const errorMessage = userType === 'partner-admin'
            ? 'Your partner token is not valid please logout and login again'
            : 'Your super admin token is not valid please logout and login again';

          alert(errorMessage);

          // Clear invalid tokens
          if (userType === 'partner-admin') {
            localStorage.removeItem('partnerAuthToken');
            document.cookie = 'partnerAuthToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
          } else {
            localStorage.removeItem('superAdminAuthToken');
            document.cookie = 'superAdminAuthToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
          }
          return;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('API Response:', data);

      if (data.success) {
        if (userType === 'super-admin') {
          setProducts(data.data.products || []);
        } else if (userType === 'partner-admin') {
          // Handle the vendor products API response structure
          setProducts(data.data.products.data || []);
        }
      } else {
        console.error('API returned error:', data.message);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    fetchProducts();
  }, []);

  useEffect(() => {
    let results = [...products];
    console.log('Filtering products:', results);

    // Filter by search term
    if (searchTerm) {
      results = results.filter(product => {
        const name = product.name?.toLowerCase() || '';
        const sku = product.sku?.toLowerCase() || '';
        const searchLower = searchTerm.toLowerCase();

        // For partner-admin, also search in brand name and categories
        if (userType === 'partner-admin') {
          const brandName = product.brand?.name?.toLowerCase() || '';
          const categoryNames = product.categories?.map(cat => cat.name?.toLowerCase()).join(' ') || '';
          return name.includes(searchLower) ||
                 sku.includes(searchLower) ||
                 brandName.includes(searchLower) ||
                 categoryNames.includes(searchLower);
        }

        return name.includes(searchLower) || sku.includes(searchLower);
      });
    }

    // Filter by category (for super-admin only as partner-admin has different structure)
    if (selectedCategory && userType === 'super-admin') {
      results = results.filter(product => product.category === selectedCategory);
    }

    // Filter by status
    if (selectedStatus) {
      if (userType === 'partner-admin') {
        // Use is_active for partner-admin
        const statusMap = {
          'active': true,
          'inactive': false
        };
        results = results.filter(product => product.is_active === statusMap[selectedStatus]);
      } else {
        results = results.filter(product => product.status === selectedStatus);
      }
    }

    // Sort the products
    if (sortConfig.key) {
      results.sort((a, b) => {
        let aValue = a[sortConfig.key];
        let bValue = b[sortConfig.key];

        // Handle nested properties for partner-admin
        if (userType === 'partner-admin') {
          if (sortConfig.key === 'price') {
            aValue = parseFloat(a.current_price || 0);
            bValue = parseFloat(b.current_price || 0);
          } else if (sortConfig.key === 'stock') {
            aValue = parseInt(a.quantity || 0);
            bValue = parseInt(b.quantity || 0);
          }
        }

        if (aValue < bValue) {
          return sortConfig.direction === 'ascending' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'ascending' ? 1 : -1;
        }
        return 0;
      });
    }

    setFilteredProducts(results);
  }, [products, searchTerm, sortConfig, selectedCategory, selectedStatus, userType]);

  const handleSort = (key) => {
    let direction = 'ascending';

    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }

    setSortConfig({ key, direction });
  };

  const getSortIcon = (columnName) => {
    if (sortConfig.key !== columnName) {
      return null;
    }

    return sortConfig.direction === 'ascending'
      ? <ChevronUpIcon className="h-4 w-4 inline-block ml-1" />
      : <ChevronDownIcon className="h-4 w-4 inline-block ml-1" />;
  };

  const handleDelete = (id) => {
    if (confirm('Are you sure you want to delete this product?')) {
      setProducts(products.filter(product => product.id !== id));
    }
  };

  const handleBulkDelete = () => {
    if (selectedProducts.length === 0) {
      alert('Please select at least one product to delete.');
      return;
    }

    if (confirm(`Are you sure you want to delete ${selectedProducts.length} selected products?`)) {
      setProducts(products.filter(product => !selectedProducts.includes(product.id)));
      setSelectedProducts([]);
    }
  };

  const handleStatusChange = (id, newStatus) => {
    setProducts(products.map(product =>
      product.id === id ? { ...product, status: newStatus } : product
    ));
  };

  const toggleSelectAll = (e) => {
    if (e.target.checked) {
      const currentPageIds = getCurrentPageProducts().map(product => product.id);
      setSelectedProducts(currentPageIds);
    } else {
      setSelectedProducts([]);
    }
  };

  const toggleSelectProduct = (id) => {
    setSelectedProducts(prevSelected => {
      if (prevSelected.includes(id)) {
        return prevSelected.filter(productId => productId !== id);
      } else {
        return [...prevSelected, id];
      }
    });
  };

  // Pagination
  const indexOfLastProduct = currentPage * productsPerPage;
  const indexOfFirstProduct = indexOfLastProduct - productsPerPage;

  const getCurrentPageProducts = () => {
    return filteredProducts.slice(indexOfFirstProduct, indexOfLastProduct);
  };

  const totalPages = Math.ceil(filteredProducts.length / productsPerPage);

  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  // Get unique categories for filter
  const categories = (userType === "super-admin" && [...new Set(products.map(product => product.category))]) || [];





  return (
    <div className="p-6 bg-gray-100 text-gray-600">
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold mb-4 md:mb-0 text-gray-800">Manage Products</h1>

        <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-2">
          {
            userType === "super-admin" && <Link
              href="/super-admin/products/add"
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 flex items-center justify-center"
            >
              <span>Add New Product</span>
            </Link>
          }
          {
            userType === "partner-admin" && <div className="flex gap-2">
              <Link
                href="/partner-admin/products/add"
                className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 flex items-center justify-center"
              >
                <span>Add New Product</span>
              </Link>
              <button
                onClick={fetchProducts}
                disabled={loading}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center justify-center disabled:opacity-50"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : null}
                <span>Refresh</span>
              </button>
            </div>
          }


          {selectedProducts.length > 0 && (
            <button
              onClick={handleBulkDelete}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center justify-center"
            >
              <span>Delete Selected ({selectedProducts.length})</span>
            </button>
          )}
        </div>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden mb-6">
        <div className="p-4 border-b border-gray-200">
          <div className="flex flex-col md:flex-row md:items-center justify-between space-y-4 md:space-y-0">
            <div className="relative w-full md:w-64">
              <input
                type="text"
                placeholder="Search products..."
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full text-gray-900 bg-white"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-2.5" />
            </div>

            <button
              className="flex items-center text-gray-700 px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
              onClick={() => setShowFilters(!showFilters)}
            >
              <AdjustmentsHorizontalIcon className="h-5 w-5 mr-2" />
              Filters
            </button>
          </div>

          {showFilters && (
            <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="categoryFilter" className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  id="categoryFilter"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                >
                  <option value="">All Categories</option>
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="statusFilter" className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  id="statusFilter"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900"
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                >
                  <option value="">All Statuses</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="draft">Draft</option>
                </select>
              </div>

              <div className="flex items-end">
                <button
                  className="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                  onClick={() => {
                    setSelectedCategory('');
                    setSelectedStatus('');
                    setSearchTerm('');
                  }}
                >
                  Clear Filters
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Super Admin Table */}
        {
          userType === "super-admin" &&
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left">
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      onChange={toggleSelectAll}
                      checked={selectedProducts.length === getCurrentPageProducts().length && getCurrentPageProducts().length > 0}
                    />
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <button
                      className="flex items-center font-medium text-gray-500 uppercase tracking-wider"
                      onClick={() => handleSort('name')}
                    >
                      Product
                      {getSortIcon('name')}
                    </button>
                  </th>


                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <button
                      className="flex items-center justify-end font-medium text-gray-500 uppercase tracking-wider"
                      onClick={() => handleSort('price')}
                    >
                      $Price
                      {getSortIcon('price')}
                    </button>
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>

                  {/* <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <button
                      className="flex items-center font-medium text-gray-500 uppercase tracking-wider"
                      onClick={() => handleSort('category')}
                    >
                      Category
                      {getSortIcon('category')}
                    </button>
                  </th> */}
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <button
                      className="flex items-center justify-end font-medium text-gray-500 uppercase tracking-wider"
                      onClick={() => handleSort('stock')}
                    >
                      Stock
                      {getSortIcon('stock')}
                    </button>
                  </th>
                  {/* <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Assigned to Partner
                  </th> */}
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <button
                      className="flex items-center justify-end font-medium text-gray-500 uppercase tracking-wider"
                      onClick={() => handleSort('margin')}
                    >
                      Margin
                      {getSortIcon('margin')}
                    </button>
                  </th>

                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <button
                      className="flex items-center font-medium text-gray-500 uppercase tracking-wider"
                      onClick={() => handleSort('supplier')}
                    >
                      Supplier
                      {getSortIcon('supplier')}
                    </button>
                  </th>

                  {/* <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th> */}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {getCurrentPageProducts().length > 0 ? (
                  getCurrentPageProducts().map((product) => (
                    <tr key={product.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                          checked={selectedProducts.includes(product.id)}
                          onChange={() => toggleSelectProduct(product.id)}
                        />
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-10 w-10 flex-shrink-0 mr-3">
                            <img
                              className="h-10 w-10 rounded-md object-cover"
                              src={product.imageUrl}
                              alt={product.name}
                            />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{product.name}</div>
                            <div className="text-gray-500 text-sm">SKU: {product.sku}(ID: {product.id})</div>
                          </div>
                          {product.featured && (
                            <span className="ml-2 px-2 py-0.5 text-xs bg-indigo-100 text-indigo-800 rounded-full">
                              Featured
                            </span>
                          )}
                        </div>
                      </td>

                      {/* <td className="px-4 py-4 whitespace-nowrap">
                        <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                          {product.category}
                        </span>
                      </td> */}
                      <td className="px-4 py-4 whitespace-nowrap text-right">
                        <div className="flex flex-col">
                          <div className="font-medium text-green-600">
                            {product.currency?.symbol || '$'}{product.current_price}
                          </div>
                          <div className="text-sm text-gray-500 line-through">
                            {product.currency?.symbol || '$'}{product.base_price}
                          </div>
                          {product.discount_percentage > 0 && (
                            <div className="text-xs text-orange-600">
                              -{product.discount_percentage.toFixed(1)}% off
                            </div>
                          )}
                          <div className="text-xs text-gray-400 mt-1">
                            Margins: C:{product.default_customer_margin}% | P:{product.default_partner_margin}%
                          </div>
                        </div>
                      </td>

                      <td className="px-4 py-4 whitespace-nowrap text-right">
                        <div className="flex flex-col">
                          <span className={`font-medium ${product.quantity < 10 ? 'text-red-600' : 'text-gray-900'}`}>
                            {product.quantity}
                          </span>
                          <span className={`text-xs ${
                            product.stock_status === 'in_stock' ? 'text-green-600' :
                            product.stock_status === 'low_stock' ? 'text-yellow-600' :
                            'text-red-600'
                          }`}>
                            {product.stock_status?.replace('_', ' ') || 'Unknown'}
                          </span>
                        </div>
                      </td>

                      <td className="px-4 py-4 whitespace-nowrap">
                        <select
                          value={product.is_active ? 'active' : 'inactive'}
                          onChange={(e) => handleStatusChange(product.id, e.target.value)}
                          className={`text-sm rounded-full px-2 py-1 border ${
                            product.is_active ? 'bg-green-100 text-green-800 border-green-200' :
                            'bg-red-100 text-red-800 border-red-200'
                          }`}
                        >
                          <option value="active">Active</option>
                          <option value="inactive">Inactive</option>
                        </select>
                      </td>
                      {/* <td className="px-4 py-4 whitespace-nowrap">
                      <select
                        value={product.assignedToPartner}
                        onChange={(e) => handleStatusChange(product.id, e.target.value)}
                        className={`text-sm rounded-full px-2 py-1 border ${product.assignedToPartner === 'yes' ? 'bg-green-100 text-green-800 border-green-200' : 'bg-red-100 text-red-800 border-red-200'}`}
                      >
                        <option value="yes">Yes</option>
                        <option value="no">No</option>
                      </select>
                    </td> */}

                      <td className="px-4 py-4 whitespace-nowrap text-right font-medium text-gray-900">
                        <p>{product.default_customer_margin}%</p>
                        <p>{product.default_partner_margin}%</p>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <p>{product.vendor.name}</p>
                        <p>{product.vendor.email}</p>
                        <p>ID: {product.vendor.id}</p>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          {/* <button
                            title="View product"
                            className="text-gray-500 hover:text-gray-700"
                          >
                            <Link href={`/super-admin/products/${product.id}`}>
                              <EyeIcon className="h-5 w-5" />
                            </Link>
                          </button> */}
                          {/* <Link href={`/super-admin/products/edit/${product.id}`}>
                            <button
                              title="Edit product"
                              className="text-indigo-600 hover:text-indigo-900"
                            >
                              <PencilIcon className="h-5 w-5" />
                            </button>
                          </Link> */}
                          {/* <button
                            title="Delete product"
                            onClick={() => handleDelete(product.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <TrashIcon className="h-5 w-5" />
                          </button> */}
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="7" className="px-4 py-6 text-center text-gray-500">
                      No products found matching your criteria
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        }

        {/* Partner Admin table */}
        {
          userType === "partner-admin" && <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left">
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      onChange={toggleSelectAll}
                      checked={selectedProducts.length === getCurrentPageProducts().length && getCurrentPageProducts().length > 0}
                    />
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <button
                      className="flex items-center font-medium text-gray-500 uppercase tracking-wider"
                      onClick={() => handleSort('name')}
                    >
                      Product
                      {getSortIcon('name')}
                    </button>
                  </th>

                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <button
                      className="flex items-center justify-end font-medium text-gray-500 uppercase tracking-wider"
                      onClick={() => handleSort('price')}
                    >
                      Price & Margins
                      {getSortIcon('price')}
                    </button>
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <button
                      className="flex items-center justify-end font-medium text-gray-500 uppercase tracking-wider"
                      onClick={() => handleSort('stock')}
                    >
                      Stock
                      {getSortIcon('stock')}
                    </button>
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>

                  {/* <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <button
                      className="flex items-center font-medium text-gray-500 uppercase tracking-wider"
                      onClick={() => handleSort('category')}
                    >
                      Category
                      {getSortIcon('category')}
                    </button>
                  </th> */}
                  {/* <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <button
                      className="flex items-center justify-end font-medium text-gray-500 uppercase tracking-wider"
                      onClick={() => handleSort('stock')}
                    >
                      Stock
                      {getSortIcon('stock')}
                    </button>
                  </th> */}

                  {/* <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <button
                      className="flex items-center font-medium text-gray-500 uppercase tracking-wider"
                      onClick={() => handleSort('supplier')}
                    >
                      Supplier
                      {getSortIcon('supplier')}
                    </button>
                  </th> */}

                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td colSpan="6" className="px-4 py-8 text-center">
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
                        <span className="ml-2 text-gray-600">Loading products...</span>
                      </div>
                    </td>
                  </tr>
                ) : getCurrentPageProducts().length > 0 ? (
                  getCurrentPageProducts().map((product) => (
                    <tr key={product.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                          checked={selectedProducts.includes(product.id)}
                          onChange={() => toggleSelectProduct(product.id)}
                        />
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-12 w-12 flex-shrink-0 mr-3">
                            {product.images && product.images.length > 0 ? (
                              <img
                                className="h-12 w-12 rounded-md object-cover"
                                src={product.images[0].full_image_url || product.images[0].image_url}
                                alt={product.name}
                                onError={(e) => {
                                  e.target.src = '/placeholder-image.png'; // Fallback image
                                }}
                              />
                            ) : (
                              <div className="h-12 w-12 rounded-md bg-gray-200 flex items-center justify-center">
                                <span className="text-gray-400 text-xs">No Image</span>
                              </div>
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="font-medium text-gray-900">{product.name}</div>
                            <div className="text-gray-500 text-sm">SKU: {product.sku}</div>
                            <div className="text-gray-400 text-xs">ID: {product.id}</div>
                            {product.brand && (
                              <div className="text-blue-600 text-xs">Brand: {product.brand.name}</div>
                            )}
                          </div>
                          <div className="flex flex-col gap-1">
                            {product.is_featured && (
                              <span className="px-2 py-0.5 text-xs bg-indigo-100 text-indigo-800 rounded-full">
                                Featured
                              </span>
                            )}
                            <span className={`px-2 py-0.5 text-xs rounded-full ${
                              product.approval_status === 'approved' ? 'bg-green-100 text-green-800' :
                              product.approval_status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {product.approval_status || 'Unknown'}
                            </span>
                          </div>
                        </div>
                      </td>

                      {/* <td className="px-4 py-4 whitespace-nowrap">
                        <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                          {product.category}
                        </span>
                      </td> */}
                      <td className="px-4 py-4 whitespace-nowrap text-right">
                        <div className="flex flex-col">
                          <div className="font-medium text-green-600">
                            {product.currency?.symbol || '$'}{product.current_price}
                          </div>
                          <div className="text-sm text-gray-500 line-through">
                            {product.currency?.symbol || '$'}{product.base_price}
                          </div>
                          {product.discount_percentage > 0 && (
                            <div className="text-xs text-orange-600">
                              -{product.discount_percentage.toFixed(1)}% off
                            </div>
                          )}
                          <div className="text-xs text-gray-400 mt-1">
                            Margins: C:{product.default_customer_margin}% | P:{product.default_partner_margin}%
                          </div>
                        </div>
                      </td>

                      <td className="px-4 py-4 whitespace-nowrap text-right">
                        <div className="flex flex-col">
                          <span className={`font-medium ${product.quantity < 10 ? 'text-red-600' : 'text-gray-900'}`}>
                            {product.quantity}
                          </span>
                          <span className={`text-xs ${
                            product.stock_status === 'in_stock' ? 'text-green-600' :
                            product.stock_status === 'low_stock' ? 'text-yellow-600' :
                            'text-red-600'
                          }`}>
                            {product.stock_status?.replace('_', ' ') || 'Unknown'}
                          </span>
                        </div>
                      </td>

                      <td className="px-4 py-4 whitespace-nowrap">
                        <select
                          value={product.is_active ? 'active' : 'inactive'}
                          onChange={(e) => handleStatusChange(product.id, e.target.value)}
                          className={`text-sm rounded-full px-2 py-1 border ${
                            product.is_active ? 'bg-green-100 text-green-800 border-green-200' :
                            'bg-red-100 text-red-800 border-red-200'
                          }`}
                        >
                          <option value="active">Active</option>
                          <option value="inactive">Inactive</option>
                        </select>
                      </td>
                      {/* <td className="px-4 py-4 whitespace-nowrap">
                      <select
                        value={product.assignedToPartner}
                        onChange={(e) => handleStatusChange(product.id, e.target.value)}
                        className={`text-sm rounded-full px-2 py-1 border ${product.assignedToPartner === 'yes' ? 'bg-green-100 text-green-800 border-green-200' : 'bg-red-100 text-red-800 border-red-200'}`}
                      >
                        <option value="yes">Yes</option>
                        <option value="no">No</option>
                      </select>
                    </td> */}

                      <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          {/* <button
                            title="View product"
                            className="text-gray-500 hover:text-gray-700"
                          >
                            <Link href={`/super-admin/products/${product.id}`}>
                              <EyeIcon className="h-5 w-5" />
                            </Link>
                          </button> */}
                          <Link href={`/partner-admin/products/edit/${product.id}`}>
                            <button
                              title="Edit product"
                              className="text-indigo-600 hover:text-indigo-900"
                            >
                              <PencilIcon className="h-5 w-5" />
                            </button>
                          </Link>
                          {/* <button
                            title="Delete product"
                            onClick={() => handleDelete(product.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <TrashIcon className="h-5 w-5" />
                          </button> */}
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="6" className="px-4 py-6 text-center text-gray-500">
                      No products found matching your criteria
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        }

        {totalPages > 1 && (
          <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200 bg-gray-50">
            <div className="flex-1 flex justify-between">
              <button
                onClick={() => paginate(currentPage - 1)}
                disabled={currentPage === 1}
                className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${currentPage === 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
              >
                Previous
              </button>
              <div className="hidden md:flex">
                {[...Array(totalPages)].map((_, i) => (
                  <button
                    key={i}
                    onClick={() => paginate(i + 1)}
                    className={`relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium ${currentPage === i + 1
                      ? 'bg-indigo-600 text-white border-indigo-600'
                      : 'text-gray-700 hover:bg-gray-50'
                      } mx-1 rounded-md`}
                  >
                    {i + 1}
                  </button>
                ))}
              </div>
              <button
                onClick={() => paginate(currentPage + 1)}
                disabled={currentPage === totalPages}
                className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${currentPage === totalPages
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>

      <div className="text-sm text-gray-500 mt-4">
        Showing {filteredProducts.length > 0 ? indexOfFirstProduct + 1 : 0} to {Math.min(indexOfLastProduct, filteredProducts.length)} of {filteredProducts.length} products
      </div>
    </div>
  );
}
