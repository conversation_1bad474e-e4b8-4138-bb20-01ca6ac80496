'use client';

import { useState, useRef } from 'react';
import { CameraIcon, XCircleIcon } from '@heroicons/react/24/outline';

export default function EditProduct({ params }) {
    // get id from params and fetch product data from the server using id then set to prodcut state.

    const fileInputRef = useRef(null);
    /* const [product, setProduct] = useState({
        name: '',
        description: '',
        price: '',
        category: '',
        stock: '',
        sku: '',
        brand: '',
        featured: false,
        status: 'active',
        metaTitle: '',
        metaKeywords: '',
        metaDescription: '',
    }); */

    /* dummy data */
    const SelectedProduct = {
        name: 'Gadget 1',
        description: 'This is a sample product description.',
        category: 'electronics',
        supplierName: 'Supplier A',
        costPrice: 9.99,
        retailPrice: 19.99,
        price: 29.99,
        margin: 0,
        assignToPartner: "no",
        partnerMargin: 0,
        stock: 10,
        sku: 'GADGET-001',
        brand: 'Brand A',
        featured: false,
        status: 'active',
        metaTitle: 'Sample Product',
        metaKeywords: 'sample, product, keywords',
        metaDescription: 'This is a sample product meta description.',
        metaDescription: 'This is a sample product meta description.',
        images: ['image1.jpg', 'image2.jpg', 'image3.jpg'],
    };

    const [product, setProduct] = useState(SelectedProduct);
    const [images, setImages] = useState([]);
    const [previewImages, setPreviewImages] = useState(product.images);
    const [formErrors, setFormErrors] = useState({});

    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        setProduct(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));

        // Clear error for this field when user edits it
        if (formErrors[name]) {
            setFormErrors(prev => {
                const newErrors = { ...prev };
                delete newErrors[name];
                return newErrors;
            });
        }
    };

    const handleImageUpload = (e) => {
        const files = Array.from(e.target.files);

        // Limit to 5 images
        if (images.length + files.length > 5) {
            alert('You can upload a maximum of 5 images');
            return;
        }

        setImages(prevImages => [...prevImages, ...files]);

        // Create preview URLs
        const newPreviewImages = files.map(file => URL.createObjectURL(file));
        setPreviewImages(prevPreviewImages => [...prevPreviewImages, ...newPreviewImages]);
    };

    const removeImage = (index) => {
        // Remove image preview
        URL.revokeObjectURL(previewImages[index]); // Free memory

        setImages(prevImages => prevImages.filter((_, i) => i !== index));
        setPreviewImages(prevPreviewImages => prevPreviewImages.filter((_, i) => i !== index));
    };

    const validateForm = () => {
        const errors = {};

        if (!product.name.trim()) errors.name = 'Product name is required.';
        if (!product.description.trim()) errors.description = 'Description is required.';
        if (!product.supplierName.trim()) errors.supplierName = 'Supplier name is required.';
        if (!product.costPrice) errors.costPrice = 'Cost price is required.';
        if (!product.retailPrice) errors.retailPrice = 'Retail price is required.';
        if (isNaN(product.costPrice) || Number(product.costPrice) <= 0) errors.costPrice = 'Cost price must be a positive number.';
        if (!product.price) errors.price = 'Price is required.';
        if (isNaN(product.price) || Number(product.price) <= 0) errors.price = 'Price must be a positive number';
        if (!product.category) errors.category = 'Category is required.';
        if (!product.stock) errors.stock = 'Stock quantity is required.';
        if (isNaN(product.stock) || Number(product.stock) < 0) errors.stock = 'Stock must be a non-negative number';
        if (!product.metaTitle.trim()) errors.metaTitle = 'Meta title is required.';
        if (!product.metaKeywords.trim() || product.metaKeywords.split(',').length === 0) errors.metaKeywords = 'Meta keywords are required.';
        if (!product.metaDescription.trim()) errors.metaDescription = 'Meta description is required.';
        if (images.length === 0 && previewImages.length === 0) errors.images = 'At least one product image is required.';

        setFormErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const handleSubmit = (e) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        // Create FormData for backend submission
        const formData = new FormData();

        /* product.metaKeywords => split, map, trim, uppercase, lowercase, filter */
        product.metaKeywords = (product.metaKeywords.split(',').map(keyword => {
            const newKeyword = keyword.trim().charAt(0).toUpperCase() + keyword.slice(1).toLowerCase();

            if (!newKeyword || newKeyword.length === 0 || newKeyword === '' || newKeyword === undefined) {
                return null; // return null conditions
            } else {
                return newKeyword;
            }
        })).filter(keyword => keyword !== null/* filter out nulls */);

        // Edit all product fields
        Object.keys(product).forEach(key => {
            formData.append(key, product[key]);
        });

        // Edit all images
        images.forEach((image, index) => {
            formData.append(`image${index + 1}`, image);
        });

        console.log('Product submitted:', product);
        console.log('Images:', images);

        // Here you would typically call an API to save the product
        // fetch('/api/products', {
        //   method: 'POST',
        //   body: formData,
        // })

        alert('Product Edited successfully!');

        // Reset form
        setProduct(SelectedProduct);

        // setImages([]);
        // setPreviewImages([]);
    };

    return (
        <div className="p-6 bg-gray-100">
            <h1 className="text-2xl font-semibold mb-6 text-gray-800">Edit Product</h1>

            <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="md:col-span-2">
                        <h2 className="text-lg font-medium mb-4 text-gray-800">Basic Information</h2>
                    </div>

                    {/* Product Name */}
                    <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                            Product Name <span className="text-red-500">*</span>
                        </label>
                        <input
                            type="text"
                            id="name"
                            name="name"
                            value={product.name}
                            onChange={handleChange}
                            className={`w-full px-3 py-2 border ${formErrors.name ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white text-gray-900`}
                        />
                        {formErrors.name && <p className="mt-1 text-sm text-red-500">{formErrors.name}</p>}
                    </div>

                    <div>
                        <label htmlFor="sku" className="block text-sm font-medium text-gray-700 mb-1">
                            SKU / Product Code
                        </label>
                        <input
                            type="text"
                            id="sku"
                            name="sku"
                            value={product.sku}
                            onChange={handleChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white text-gray-900"
                        />
                    </div>

                    {/* product description */}
                    <div className="md:col-span-2">
                        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                            Description <span className="text-red-500">*</span>
                        </label>
                        <textarea
                            id="description"
                            name="description"
                            value={product.description}
                            onChange={handleChange}
                            rows="4"
                            className={`w-full px-3 py-2 border ${formErrors.description ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white text-gray-900`}
                        ></textarea>
                        {formErrors.description && <p className="mt-1 text-sm text-red-500">{formErrors.description}</p>}
                    </div>

                    <div>
                        <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                            Category <span className="text-red-500">*</span>
                        </label>
                        <select
                            id="category"
                            name="category"
                            value={product.category}
                            onChange={handleChange}
                            className={`w-full px-3 py-2 border ${formErrors.category ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white text-gray-900`}
                        >
                            <option value="">Select a category</option>
                            <option value="electronics">Electronics</option>
                            <option value="clothing">Clothing</option>
                            <option value="home">Home & Kitchen</option>
                            <option value="books">Books</option>
                            <option value="beauty">Beauty & Personal Care</option>
                            <option value="sports">Sports & Outdoors</option>
                        </select>
                        {formErrors.category && <p className="mt-1 text-sm text-red-500">{formErrors.category}</p>}
                    </div>

                    <div>
                        <label htmlFor="brand" className="block text-sm font-medium text-gray-700 mb-1">
                            Brand
                        </label>
                        <input
                            type="text"
                            id="brand"
                            name="brand"
                            value={product.brand}
                            onChange={handleChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white text-gray-900"
                        />
                    </div>

                    <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                            Supplier Name <span className="text-red-500">*</span>
                        </label>
                        <input
                            type="text"
                            id="supplierName"
                            name="supplierName"
                            value={product.supplierName}
                            onChange={handleChange}
                            className={`w-full px-3 py-2 border ${formErrors.name ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white text-gray-900`}
                        />
                        {formErrors.supplierName && <p className="mt-1 text-sm text-red-500">{formErrors.supplierName}</p>}
                    </div>

                    <div className="md:col-span-2">
                        <h2 className="text-lg font-medium mb-4 mt-4 text-gray-800">Pricing & Inventory</h2>
                    </div>

                    <div>
                        <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
                            Cost Price ($) <span className="text-red-500">*</span>
                        </label>
                        <input
                            type="number"
                            id="costPrice"
                            name="costPrice"
                            min="0"
                            step="0.01"
                            value={product.costPrice}
                            onChange={handleChange}
                            className={`w-full px-3 py-2 border ${formErrors.costPrice ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white text-gray-900`}
                        />
                        {formErrors.costPrice && <p className="mt-1 text-sm text-red-500">{formErrors.costPrice}</p>}
                    </div>

                    <div>
                        <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
                            Retail Price ($) <span className="text-red-500">*</span>
                        </label>
                        <input
                            type="number"
                            id="retailPrice"
                            name="retailPrice"
                            min="0"
                            step="0.01"
                            value={product.retailPrice}
                            onChange={handleChange}
                            className={`w-full px-3 py-2 border ${formErrors.retailPrice ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white text-gray-900`}
                        />
                        {formErrors.retailPrice && <p className="mt-1 text-sm text-red-500">{formErrors.retailPrice}</p>}
                    </div>

                    <div>
                        <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
                            Margin (%) <span className="text-red-500">*</span>
                        </label>
                        <input
                            type="number"
                            id="margin"
                            name="margin"
                            min="0"
                            step="0.01"
                            value={product.margin}
                            onChange={handleChange}
                            className={`w-full px-3 py-2 border ${formErrors.margin} ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white text-gray-900`}
                        />
                        {formErrors.margin && <p className="mt-1 text-sm text-red-500">{formErrors.margin}</p>}
                    </div>

                    <div>
                        <label htmlFor="stock" className="block text-sm font-medium text-gray-700 mb-1">
                            Stock Quantity <span className="text-red-500">*</span>
                        </label>
                        <input
                            type="number"
                            id="stock"
                            name="stock"
                            min="0"
                            value={product.stock}
                            onChange={handleChange}
                            className={`w-full px-3 py-2 border ${formErrors.stock ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white text-gray-900`}
                        />
                        {formErrors.stock && <p className="mt-1 text-sm text-red-500">{formErrors.stock}</p>}
                    </div>

                    <div>
                        <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                            Status
                        </label>
                        <select
                            id="status"
                            name="status"
                            value={product.status}
                            onChange={handleChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white text-gray-900"
                        >
                            <option value="active">Active</option>
                            <option value="draft">Draft</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>

                    <div className="flex items-center">
                        <input
                            type="checkbox"
                            id="featured"
                            name="featured"
                            checked={product.featured}
                            onChange={handleChange}
                            className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        />
                        <label htmlFor="featured" className="ml-2 block text-sm text-gray-700">
                            Featured Product
                        </label>
                    </div>

                    <div>
                        <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                            Assign to Partner
                        </label>
                        <select
                            id="assignToPartner"
                            name="assignToPartner"
                            value={product.assignToPartner}
                            onChange={handleChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white text-gray-900"
                        >
                            <option value="yes">Yes</option>
                            <option value="no">No</option>
                        </select>
                    </div>

                    {
                        product?.assignToPartner === "yes" && (
                            <div>
                                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                                    Custom merging for Partner
                                </label>
                                <input
                                    type="number"
                                    name="partnerMargin"
                                    value={product.partnerMargin}
                                    id="partnerMargin"
                                    onChange={handleChange}
                                    className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white text-gray-900'
                                />
                            </div>
                        )
                    }

                    {/* Meta Titile */}

                    <div className="md:col-span-2">
                        <h2 className="text-lg font-medium mb-4 mt-4 text-gray-800">Meta Data</h2>
                    </div>

                    <div>
                        <label htmlFor="metaTitle" className="block text-sm font-medium text-gray-700 mb-1">
                            Meta Title <span className="text-red-500">*</span>
                        </label>
                        <input
                            type="text"
                            id="metaTitle"
                            name="metaTitle"
                            placeholder='Enter a product title'
                            value={product.metaTitle}
                            onChange={handleChange}
                            className={`w-full px-3 py-2 border ${formErrors.metaTitle ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white text-gray-900`}
                        />
                        {formErrors.metaTitle && <p className="mt-1 text-sm text-red-500">{formErrors.metaTitle}</p>}
                    </div>

                    {/* Meta Keywords */}
                    <div className="md:col-span-2">
                        <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                            Meta Keywords <span className="text-red-500">*</span>
                        </label>

                        <input
                            type="text"
                            name="metaKeywords"
                            id="metaKeywords"
                            value={product.metaKeywords}
                            placeholder='Keyword1, Keyword2, Keyword3...'
                            className={`w-full px-3 py-2 border ${formErrors.metaKeywords ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white text-gray-900`}
                            onChange={handleChange} />
                    </div>

                    {/* Meta Description */}
                    <div className="md:col-span-2">
                        <label htmlFor="metaDescription" className="block text-sm font-medium text-gray-700 mb-1">
                            Meta Description <span className="text-red-500">*</span>
                        </label>
                        <textarea
                            id="metaDescription"
                            name="metaDescription"
                            value={product.metaDescription}
                            onChange={handleChange}
                            rows="4"
                            className={`w-full px-3 py-2 border ${formErrors.metaDescription ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white text-gray-900`}
                        ></textarea>
                        {formErrors.metaDescription && <p className="mt-1 text-sm text-red-500">{formErrors.metaDescription}</p>}
                    </div>

                    <div className="md:col-span-2">
                        <h2 className="text-lg font-medium mb-4 mt-4 text-gray-800">Product Images</h2>

                        <p className="text-sm text-gray-500 mb-2">Upload up to 5 images. First image will be the main image.</p>

                        {formErrors.images && <p className="mb-2 text-sm text-red-500">{formErrors.images}</p>}

                        < div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4" >
                            {/* Image preview cards */}
                            {
                                previewImages.map((src, index) => (
                                    <div key={index} className="relative bg-gray-100 rounded-lg p-1 border border-gray-300">
                                        <img
                                            src={src}
                                            alt={`Product preview ${index + 1}`}
                                            className="w-full h-32 object-contain rounded-lg"
                                        />
                                        <button
                                            type="button"
                                            onClick={() => removeImage(index)}
                                            className="absolute -top-2 -right-2 bg-white rounded-full text-red-500 hover:text-red-700"
                                        >
                                            <XCircleIcon className="h-6 w-6" />
                                        </button>
                                        {index === 0 && (
                                            <span className="absolute bottom-0 left-0 bg-indigo-600 text-white text-xs px-2 py-1 rounded-tr-lg rounded-bl-lg">
                                                Main
                                            </span>
                                        )}
                                    </div>
                                ))
                            }

                            {/* Edit image button */}
                            {
                                previewImages.length < 5 && (
                                    <button
                                        type="button"
                                        onClick={() => fileInputRef.current.click()}
                                        className="border-2 border-dashed border-gray-300 rounded-lg p-4 h-32 flex flex-col items-center justify-center text-gray-500 hover:bg-gray-50"
                                    >
                                        <CameraIcon className="h-8 w-8 mb-2" />
                                        <span>Edit Image</span>
                                    </button>
                                )
                            }

                            < input
                                type="file"
                                ref={fileInputRef}
                                onChange={handleImageUpload}
                                accept="image/*"
                                multiple
                                className="hidden"
                            />
                        </div>
                    </div>
                </div>

                <div className="flex justify-end mt-6">
                    <button
                        type="button"
                        className="bg-white text-gray-700 px-4 py-2 border border-gray-300 rounded-md shadow-sm mr-2 hover:bg-gray-50"
                    >
                        Cancel
                    </button>
                    <button
                        type="submit"
                        className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                    >
                        Edit Product
                    </button>
                </div>
            </form>
        </div>
    );
}
