'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { FiPlus, FiTrash2, FiUpload, FiX } from 'react-icons/fi';
import Swal from 'sweetalert2';

const initialProduct = {
  name: '',
  description: '',
  price: {
    pack: { numberOfProducts: '', perPackPrice: '', perPackSpecialPrice: '' },
    bulk: [],
  },
  category: '',
  stock: '',
  sku: '',
  brand: '',
  featured: false,
  status: 'active',
  metaTitle: '',
  metaKeywords: '',
  metaDescription: '',
  images: [],
};

export const postProductApi2 = async function (submittedData) {
  const res = await fetch("/api/products", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(submittedData),
  });
  const data = await res.json();
  // console.log({ data });
  return data;
};

export default function AddProduct({ categories, brands }) {
  const [product, setProduct] = useState(initialProduct);
  const [keywords, setKeywords] = useState([]);
  const [showBulk, setShowBulk] = useState(false);
  const [errors, setErrors] = useState({});
  const [submittedData, setSubmittedData] = useState(null);
  const [showManualOrSelectedBrands, setShowManualOrSelectedBrands] = useState(true);
  const [toggleStockPrice, setToggleStockPrice] = useState(false);
  const [toggleSpecialPriceOffer, setToggleSpecialPriceOffer] = useState(false);
  const [toggleBulkSpecialPriceOffer, setToggleBulkSpecialPriceOffer] = useState(false);

  const validate = () => {
    const err = {};
    const requiredFields = ['name', 'description', 'category', 'sku', 'brand'];
    requiredFields.forEach((field) => {
      if (!product[field].trim()) err[field] = 'This field is required';
    });
    if (product.stock < 0) err.stock = 'Stock must be a non-negative number';
    if (!product.price.pack.numberOfProducts || Number(product.price.pack.numberOfProducts) <= 0)
      err.numberOfProducts = 'Enter positive number of products in a pack';
    if (!product.price.pack.perPackPrice || Number(product.price.pack.perPackPrice) <= 0)
      err.perPackPrice = 'Enter price per pack';
    if (Number(product.price.pack.perPackSpecialPrice) <= 0 && toggleBulkSpecialPriceOffer)
      err.perPackSpecialPrice = 'Enter positive special price per pack';
    if (product.images.length === 0)
      err.images = 'At least one image is required';
    return err;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name === 'metaKeywords') {
      const formatted = value
        .split(',')
        .map((kw) => {
          const trimmed = kw.trim();
          return trimmed.charAt(0).toUpperCase() + trimmed.slice(1).toLowerCase();
        })
        .filter(Boolean);
      setProduct((prev) => ({ ...prev, metaKeywords: formatted.join(', ') }));
      setKeywords(formatted);
    } else {
      setProduct((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handlePackPriceChange = (e) => {
    // postive number
    if (Number(e.target.value) < 0) return;
    const { name, value } = e.target;
    setProduct((prev) => ({
      ...prev,
      price: {
        ...prev.price,
        pack: { ...prev.price.pack, [name]: value },
      },
    }));
  };

  const handleBulkChange = (index, field, value) => {
    // positive number
    if (Number(value) < 0) return;
    const newBulk = [...product.price.bulk];
    newBulk[index][field] = value;
    setProduct((prev) => ({
      ...prev,
      price: { ...prev.price, bulk: newBulk },
    }));
  };

  const addBulkRow = () => {
    setProduct((prev) => ({
      ...prev,
      price: {
        ...prev.price,
        bulk: [...prev.price.bulk, { numberOfPacks: '', perPackPrice: '', perPackSpecialPrice: '' }],
      },
    }));
  };

  const removeBulkRow = (index) => {
    const newBulk = [...product.price.bulk];
    newBulk.splice(index, 1);
    setProduct((prev) => ({
      ...prev,
      price: { ...prev.price, bulk: newBulk },
    }));
  };

  const handleImageUpload = (e) => {
    const files = Array.from(e.target.files);
    if (product.images.length + files.length > 5) {
      alert('Maximum 5 images allowed');
      return;
    }
    const previews = files.map((file) => URL.createObjectURL(file));
    setProduct((prev) => ({
      ...prev,
      images: [...prev.images, ...previews],
    }));
  };

  const removeImage = (index) => {
    const updated = [...product.images];
    updated.splice(index, 1);
    setProduct((prev) => ({ ...prev, images: updated }));
  };

  const removeKeyword = (index) => {
    const updatedKeywords = [...keywords];
    updatedKeywords.splice(index, 1);
    setKeywords(updatedKeywords);
    setProduct((prev) => ({
      ...prev,
      metaKeywords: updatedKeywords.join(', '),
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // const validationErrors = validate();
    // if (Object.keys(validationErrors).length > 0) {
    //   setErrors(validationErrors);
    //   setSubmittedData(null);
    // } else 
    {
      setErrors({});
      setSubmittedData({ ...product, keywords });
      console.log('Submitted Product:', product, submittedData);

      // requried fields for testing
      const sanitizedProductData = {
        name: "Medium Wireless asdg Headphone Set with Mic",
        sku: "WE-PREMIUM-013",
        brand_id: 1,
        category_id: 1,
        description: "High-quality noise-cancelling wireless headphones with premium sound quality and 24-hour battery life. Perfect for professional use and entertainment.",
        currency_code: "AUD",
        number_of_products: 1,
        per_pack_price: 149.99,
        per_pack_special_price: 129.99,
        customer_margin: 15,
        partner_margin: 10,
        customer_margin_type: "percentage",
        partner_margin_type: "percentage",
        add_bulk_prices: true,
        bulk_prices: [
          {
            number_of_packs: 5,
            per_pack_price: 140.99,
            per_pack_special_price: 119.99,
            customer_margin: 12,
            partner_margin: 8,
            customer_margin_type: "percentage",
            partner_margin_type: "percentage"
          },
          {
            number_of_packs: 10,
            per_pack_price: 130.99,
            per_pack_special_price: 109.99,
            customer_margin: 10,
            partner_margin: 7,
            customer_margin_type: "percentage",
            partner_margin_type: "percentage"
          },
          {
            number_of_packs: 20,
            per_pack_price: 120.99,
            per_pack_special_price: 99.99,
            customer_margin: 8,
            partner_margin: 6,
            customer_margin_type: "percentage",
            partner_margin_type: "percentage"
          }
        ],

        default_customer_margin: 15,
        default_partner_margin: 10,
        default_customer_margin_type: "percentage",
        default_partner_margin_type: "percentage",

        quantity: 100,

        meta_title: "Premium Wireless Headphones | Professional Audio",
        meta_description: "High-quality noise-cancelling wireless headphones with premium sound quality and 24-hour battery life.",
        meta_keywords: "headphones, wireless, noise-cancelling, premium audio",

        images: [
          "https://example.com/images/headphones-main.jpg",
          "https://example.com/images/headphones-angle1.jpg",
          "https://example.com/images/headphones-angle2.jpg"
        ]
      };


      // const response = await postProductApi2(sanitizedProductData);
      const postProduct = async function (sanitizedProductData) {
        const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/vendor/products`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer 40|AwpPdlMvG6WAy5fgnf8IgARVRZXKJz4AhXkKCK4w0af36062`,
          },
          body: JSON.stringify(sanitizedProductData),
        });
        const data = await response.json();
        // console.log({ data });
        return data;
      };
      const response = await postProduct(sanitizedProductData);
      console.log({ response });


      if (response.data && response.success === true && response.data.product.id) {
        setProduct(initialProduct);
        setKeywords([]);
        setErrors({});
        setSubmittedData(null);

        Swal.fire({
          position: "center",
          icon: "success",
          title: "Your product has been saved successfully.",
          showConfirmButton: false,
          timer: 2000
        });
      } else if (response.success === false && response.errors) {
        // Handle validation errors
        const errorMessages = [];

        // Extract error messages from response
        Object.entries(response.errors).forEach(([field, messages]) => {
          errorMessages.push(`${field}: ${messages.join(', ')}`);
        });

        // Show error alert with all validation messages
        Swal.fire({
          position: "center",
          icon: "error",
          title: response.message,
          text: errorMessages.join('\n'),
          showConfirmButton: true
        });
      } else {
        // Show generic error message
        Swal.fire({
          position: "center",
          icon: "error",
          title: "Something went wrong. Please try again.",
          showConfirmButton: false,
          timer: 2000
        });
      }
    }
  };

  return (
    <div className="text-gray-600">
      <form
        onSubmit={handleSubmit}
        className="max-w-4xl mx-auto space-y-6 p-4 sm:p-6 bg-white shadow-md rounded-md"
      >
        <h2 className="text-3xl font-bold text-center mb-6">Add New Product</h2>

        {/* BASIC FIELDS */}
        <div className="grid md:grid-cols-2 gap-6">
          {[
            { label: 'Product Name', name: 'name' },
            { label: 'SKU', name: 'sku' },
          ].map((field) => (
            <div key={field.name}>
              <label className="block text-sm font-medium mb-1">
                {field.label} <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name={field.name}
                value={product[field.name]}
                onChange={handleInputChange}
                className={`w-full border rounded p-2 ${errors[field.name] ? 'border-red-500' : ''
                  }`}
              />
              {errors[field.name] && (
                <p className="text-red-500 text-sm">{errors[field.name]}</p>
              )}
            </div>
          ))}

          {/* STOCK */}
          <div>
            {
              toggleStockPrice && <div>
                <label className="block text-sm font-medium mb-1">
                  Stock
                </label>
                <input
                  type="number"
                  name="stock"
                  value={product.stock}
                  onChange={handleInputChange}
                  className={`w-full border rounded p-2 ${errors.stock ? 'border-red-500' : ''
                    }`}
                />
                {errors.stock && (
                  <p className="text-red-500 text-sm">{errors.stock}</p>
                )}
              </div>
            }

            <div className='flex items-center'>
              <input
                type="checkbox"
                className="w-5 h-5"
                checked={toggleStockPrice}
                onChange={() => setToggleStockPrice(!toggleStockPrice)}
              />
              <label htmlFor="showManualOrSelectedBrands"
                className='mx-2'>Do you want to show stock price</label>
            </div>
          </div>


          {/* Brand */}
          <div>
            {showManualOrSelectedBrands === true ?
              <div>
                <label className="block text-sm font-medium mb-1">
                  Brand <span className="text-red-500">*</span>
                </label>
                <select
                  name="brand"
                  value={product.brand}
                  onChange={handleInputChange}
                  className={`w-full border rounded p-2 ${errors.brand ? 'border-red-500' : ''
                    }`}
                >
                  <option value="">Select Brand</option>
                  {brands.map((brand, i) => (
                    <option key={i} value={brand}>
                      {brand}
                    </option>
                  ))}
                </select>
                {errors.brand && (
                  <p className="text-red-500 text-sm">{errors.brand}</p>
                )}
              </div>
              : (
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Brand <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    name="brand"
                    value={product.brand}
                    onChange={handleInputChange}
                    className={`w-full border rounded p-2 ${errors.brand ? 'border-red-500' : ''
                      }`}
                  />
                  {errors.brand && (
                    <p className="text-red-500 text-sm">{errors.brand}</p>
                  )}
                </div>
              )}
            <div className="flex items-center gap-2 my-1">
              <input
                type="checkbox"
                id="manualBrandToggle"
                className="h-5 w-5 text-blue-600 rounded focus:ring-blue-500 border-gray-300"
                onChange={() =>
                  setShowManualOrSelectedBrands(!showManualOrSelectedBrands)
                }
              />
              <label htmlFor="manualBrandToggle" className="text-sm font-medium text-gray-700">
                Click here to add brand from manually or selected list?
              </label>
            </div>
          </div>


          {/* CATEGORY */}
          <div>
            <label className="block text-sm font-medium mb-1">
              Category <span className="text-red-500">*</span>
            </label>
            <select
              name="category"
              value={product.category}
              onChange={handleInputChange}
              className={`w-full border rounded p-2 ${errors.category ? 'border-red-500' : ''
                }`}
            >
              <option value="">Select Category</option>
              {/* {categories.map((cat, i) => (
                <option key={i} value={cat}>
                  {cat}
                </option>
              ))} */}
            </select>
            {errors.category && (
              <p className="text-red-500 text-sm">{errors.category}</p>
            )}
          </div>
        </div>

        {/* DESCRIPTION */}
        <div>
          <label className="block text-sm font-medium mb-1">
            Description <span className="text-red-500">*</span>
          </label>
          <textarea
            name="description"
            rows={3}
            value={product.description}
            onChange={handleInputChange}
            className={`w-full border rounded p-2 ${errors.description ? 'border-red-500' : ''
              }`}
          />
          {errors.description && (
            <p className="text-red-500 text-sm">{errors.description}</p>
          )}
        </div>

        {/* PACK PRICE */}
        <div className="border p-4 rounded bg-gray-50">
          <h3 className="text-lg font-semibold mb-4">Pack Pricing</h3>
          <div className="grid sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm mb-1">
                Number of products in one pack <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                name="numberOfProducts"
                value={product.price.pack.numberOfProducts}
                onChange={handlePackPriceChange}
                className={`w-full border rounded p-2 ${errors.numberOfProducts ? 'border-red-500' : ''
                  }`}
              />
              {errors.numberOfProducts && (
                <p className="text-red-500 text-sm">{errors.numberOfProducts}</p>
              )}
            </div>
            <div>
              <label className="block text-sm mb-1">
                Price per pack <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                name="perPackPrice"
                value={product.price.pack.perPackPrice}
                onChange={handlePackPriceChange}
                className={`w-full border rounded p-2 ${errors.perPackPrice ? 'border-red-500' : ''
                  }`}
              />
              {errors.perPackPrice && (
                <p className="text-red-500 text-sm">{errors.perPackPrice}</p>
              )}
            </div>
            <div>
              {toggleSpecialPriceOffer && <>
                <label className="block text-sm mb-1">
                  Per pack special price offer
                </label>
                <input
                  type="number"
                  name="perPackSpecialPrice"
                  value={product.price.pack.perPackSpecialPrice}
                  onChange={handlePackPriceChange}
                  className={`w-full border rounded p-2 ${errors.perPackSpecialPrice ? 'border-red-500' : ''
                    }`}
                />
              </>
              }
              <div className='flex items-center'>
                <input
                  type="checkbox"
                  name=""
                  id=""
                  className="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 my-1"
                  onChange={() => setToggleSpecialPriceOffer(!toggleSpecialPriceOffer)} />
                <span className='mx-2'>Do you want to apply special price?</span>
              </div>
              {errors.perPackSpecialPrice && (
                <p className="text-red-500 text-sm">{errors.perPackSpecialPrice}</p>
              )}
            </div>
          </div>
        </div>

        {/* BULK PRICING TOGGLE */}
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={showBulk}
            onChange={() => setShowBulk(!showBulk)}
            className='w-5 h-5 text-blue-600 focus:ring-blue-500 border-gray-300'
          />
          <label>Do you want to add bulk prices?</label>
        </div>

        {/* BULK PRICING SECTION */}
        {showBulk && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="border p-4 rounded bg-blue-50 space-y-4"
          >
            <h3 className="text-lg font-semibold">Bulk Pricing</h3>
            {product.price.bulk.map((bulkItem, index) => (
              <div
                key={index}
                className="flex flex-col sm:flex-row flex-wrap gap-4 items-center"
              >
                <input
                  type="number"
                  placeholder="No. of Packs"
                  value={bulkItem.numberOfPacks}
                  onChange={(e) =>
                    handleBulkChange(index, 'numberOfPacks', e.target.value)
                  }
                  className="border rounded p-2 w-full"
                />
                <input
                  type="number"
                  placeholder="Per Pack Price"
                  value={bulkItem.perPackPrice}
                  onChange={(e) =>
                    handleBulkChange(index, 'perPackPrice', e.target.value)
                  }
                  className="border rounded p-2 w-full"
                />
                {toggleBulkSpecialPriceOffer && <>
                  <input
                    type="number"
                    placeholder="Per Pack Special Price"
                    value={bulkItem.perPackSpecialPrice}
                    onChange={(e) =>
                      handleBulkChange(index, 'perPackSpecialPrice', e.target.value)
                    }
                    className="border rounded p-2 w-full"
                  />
                </>
                }
                {errors.perPackSpecialPrice && (
                  <p className="text-red-500 text-sm">{errors.perPackSpecialPrice}</p>
                )}
                <button
                  type="button"
                  onClick={() => removeBulkRow(index)}
                  className="text-red-600"
                >
                  <FiTrash2 />
                </button>
              </div>
            ))}

            <div className='flex items-center'>
              <input
                type="checkbox"
                name=""
                id=""
                className="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 my-1"
                onChange={() => setToggleBulkSpecialPriceOffer(!toggleBulkSpecialPriceOffer)}
              />
              <span className='mx-2'>Do you want to apply special price?</span>
            </div>

            <button
              type="button"
              onClick={addBulkRow}
              className="flex items-center gap-1 text-blue-700 font-medium"
            >
              <FiPlus /> Add Bulk Option
            </button>
          </motion.div>
        )}

        {/* META FIELDS */}
        <div className="grid sm:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium mb-1">Meta Title</label>
            <input
              type="text"
              name="metaTitle"
              value={product.metaTitle}
              onChange={handleInputChange}
              className="w-full border rounded p-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Meta Description</label>
            <textarea
              type="text"
              name="metaDescription"
              value={product.metaDescription}
              onChange={handleInputChange}
              className="w-full border rounded p-2"
            />
          </div>

          {/* META KEYWORDS */}
          <div className="sm:col-span-2">
            <label className="block text-sm font-medium mb-1">Meta Keywords</label>
            <input
              type="text"
              name="metaKeywords"
              value={product.metaKeywords}
              onChange={handleInputChange}
              className="w-full border rounded p-2"
            />
            <div className="flex flex-wrap gap-2 mt-2">
              {keywords.map((kw, i) => (
                <div
                  key={i}
                  className="flex items-center gap-1 bg-gray-200 px-2 py-1 rounded text-sm"
                >
                  {kw}
                  <button
                    type="button"
                    onClick={() => removeKeyword(i)}
                    className="text-red-500"
                  >
                    <FiX size={14} />
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* IMAGE UPLOAD */}
        <div>
          <label className="block text-sm font-medium mb-1">
            Upload Images <span className="text-red-500">*</span>
          </label>
          {
            product.images.length < 5 && <div className="flex items-center my-3">
              <FiUpload className='w-6 h-6 text-orange-400' />
              <input
                type="file"
                accept="image/*"
                multiple
                onChange={handleImageUpload}
                className="mx-2"
                placeholder='Upload images'
              />
            </div>
          }
          {errors.images && (
            <p className="text-red-500 text-sm">{errors.images}</p>
          )}
          <div className="flex flex-wrap gap-3 mt-2">
            {product.images.map((img, index) => (
              <div key={index} className="relative">
                <img
                  src={img}
                  alt={`Product ${index}`}
                  className="w-24 h-24 object-cover rounded border"
                />
                <button
                  type="button"
                  onClick={() => removeImage(index)}
                  className="absolute top-0 right-0 p-1 bg-white rounded-full shadow"
                >
                  <FiX />
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* SUBMIT */}
        <button
          type="submit"
          className="bg-orange-400 hover:bg-orange-500 text-white font-semibold px-6 py-2 rounded w-full sm:w-auto"
        >
          Submit
        </button>
      </form>

      {/* SUBMITTED DATA */}
      {submittedData && (
        <div className="mt-8 p-4 bg-green-100 rounded-md shadow-md">
          <h3 className="font-semibold text-lg">Submitted Product Data</h3>
          <pre className="text-sm">{JSON.stringify(submittedData, null, 2)}</pre>
        </div>
      )}
    </div>
  );
}
