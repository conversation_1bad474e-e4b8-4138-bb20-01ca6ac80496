'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { FaHeart, FaRegHeart } from 'react-icons/fa';
import PrimaryCartBtn from '@/app/components/Buttons/PrimaryCartBtn';
import toast from 'react-hot-toast';

// Interactive Image Gallery Component
export function InteractiveImageGallery({ product, selectedImage: initialImage }) {
    const [selectedImage, setSelectedImage] = useState(initialImage || product.image || '');

    return (
        <div className="space-y-4">
            <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                className="bg-white rounded-2xl p-6 shadow-lg"
            >
                <div className="aspect-square overflow-hidden rounded-xl bg-gray-100">
                    <Image
                        src={selectedImage || product.image || '/images/placeholder.png'}
                        alt={product.name}
                        width={600}
                        height={600}
                        className="w-full h-full object-contain hover:scale-105 transition-transform duration-300"
                        sizes="(max-width: 768px) 100vw, 600px"
                    />
                </div>
            </motion.div>

            {/* Thumbnail Images */}
            {product.images && product.images.length > 1 && (
                <div className="flex gap-3 overflow-x-auto pb-2">
                    {product.images.map((img, idx) => (
                        <button
                            key={idx}
                            onClick={() => setSelectedImage(img.url)}
                            className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-all ${
                                selectedImage === img.url 
                                    ? 'border-orange-400 ring-2 ring-orange-200' 
                                    : 'border-gray-200 hover:border-gray-300'
                            }`}
                        >
                            <Image
                                src={img.url}
                                alt={img.alt || product.name}
                                width={80}
                                height={80}
                                className="w-full h-full object-contain"
                                sizes="80px"
                            />
                        </button>
                    ))}
                </div>
            )}
        </div>
    );
}

// Interactive Wishlist and Cart Component
export function InteractiveActions({ product }) {
    const [isWishlist, setWishlist] = useState(false);

    const handleAddToWishlist = () => {
        setWishlist(!isWishlist);
        toast.success(isWishlist ? 'Removed from wishlist' : 'Added to wishlist');
    };

    return (
        <div className="space-y-4 px-5">
            <PrimaryCartBtn
                text="Add to Cart"
                cartPosition="left"
                tailwindCss="w-full"
                product={product}
            />
            <button
                onClick={handleAddToWishlist}
                className={`flex items-center justify-center w-full py-3 rounded-xl transition-all duration-200 ${
                    isWishlist
                        ? 'bg-red-50 text-red-500 border-2 border-red-300 hover:bg-red-100'
                        : 'bg-white text-gray-500 border-2 border-gray-200 hover:border-red-300 hover:text-red-500'
                }`}
            >
                {isWishlist ? (
                    <FaHeart className="text-2xl" />
                ) : (
                    <FaRegHeart className="text-2xl" />
                )}
            </button>
        </div>
    );
}

// Animated Product Information Component
export function AnimatedProductInfo({ children, delay = 0 }) {
    return (
        <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay }}
            className="space-y-5"
        >
            {children}
        </motion.div>
    );
}

// Animated Product Details Component
export function AnimatedProductDetails({ children, delay = 0.4 }) {
    return (
        <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay }}
            className="mt-16"
        >
            {children}
        </motion.div>
    );
}
