'use client';
import React, { useState } from 'react';
import { FiPlus, FiTrash2, FiPackage, FiDollarSign, FiPercent, FiBox, FiImage, FiInfo, FiTag, FiLayers } from 'react-icons/fi';
import { MdDescription, MdCategory, MdBrandingWatermark, MdOutlineInventory2, MdOutlinePriceChange } from 'react-icons/md';
import { BsCurrencyDollar, BsBoxSeam, BsPercent } from 'react-icons/bs';
import { BiPurchaseTag, BiPackage } from 'react-icons/bi';
import { AiOutlineNumber, AiOutlineFileImage, AiOutlineTag } from 'react-icons/ai';

export default function AddProduct2() {
    const [product, setProduct] = useState({
        name: '',
        sku: '',
        brand_id: '',
        category_id: '',
        description: '',
        currency_code: 'AUD',

        number_of_products: 1,
        per_pack_price: '',
        per_pack_special_price: '',
        special_price_title: '',
        customer_margin: 15,
        partner_margin: 10,
        customer_margin_type: 'percentage',
        partner_margin_type: 'percentage',

        add_bulk_prices: false,
        bulk_prices: [],

        default_customer_margin: 15,
        default_partner_margin: 10,
        default_customer_margin_type: 'percentage',
        default_partner_margin_type: 'percentage',

        quantity: 0,

        meta_title: '',
        meta_description: '',
        meta_keywords: '',

        images: []
    });

    const handleInputChange = (e) => {
        const { name, value, type, checked } = e.target;
        let finalValue = value;

        // Handle different input types
        if (type === 'checkbox') {
            finalValue = checked;
        } else if (type === 'number') {
            finalValue = value === '' ? '' : Number(value);
        } else if (type === 'file') {
            finalValue = Array.from(e.target.files);
        }

        setProduct(prev => ({
            ...prev,
            [name]: finalValue
        }));
    };

    const handleBulkPriceChange = (index, field, value) => {
        let finalValue = value;

        // Handle number fields
        if (['number_of_packs', 'per_pack_price', 'per_pack_special_price', 'customer_margin', 'partner_margin'].includes(field)) {
            finalValue = value === '' ? '' : Number(value);
        }

        setProduct(prev => {
            const updatedBulkPrices = [...prev.bulk_prices];
            updatedBulkPrices[index] = {
                ...updatedBulkPrices[index],
                [field]: finalValue
            };
            return {
                ...prev,
                bulk_prices: updatedBulkPrices
            };
        });
    };

    const handleImageUpload = (e) => {
        const files = Array.from(e.target.files);
        setProduct(prev => ({
            ...prev,
            images: files
        }));
    };

    const handleFormSubmit = (e) => {
        e.preventDefault();
        console.log('Product Data:', product);
        // just ui created, fields need to be reconsidered, and so more...
    };

    return (
        <div className="max-w-4xl mx-auto p-6 text-gray-600">
            <form className="space-y-6 bg-white shadow-md rounded-lg p-6"
                onSubmit={handleFormSubmit}>
                <h2 className="text-2xl font-bold mb-6 flex items-center gap-2 text-orange-400">
                    <FiPackage className="text-3xl" />
                    Add New Product
                </h2>

                {/* Basic Information */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold flex items-center gap-2 text-gray-700">
                        <FiInfo className="text-blue-500" />
                        Basic Information
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label className="block text-sm font-medium mb-1 flex items-center gap-1">
                                <FiTag className="text-green-500" />
                                Product Name
                            </label>
                            <input
                                type="text"
                                name="name"
                                value={product.name}
                                onChange={handleInputChange}
                                className="w-full border rounded-md p-2"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium mb-1 flex items-center gap-1">
                                <AiOutlineTag className="text-purple-500" />
                                SKU
                            </label>
                            <input
                                type="text"
                                name="sku"
                                value={product.sku}
                                onChange={handleInputChange}
                                className="w-full border rounded-md p-2"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium mb-1 flex items-center gap-1">
                                <MdBrandingWatermark className="text-orange-500" />
                                Brand ID
                            </label>
                            <input
                                type="number"
                                name="brand_id"
                                value={product.brand_id}
                                onChange={handleInputChange}
                                className="w-full border rounded-md p-2"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium mb-1 flex items-center gap-1">
                                <MdCategory className="text-indigo-500" />
                                Category ID
                            </label>
                            <input
                                type="number"
                                name="category_id"
                                value={product.category_id}
                                onChange={handleInputChange}
                                className="w-full border rounded-md p-2"
                            />
                        </div>
                    </div>
                    <div>
                        <label className="block text-sm font-medium mb-1 flex items-center gap-1">
                            <MdDescription className="text-teal-500" />
                            Description
                        </label>
                        <textarea
                            name="description"
                            value={product.description}
                            onChange={handleInputChange}
                            rows={4}
                            className="w-full border rounded-md p-2"
                        />
                    </div>
                </div>

                {/* Pricing Information */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold flex items-center gap-2 text-gray-700">
                        <FiDollarSign className="text-green-500" />
                        Pricing Information
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label className="block text-sm font-medium mb-1 flex items-center gap-1">
                                <BsCurrencyDollar className="text-emerald-500" />
                                Currency Code
                            </label>
                            <input
                                type="text"
                                name="currency_code"
                                value={product.currency_code}
                                onChange={handleInputChange}
                                className="w-full border rounded-md p-2"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium mb-1 flex items-center gap-1">
                                <BiPackage className="text-blue-500" />
                                Products per Pack
                            </label>
                            <input
                                type="number"
                                name="number_of_products"
                                value={product.number_of_products}
                                onChange={handleInputChange}
                                className="w-full border rounded-md p-2"
                                min="1"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium mb-1 flex items-center gap-1">
                                <MdOutlinePriceChange className="text-red-500" />
                                Price per Pack
                            </label>
                            <input
                                type="number"
                                name="per_pack_price"
                                value={product.per_pack_price}
                                onChange={handleInputChange}
                                className="w-full border rounded-md p-2"
                                step="0.01"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium mb-1 flex items-center gap-1">
                                <BiPurchaseTag className="text-purple-500" />
                                Special Price per Pack
                            </label>
                            <input
                                type="number"
                                name="per_pack_special_price"
                                value={product.per_pack_special_price}
                                onChange={handleInputChange}
                                className="w-full border rounded-md p-2"
                                step="0.01"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium mb-1 flex items-center gap-1">
                                <FiPercent className="text-green-500" />
                                Special Price Title
                            </label>
                            <input
                                type="text"
                                name="special_price_title"
                                value={product.special_price_title}
                                onChange={handleInputChange}
                                className="w-full border rounded-md p-2"
                            />
                        </div>
                    </div>
                </div>

                {/* Margins */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold flex items-center gap-2 text-gray-700">
                        <FiPercent className="text-yellow-500" />
                        Margin Settings
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label className="block text-sm font-medium mb-1 flex items-center gap-1">
                                <BsPercent className="text-blue-500" />
                                Customer Margin (%)
                            </label>
                            <input
                                type="number"
                                name="customer_margin"
                                value={product.customer_margin}
                                onChange={handleInputChange}
                                className="w-full border rounded-md p-2"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium mb-1 flex items-center gap-1">
                                <BsPercent className="text-green-500" />
                                Partner Margin (%)
                            </label>
                            <input
                                type="number"
                                name="partner_margin"
                                value={product.partner_margin}
                                onChange={handleInputChange}
                                className="w-full border rounded-md p-2"
                            />
                        </div>
                    </div>
                </div>

                {/* Bulk Pricing */}
                <div className="space-y-4">
                    <div className="flex items-center gap-2">
                        <input
                            type="checkbox"
                            name="add_bulk_prices"
                            checked={product.add_bulk_prices}
                            onChange={handleInputChange}
                            className="w-4 h-4"
                        />
                        <label className="text-lg font-semibold flex items-center gap-2">
                            <BsBoxSeam className="text-orange-500" />
                            Enable Bulk Pricing
                        </label>
                    </div>

                    {product.add_bulk_prices && (
                        <div className="space-y-4">
                            {product.bulk_prices.map((bulk, index) => (
                                <div key={index} className="p-4 border rounded-md bg-gray-50">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium mb-1">Number of Packs</label>
                                            <input
                                                type="number"
                                                value={bulk.number_of_packs}
                                                onChange={(e) => handleBulkPriceChange(index, 'number_of_packs', e.target.value)}
                                                className="w-full border rounded-md p-2"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium mb-1">Price per Pack</label>
                                            <input
                                                type="number"
                                                value={bulk.per_pack_price}
                                                onChange={(e) => handleBulkPriceChange(index, 'per_pack_price', e.target.value)}
                                                className="w-full border rounded-md p-2"
                                                step="0.01"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium mb-1">Special Price per Pack</label>
                                            <input
                                                type="number"
                                                value={bulk.per_pack_special_price}
                                                onChange={(e) => handleBulkPriceChange(index, 'per_pack_special_price', e.target.value)}
                                                className="w-full border rounded-md p-2"
                                                step="0.01"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium mb-1">Special Price Title</label>
                                            <input
                                                type="text"
                                                value={bulk.special_price_title}
                                                onChange={(e) => handleBulkPriceChange(index, 'special_price_title', e.target.value)}
                                                className="w-full border rounded-md p-2"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium mb-1">Customer Margin (%)</label>
                                            <input
                                                type="number"
                                                value={bulk.customer_margin}
                                                onChange={(e) => handleBulkPriceChange(index, 'customer_margin', e.target.value)}
                                                className="w-full border rounded-md p-2"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium mb-1">Partner Margin (%)</label>
                                            <input
                                                type="number"
                                                value={bulk.partner_margin}
                                                onChange={(e) => handleBulkPriceChange(index, 'partner_margin', e.target.value)}
                                                className="w-full border rounded-md p-2"
                                            />
                                        </div>
                                    </div>
                                    <button
                                        type="button"
                                        onClick={() => removeBulkPrice(index)}
                                        className="mt-4 text-red-500 flex items-center gap-2"
                                    >
                                        <FiTrash2 /> Remove Bulk Price
                                    </button>
                                </div>
                            ))}
                            {product.bulk_prices.length < 5 && (
                                <button
                                    type="button"
                                    onClick={addBulkPrice}
                                    className="flex items-center gap-2 text-blue-500"
                                >
                                    <FiPlus /> Add Bulk Price
                                </button>
                            )}
                        </div>
                    )}
                </div>

                {/* Stock */}
                <div>
                    <label className="block text-sm font-medium mb-1 flex items-center gap-1">
                        <MdOutlineInventory2 className="text-indigo-500" />
                        Quantity
                    </label>
                    <input
                        type="number"
                        name="quantity"
                        value={product.quantity}
                        onChange={handleInputChange}
                        className="w-full border rounded-md p-2"
                    />
                </div>

                {/* Meta Information */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold flex items-center gap-2 text-gray-700">
                        <FiLayers className="text-purple-500" />
                        Meta Information
                    </h3>
                    <div>
                        <label className="block text-sm font-medium mb-1 flex items-center gap-1">
                            <MdDescription className="text-teal-500" />
                            Meta Title
                        </label>
                        <input
                            type="text"
                            name="meta_title"
                            value={product.meta_title}
                            onChange={handleInputChange}
                            className="w-full border rounded-md p-2"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium mb-1 flex items-center gap-1">
                            <MdDescription className="text-teal-500" />
                            Meta Description
                        </label>
                        <textarea
                            name="meta_description"
                            value={product.meta_description}
                            onChange={handleInputChange}
                            rows={3}
                            className="w-full border rounded-md p-2"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium mb-1 flex items-center gap-1">
                            <MdDescription className="text-teal-500" />
                            Meta Keywords
                        </label>
                        <input
                            type="text"
                            name="meta_keywords"
                            value={product.meta_keywords}
                            onChange={handleInputChange}
                            className="w-full border rounded-md p-2"
                            placeholder="Separate keywords with commas"
                        />
                    </div>
                </div>

                {/* Images */}
                <div>
                    <label className="block text-sm font-medium mb-1 flex items-center gap-1">
                        <AiOutlineFileImage className="text-pink-500" />
                        Product Images
                    </label>
                    <input
                        type="file"
                        multiple
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="w-full border rounded-md p-2"
                    />
                </div>

                <button
                    type="submit"
                    className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 flex items-center justify-center gap-2"
                >
                    <FiPlus className="text-xl" />
                    Add Product
                </button>
            </form>
        </div>
    );
}
