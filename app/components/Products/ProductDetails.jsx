'use client';
import { motion } from 'framer-motion';

export default function ProductDtails({ id }) {
    /* Dummy product data. Replace with server data by id */
    const product = {
        id: 1,
        name: 'Smartphone X',
        category: 'Electronics',
        price: 999,
        stock: 50,
        sku: 'SPX00123',
        status: 'active',
        imageUrl: 'https://placehold.co/100x100/667eea/ffffff?text=PhoneX',
        featured: true,
        created: '2023-05-15',
        verify: 'approved',
    };

    // task!! get a product meta data (get)

    // task!!! retrieveing a prodcut (get)

    return (
        <section className="h-screen flex items-center justify-center">
            <motion.div
                initial={{ opacity: 0, y: 40 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, ease: 'easeOut' }}
                className=" md:bg-white w-full md:w-11/12 md:border border-orange-400 rounded-2xl md:shadow-lg max-w-3xl mx-auto p-6 md:p-8 grid grid-cols-1 md:grid-cols-3 gap-6"
            >
                {/* Image */}
                <div className="flex justify-center md:justify-start items-center">
                    <img
                        src={product.imageUrl}
                        alt={product.name}
                        className="w-28 h-28 md:w-32 md:h-32 rounded-xl object-contain border-2 border-orange-200"
                    />
                </div>

                {/* Info */}
                <div className="md:col-span-2 space-y-4">
                    <div className="flex flex-col md:flex-row md:justify-between md:items-center">
                        <div>
                            <h2 className="text-2xl font-bold text-orange-400">{product.name}</h2>
                            <p className="text-sm text-gray-500">{product.category}</p>
                        </div>
                        <span className="mt-2 md:mt-0 text-sm px-3 py-1 bg-orange-100 text-orange-500 rounded-full font-medium capitalize">
                            {product.status}
                        </span>
                    </div>

                    <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm text-gray-700">
                        <p><span className="font-semibold">Price:</span> ${product.price}</p>
                        <p><span className="font-semibold">Stock:</span> {product.stock}</p>
                        <p><span className="font-semibold">SKU:</span> {product.sku}</p>
                        <p>
                            <span className="font-semibold">Verify:</span>{' '}
                            <span className={`${product.verify === 'approved' ? 'text-green-600' : 'text-red-600'}`}>
                                {product.verify}
                            </span>
                        </p>
                        <p><span className="font-semibold">Created:</span> {product.created}</p>
                        <p>
                            <span className="font-semibold">Featured:</span>{' '}
                            {product.featured ? (
                                <span className="text-orange-500 font-medium">Yes</span>
                            ) : (
                                <span className="text-gray-500">No</span>
                            )}
                        </p>
                    </div>
                </div>
            </motion.div>

        </section>
    );
}

