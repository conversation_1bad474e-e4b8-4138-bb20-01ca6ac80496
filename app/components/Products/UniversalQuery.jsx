'use client';

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import {
    FaTh,
    <PERSON>a<PERSON>ist,
    FaSort,
    FaSortUp,
    FaSortDown,
    FaShoppingCart,
    FaHeart,
    FaEye,
    FaStar,
    FaTag,
    FaBox,
    FaStore
} from 'react-icons/fa';
import { HiSortAscending, HiSortDescending } from 'react-icons/hi';
import toast from 'react-hot-toast';
import SearchBar from '../Layout/SreachBar';

export default function UniversalQuery({ searchParams, responseQuery, hasSearchQuery }) {
    // Extract data from responseQuery prop (now passed from server-side)
    const queryProducts = responseQuery?.success ? responseQuery.data.products : { data: [], total: 0 };
    const queryCategories = responseQuery?.success ? responseQuery.data.categories : { data: [], total: 0 };
    const queryBrands = responseQuery?.success ? responseQuery.data.brands : { data: [], total: 0 };

    // If no search query, don't render anything
    if (!hasSearchQuery || !searchParams?.search) {
        return <SearchBar />;
    }

    // State Management for Products
    const [productsViewMode, setProductsViewMode] = useState('grid');
    const [productsCurrentPage, setProductsCurrentPage] = useState(1);
    const [productsItemsPerPage, setProductsItemsPerPage] = useState(15);
    const [productsSortConfig, setProductsSortConfig] = useState([
        { key: 'name', direction: 'asc' }
    ]);

    // State Management for Categories
    const [categoriesViewMode, setCategoriesViewMode] = useState('grid');
    const [categoriesCurrentPage, setCategoriesCurrentPage] = useState(1);
    const [categoriesItemsPerPage, setCategoriesItemsPerPage] = useState(15);
    const [categoriesSortConfig, setCategoriesSortConfig] = useState([
        { key: 'name', direction: 'asc' }
    ]);

    // State Management for Brands
    const [brandsViewMode, setBrandsViewMode] = useState('grid');
    const [brandsCurrentPage, setBrandsCurrentPage] = useState(1);
    const [brandsItemsPerPage, setBrandsItemsPerPage] = useState(15);
    const [brandsSortConfig, setBrandsSortConfig] = useState([
        { key: 'name', direction: 'asc' }
    ]);

    // Sorting function
    const applySorting = (data, sortConfig) => {
        return [...data].sort((a, b) => {
            for (const sort of sortConfig) {
                const aVal = a[sort.key] || '';
                const bVal = b[sort.key] || '';

                let comparison = 0;
                if (typeof aVal === 'string') {
                    comparison = aVal.localeCompare(bVal);
                } else {
                    comparison = aVal - bVal;
                }

                if (comparison !== 0) {
                    return sort.direction === 'asc' ? comparison : -comparison;
                }
            }
            return 0;
        });
    };

    // Pagination function
    const applyPagination = (data, currentPage, itemsPerPage) => {
        const startIndex = (currentPage - 1) * itemsPerPage;
        return data.slice(startIndex, startIndex + itemsPerPage);
    };

    // Processed data with sorting and pagination
    const processedProducts = useMemo(() => {
        const sorted = applySorting(queryProducts.data, productsSortConfig);
        const totalPages = Math.ceil(sorted.length / productsItemsPerPage);
        const paginated = applyPagination(sorted, productsCurrentPage, productsItemsPerPage);
        return { data: paginated, totalPages, totalItems: sorted.length };
    }, [queryProducts.data, productsSortConfig, productsCurrentPage, productsItemsPerPage]);

    const processedCategories = useMemo(() => {
        const sorted = applySorting(queryCategories.data, categoriesSortConfig);
        const totalPages = Math.ceil(sorted.length / categoriesItemsPerPage);
        const paginated = applyPagination(sorted, categoriesCurrentPage, categoriesItemsPerPage);
        return { data: paginated, totalPages, totalItems: sorted.length };
    }, [queryCategories.data, categoriesSortConfig, categoriesCurrentPage, categoriesItemsPerPage]);

    const processedBrands = useMemo(() => {
        const sorted = applySorting(queryBrands.data, brandsSortConfig);
        const totalPages = Math.ceil(sorted.length / brandsItemsPerPage);
        const paginated = applyPagination(sorted, brandsCurrentPage, brandsItemsPerPage);
        return { data: paginated, totalPages, totalItems: sorted.length };
    }, [queryBrands.data, brandsSortConfig, brandsCurrentPage, brandsItemsPerPage]);

    // Sort handlers
    const handleSort = (key, section) => {
        const setSortConfig = section === 'products' ? setProductsSortConfig :
            section === 'categories' ? setCategoriesSortConfig : setBrandsSortConfig;

        setSortConfig(prev => {
            const existing = prev.find(sort => sort.key === key);
            if (existing) {
                return prev.map(sort =>
                    sort.key === key
                        ? { ...sort, direction: sort.direction === 'asc' ? 'desc' : 'asc' }
                        : sort
                );
            } else {
                return [...prev, { key, direction: 'asc' }];
            }
        });
    };

    return (
        <div className="min-h-screen bg-gray-50">
            <SearchBar />

            {/* Header Section */}
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white shadow-sm border-b"
            >
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <div className="text-center">
                        <h1 className="text-4xl font-bold text-gray-900 mb-4">
                            Search Results for "{searchParams?.search}"
                        </h1>
                        <div className="h-1 w-24 bg-orange-400 mx-auto rounded-full mb-4"></div>
                        <p className="text-gray-600 max-w-2xl mx-auto">
                            Found {responseQuery?.data?.total || 0} total items across products, categories, and brands
                        </p>

                        {/* Stats Cards */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                            <div className="bg-gradient-to-r from-orange-400 to-orange-500 rounded-2xl p-6 text-white">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-orange-100 text-sm font-medium">Products</p>
                                        <p className="text-3xl font-bold">{queryProducts?.total || 0}</p>
                                    </div>
                                    <FaBox className="h-8 w-8 text-orange-200" />
                                </div>
                            </div>
                            <div className="bg-gradient-to-r from-blue-400 to-blue-500 rounded-2xl p-6 text-white">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-blue-100 text-sm font-medium">Categories</p>
                                        <p className="text-3xl font-bold">{queryCategories?.total || 0}</p>
                                    </div>
                                    <FaTag className="h-8 w-8 text-blue-200" />
                                </div>
                            </div>
                            <div className="bg-gradient-to-r from-green-400 to-green-500 rounded-2xl p-6 text-white">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-green-100 text-sm font-medium">Brands</p>
                                        <p className="text-3xl font-bold">{queryBrands?.total || 0}</p>
                                    </div>
                                    <FaStore className="h-8 w-8 text-green-200" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </motion.div>

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-12">

                {/* Products Section */}
                {queryProducts?.data?.length > 0 && (
                    <motion.section
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 }}
                        className="bg-white rounded-2xl shadow-lg p-6"
                    >
                        {/* Section Header */}
                        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 mb-6">
                            <div>
                                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                                    <FaBox className="text-orange-400" />
                                    Products
                                </h2>
                                <p className="text-gray-600 mt-1">
                                    Found {processedProducts.totalItems} products
                                </p>
                            </div>

                            {/* Controls */}
                            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                                {/* View Toggle */}
                                <div className="flex items-center gap-2 bg-gray-100 rounded-lg p-1">
                                    <button
                                        onClick={() => setProductsViewMode('grid')}
                                        className={`p-2 rounded-md transition-colors ${productsViewMode === 'grid'
                                            ? 'bg-orange-400 text-white'
                                            : 'text-gray-600 hover:bg-gray-200'
                                            }`}
                                        aria-label="Grid view"
                                    >
                                        <FaTh />
                                    </button>
                                    <button
                                        onClick={() => setProductsViewMode('list')}
                                        className={`p-2 rounded-md transition-colors ${productsViewMode === 'list'
                                            ? 'bg-orange-400 text-white'
                                            : 'text-gray-600 hover:bg-gray-200'
                                            }`}
                                        aria-label="List view"
                                    >
                                        <FaList />
                                    </button>
                                </div>

                                {/* Items per page */}
                                <div className="flex items-center gap-2">
                                    <label htmlFor="products-per-page" className="text-sm text-gray-600">
                                        Items:
                                    </label>
                                    <select
                                        id="products-per-page"
                                        value={productsItemsPerPage}
                                        onChange={(e) => {
                                            setProductsItemsPerPage(Number(e.target.value));
                                            setProductsCurrentPage(1);
                                        }}
                                        className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-orange-400 text-sm"
                                    >
                                        <option value={12}>12</option>
                                        <option value={15}>15</option>
                                        <option value={24}>24</option>
                                        <option value={48}>48</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        {/* Sort Controls */}
                        <div className="flex flex-wrap gap-2 mb-6">
                            <span className="text-sm text-gray-600 font-medium">Sort by:</span>
                            {['name', 'current_price', 'quantity', 'slug'].map(key => {
                                const sortItem = productsSortConfig.find(sort => sort.key === key);
                                return (
                                    <button
                                        key={key}
                                        onClick={() => handleSort(key, 'products')}
                                        className={`px-3 py-1 rounded-lg text-sm font-medium transition-all flex items-center gap-1 ${sortItem
                                            ? 'bg-orange-400 text-white'
                                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                            }`}
                                    >
                                        <span className="capitalize">{key.replace('_', ' ')}</span>
                                        {sortItem && (
                                            sortItem.direction === 'asc' ? <HiSortAscending /> : <HiSortDescending />
                                        )}
                                    </button>
                                );
                            })}
                        </div>

                        {/* Products Grid/List */}
                        <div className={`grid gap-6 mb-6 ${productsViewMode === 'grid'
                            ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                            : 'grid-cols-1'
                            }`}
                        >
                            <AnimatePresence>
                                {processedProducts.data.map((product, index) => (
                                    <motion.div
                                        key={product.id}
                                        initial={{ opacity: 0, scale: 0.9 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        exit={{ opacity: 0, scale: 0.9 }}
                                        transition={{ delay: index * 0.05 }}
                                        className={`bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 group ${productsViewMode === 'list' ? 'flex items-center p-4' : 'p-4'
                                            }`}
                                    >
                                        {productsViewMode === 'grid' ? (
                                            // Grid View
                                            <>
                                                {/* Product Image */}
                                                <div className="relative mb-4 aspect-square">
                                                    {product.images && product.images.length > 0 ? (
                                                        <Image
                                                            src={product.images[0].full_image_url || '/placeholder-product.jpg'}
                                                            alt={product.images[0].alt_text || product.name}
                                                            fill
                                                            className="object-cover rounded-lg"
                                                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                                                        />
                                                    ) : (
                                                        <div className="w-full h-full bg-gray-200 rounded-lg flex items-center justify-center">
                                                            <FaBox className="text-gray-400 text-4xl" />
                                                        </div>
                                                    )}

                                                    {/* Discount Badge */}
                                                    {product.discount_percentage > 0 && (
                                                        <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                                                            -{product.discount_percentage}%
                                                        </div>
                                                    )}
                                                </div>

                                                {/* Product Info */}
                                                <div className="space-y-2">
                                                    <h3 className="font-semibold text-gray-900 line-clamp-2 group-hover:text-orange-400 transition-colors">
                                                        {product.name}
                                                    </h3>

                                                    <p className="text-sm text-gray-600 line-clamp-2">
                                                        {product.description}
                                                    </p>

                                                    {/* Brand */}
                                                    {product.brand && (
                                                        <p className="text-xs text-gray-500">
                                                            Brand: {product.brand.name}
                                                        </p>
                                                    )}

                                                    {/* Price */}
                                                    <div className="flex items-center gap-2">
                                                        <span className="text-lg font-bold text-orange-400">
                                                            ${product.current_price}
                                                        </span>
                                                        {product.base_price !== product.current_price && (
                                                            <span className="text-sm text-gray-500 line-through">
                                                                ${product.base_price}
                                                            </span>
                                                        )}
                                                    </div>

                                                    {/* Stock Status */}
                                                    <div className="flex items-center justify-between">
                                                        <span className={`text-xs px-2 py-1 rounded-full ${product.stock_status === 'in_stock'
                                                            ? 'bg-green-100 text-green-800'
                                                            : 'bg-red-100 text-red-800'
                                                            }`}>
                                                            {product.stock_status === 'in_stock' ? 'In Stock' : 'Out of Stock'}
                                                        </span>
                                                        <span className="text-xs text-gray-500">
                                                            Qty: {product.quantity}
                                                        </span>
                                                    </div>

                                                    {/* Action Buttons */}
                                                    <div className="flex gap-2 pt-2">
                                                        <Link
                                                            href={`/products/${product.id}`}
                                                            className="flex-1 bg-orange-400 text-white px-3 py-2 rounded-lg text-sm font-medium hover:bg-orange-500 transition-colors text-center"
                                                        >
                                                            View Details
                                                        </Link>
                                                        <button
                                                            onClick={() => toast.success('Added to cart!')}
                                                            className="px-3 py-2 border border-orange-400 text-orange-400 rounded-lg hover:bg-orange-50 transition-colors"
                                                            aria-label="Add to cart"
                                                        >
                                                            <FaShoppingCart />
                                                        </button>
                                                    </div>
                                                </div>
                                            </>
                                        ) : (
                                            // List View
                                            <>
                                                {/* Product Image */}
                                                <div className="relative w-20 h-20 flex-shrink-0 mr-4">
                                                    {product.images && product.images.length > 0 ? (
                                                        <Image
                                                            src={product.images[0].full_image_url || '/placeholder-product.jpg'}
                                                            alt={product.images[0].alt_text || product.name}
                                                            fill
                                                            className="object-cover rounded-lg"
                                                            sizes="80px"
                                                        />
                                                    ) : (
                                                        <div className="w-full h-full bg-gray-200 rounded-lg flex items-center justify-center">
                                                            <FaBox className="text-gray-400 text-xl" />
                                                        </div>
                                                    )}
                                                </div>

                                                {/* Product Info */}
                                                <div className="flex-1 min-w-0">
                                                    <div className="flex justify-between items-start">
                                                        <div className="flex-1 min-w-0 mr-4">
                                                            <h3 className="font-semibold text-gray-900 truncate group-hover:text-orange-400 transition-colors">
                                                                {product.name}
                                                            </h3>
                                                            <p className="text-sm text-gray-600 line-clamp-1 mt-1">
                                                                {product.description}
                                                            </p>
                                                            {product.brand && (
                                                                <p className="text-xs text-gray-500 mt-1">
                                                                    Brand: {product.brand.name}
                                                                </p>
                                                            )}
                                                        </div>

                                                        <div className="text-right">
                                                            <div className="flex items-center gap-2">
                                                                <span className="text-lg font-bold text-orange-400">
                                                                    ${product.current_price}
                                                                </span>
                                                                {product.base_price !== product.current_price && (
                                                                    <span className="text-sm text-gray-500 line-through">
                                                                        ${product.base_price}
                                                                    </span>
                                                                )}
                                                            </div>
                                                            <div className="flex items-center gap-2 mt-2">
                                                                <Link
                                                                    href={`/products/${product.id}`}
                                                                    className="bg-orange-400 text-white px-3 py-1 rounded text-sm hover:bg-orange-500 transition-colors"
                                                                >
                                                                    View
                                                                </Link>
                                                                <button
                                                                    onClick={() => toast.success('Added to cart!')}
                                                                    className="p-1 border border-orange-400 text-orange-400 rounded hover:bg-orange-50 transition-colors"
                                                                    aria-label="Add to cart"
                                                                >
                                                                    <FaShoppingCart className="text-sm" />
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </>
                                        )}
                                    </motion.div>

                                ))}
                            </AnimatePresence>
                        </div>

                        {/* Products Pagination */}
                        {processedProducts.totalPages > 1 && (
                            <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-6 border-t border-gray-200">
                                <div className="text-gray-600 text-sm">
                                    Page {productsCurrentPage} of {processedProducts.totalPages} • Total: {processedProducts.totalItems} products
                                </div>
                                <div className="flex gap-2">
                                    <button
                                        onClick={() => setProductsCurrentPage(prev => Math.max(prev - 1, 1))}
                                        disabled={productsCurrentPage === 1}
                                        className="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                    >
                                        Previous
                                    </button>

                                    {/* Page Numbers */}
                                    {Array.from({ length: Math.min(5, processedProducts.totalPages) }, (_, i) => {
                                        const pageNum = Math.max(1, Math.min(processedProducts.totalPages - 4, productsCurrentPage - 2)) + i;
                                        if (pageNum <= processedProducts.totalPages) {
                                            return (
                                                <button
                                                    key={pageNum}
                                                    onClick={() => setProductsCurrentPage(pageNum)}
                                                    className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${productsCurrentPage === pageNum
                                                        ? 'bg-orange-400 text-white'
                                                        : 'border border-gray-300 text-gray-700 hover:bg-gray-50'
                                                        }`}
                                                >
                                                    {pageNum}
                                                </button>
                                            );
                                        }
                                        return null;
                                    })}

                                    <button
                                        onClick={() => setProductsCurrentPage(prev => Math.min(prev + 1, processedProducts.totalPages))}
                                        disabled={productsCurrentPage === processedProducts.totalPages}
                                        className="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                    >
                                        Next
                                    </button>
                                </div>
                            </div>
                        )}
                    </motion.section>
                )}

                {/* Categories Section */}
                {queryCategories?.data?.length > 0 && (
                    <motion.section
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                        className="bg-white rounded-2xl shadow-lg p-6"
                    >
                        {/* Section Header */}
                        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 mb-6">
                            <div>
                                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                                    <FaTag className="text-blue-400" />
                                    Categories
                                </h2>
                                <p className="text-gray-600 mt-1">
                                    Found {processedCategories.totalItems} categories
                                </p>
                            </div>

                            {/* Controls */}
                            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                                {/* View Toggle */}
                                <div className="flex items-center gap-2 bg-gray-100 rounded-lg p-1">
                                    <button
                                        onClick={() => setCategoriesViewMode('grid')}
                                        className={`p-2 rounded-md transition-colors ${categoriesViewMode === 'grid'
                                            ? 'bg-blue-400 text-white'
                                            : 'text-gray-600 hover:bg-gray-200'
                                            }`}
                                        aria-label="Grid view"
                                    >
                                        <FaTh />
                                    </button>
                                    <button
                                        onClick={() => setCategoriesViewMode('list')}
                                        className={`p-2 rounded-md transition-colors ${categoriesViewMode === 'list'
                                            ? 'bg-blue-400 text-white'
                                            : 'text-gray-600 hover:bg-gray-200'
                                            }`}
                                        aria-label="List view"
                                    >
                                        <FaList />
                                    </button>
                                </div>

                                {/* Items per page */}
                                <div className="flex items-center gap-2">
                                    <label htmlFor="categories-per-page" className="text-sm text-gray-600">
                                        Items:
                                    </label>
                                    <select
                                        id="categories-per-page"
                                        value={categoriesItemsPerPage}
                                        onChange={(e) => {
                                            setCategoriesItemsPerPage(Number(e.target.value));
                                            setCategoriesCurrentPage(1);
                                        }}
                                        className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 text-sm"
                                    >
                                        <option value={12}>12</option>
                                        <option value={15}>15</option>
                                        <option value={24}>24</option>
                                        <option value={48}>48</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        {/* Sort Controls */}
                        <div className="flex flex-wrap gap-2 mb-6">
                            <span className="text-sm text-gray-600 font-medium">Sort by:</span>
                            {['name', 'product_count', 'slug'].map(key => {
                                const sortItem = categoriesSortConfig.find(sort => sort.key === key);
                                return (
                                    <button
                                        key={key}
                                        onClick={() => handleSort(key, 'categories')}
                                        className={`px-3 py-1 rounded-lg text-sm font-medium transition-all flex items-center gap-1 ${sortItem
                                            ? 'bg-blue-400 text-white'
                                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                            }`}
                                    >
                                        <span className="capitalize">{key.replace('_', ' ')}</span>
                                        {sortItem && (
                                            sortItem.direction === 'asc' ? <HiSortAscending /> : <HiSortDescending />
                                        )}
                                    </button>
                                );
                            })}
                        </div>

                        {/* Categories Grid/List */}
                        <div className={`grid gap-6 mb-6 ${categoriesViewMode === 'grid'
                            ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                            : 'grid-cols-1'
                            }`}
                        >
                            <AnimatePresence>
                                {processedCategories.data.map((category, index) => (
                                    <motion.div
                                        key={category.id}
                                        initial={{ opacity: 0, scale: 0.9 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        exit={{ opacity: 0, scale: 0.9 }}
                                        transition={{ delay: index * 0.05 }}
                                        className={`bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 group ${categoriesViewMode === 'list' ? 'flex items-center p-4' : 'p-4'
                                            }`}
                                    >
                                        {categoriesViewMode === 'grid' ? (
                                            // Grid View
                                            <>
                                                {/* Category Image */}
                                                <div className="relative mb-4 aspect-square">
                                                    {category.image ? (
                                                        <Image
                                                            src={`https://b2b.instinctfusionx.xyz/public${category.image}`}
                                                            alt={category.name}
                                                            fill
                                                            className="object-cover rounded-lg"
                                                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                                                        />
                                                    ) : (
                                                        <div className="w-full h-full bg-gray-200 rounded-lg flex items-center justify-center">
                                                            <FaTag className="text-gray-400 text-4xl" />
                                                        </div>
                                                    )}
                                                </div>

                                                {/* Category Info */}
                                                <div className="space-y-2">
                                                    <h3 className="font-semibold text-gray-900 line-clamp-2 group-hover:text-blue-400 transition-colors">
                                                        {category.name}
                                                    </h3>

                                                    {category.description && (
                                                        <p className="text-sm text-gray-600 line-clamp-2">
                                                            {category.description}
                                                        </p>
                                                    )}

                                                    {/* Product Count */}
                                                    <div className="flex items-center justify-between">
                                                        <span className="text-sm text-gray-500">
                                                            {category.product_count} products
                                                        </span>
                                                        {category.is_featured && (
                                                            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                                                                Featured
                                                            </span>
                                                        )}
                                                    </div>

                                                    {/* Action Button */}
                                                    <Link
                                                        href={`/categories/${category.id}`}
                                                        className="block w-full bg-blue-400 text-white px-3 py-2 rounded-lg text-sm font-medium hover:bg-blue-500 transition-colors text-center mt-3"
                                                    >
                                                        View Category
                                                    </Link>

                                                    <Link
                                                        href={`/products/categories/${category.id}`}
                                                        className="block w-full bg-orange-400 text-white px-3 py-2 rounded-lg text-sm font-medium hover:bg-orange-500 transition-colors text-center mt-3"
                                                    >
                                                        View Products
                                                    </Link>
                                                </div>
                                            </>
                                        ) : (
                                            // List View
                                            <>
                                                {/* Category Image */}
                                                <div className="relative w-16 h-16 flex-shrink-0 mr-4">
                                                    {category.image ? (
                                                        <Image
                                                            src={`https://b2b.instinctfusionx.xyz/public${category.image}`}
                                                            alt={category.name}
                                                            fill
                                                            className="object-cover rounded-lg"
                                                            sizes="64px"
                                                        />
                                                    ) : (
                                                        <div className="w-full h-full bg-gray-200 rounded-lg flex items-center justify-center">
                                                            <FaTag className="text-gray-400 text-xl" />
                                                        </div>
                                                    )}
                                                </div>

                                                {/* Category Info */}
                                                <div className="flex-1 min-w-0">
                                                    <div className="flex justify-between items-start">
                                                        <div className="flex-1 min-w-0 mr-4">
                                                            <h3 className="font-semibold text-gray-900 truncate group-hover:text-blue-400 transition-colors">
                                                                {category.name}
                                                            </h3>
                                                            {category.description && (
                                                                <p className="text-sm text-gray-600 line-clamp-1 mt-1">
                                                                    {category.description}
                                                                </p>
                                                            )}
                                                            <p className="text-xs text-gray-500 mt-1">
                                                                {category.product_count} products
                                                            </p>
                                                        </div>

                                                        <div className="text-right">
                                                            {category.is_featured && (
                                                                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full mb-2 inline-block">
                                                                    Featured
                                                                </span>
                                                            )}
                                                            <Link
                                                                href={`/categories/${category.slug}`}
                                                                className="block bg-blue-400 text-white px-3 py-1 rounded text-sm hover:bg-blue-500 transition-colors"
                                                            >
                                                                View
                                                            </Link>
                                                        </div>
                                                    </div>
                                                </div>
                                            </>
                                        )}
                                    </motion.div>
                                ))}
                            </AnimatePresence>
                        </div>

                        {/* Categories Pagination */}
                        {processedCategories.totalPages > 1 && (
                            <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-6 border-t border-gray-200">
                                <div className="text-gray-600 text-sm">
                                    Page {categoriesCurrentPage} of {processedCategories.totalPages} • Total: {processedCategories.totalItems} categories
                                </div>
                                <div className="flex gap-2">
                                    <button
                                        onClick={() => setCategoriesCurrentPage(prev => Math.max(prev - 1, 1))}
                                        disabled={categoriesCurrentPage === 1}
                                        className="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                    >
                                        Previous
                                    </button>

                                    {/* Page Numbers */}
                                    {Array.from({ length: Math.min(5, processedCategories.totalPages) }, (_, i) => {
                                        const pageNum = Math.max(1, Math.min(processedCategories.totalPages - 4, categoriesCurrentPage - 2)) + i;
                                        if (pageNum <= processedCategories.totalPages) {
                                            return (
                                                <button
                                                    key={pageNum}
                                                    onClick={() => setCategoriesCurrentPage(pageNum)}
                                                    className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${categoriesCurrentPage === pageNum
                                                        ? 'bg-blue-400 text-white'
                                                        : 'border border-gray-300 text-gray-700 hover:bg-gray-50'
                                                        }`}
                                                >
                                                    {pageNum}
                                                </button>
                                            );
                                        }
                                        return null;
                                    })}

                                    <button
                                        onClick={() => setCategoriesCurrentPage(prev => Math.min(prev + 1, processedCategories.totalPages))}
                                        disabled={categoriesCurrentPage === processedCategories.totalPages}
                                        className="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                    >
                                        Next
                                    </button>
                                </div>
                            </div>
                        )}
                    </motion.section>
                )}

                {/* Brands Section */}
                {queryBrands?.data?.length > 0 && (
                    <motion.section
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                        className="bg-white rounded-2xl shadow-lg p-6"
                    >
                        {/* Section Header */}
                        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 mb-6">
                            <div>
                                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                                    <FaStore className="text-green-400" />
                                    Brands
                                </h2>
                                <p className="text-gray-600 mt-1">
                                    Found {processedBrands.totalItems} brands
                                </p>
                            </div>

                            {/* Controls */}
                            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                                {/* View Toggle */}
                                <div className="flex items-center gap-2 bg-gray-100 rounded-lg p-1">
                                    <button
                                        onClick={() => setBrandsViewMode('grid')}
                                        className={`p-2 rounded-md transition-colors ${brandsViewMode === 'grid'
                                            ? 'bg-green-400 text-white'
                                            : 'text-gray-600 hover:bg-gray-200'
                                            }`}
                                        aria-label="Grid view"
                                    >
                                        <FaTh />
                                    </button>
                                    <button
                                        onClick={() => setBrandsViewMode('list')}
                                        className={`p-2 rounded-md transition-colors ${brandsViewMode === 'list'
                                            ? 'bg-green-400 text-white'
                                            : 'text-gray-600 hover:bg-gray-200'
                                            }`}
                                        aria-label="List view"
                                    >
                                        <FaList />
                                    </button>
                                </div>

                                {/* Items per page */}
                                <div className="flex items-center gap-2">
                                    <label htmlFor="brands-per-page" className="text-sm text-gray-600">
                                        Items:
                                    </label>
                                    <select
                                        id="brands-per-page"
                                        value={brandsItemsPerPage}
                                        onChange={(e) => {
                                            setBrandsItemsPerPage(Number(e.target.value));
                                            setBrandsCurrentPage(1);
                                        }}
                                        className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-green-400 text-sm"
                                    >
                                        <option value={12}>12</option>
                                        <option value={15}>15</option>
                                        <option value={24}>24</option>
                                        <option value={48}>48</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        {/* Sort Controls */}
                        <div className="flex flex-wrap gap-2 mb-6">
                            <span className="text-sm text-gray-600 font-medium">Sort by:</span>
                            {['name', 'product_count', 'slug'].map(key => {
                                const sortItem = brandsSortConfig.find(sort => sort.key === key);
                                return (
                                    <button
                                        key={key}
                                        onClick={() => handleSort(key, 'brands')}
                                        className={`px-3 py-1 rounded-lg text-sm font-medium transition-all flex items-center gap-1 ${sortItem
                                            ? 'bg-green-400 text-white'
                                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                            }`}
                                    >
                                        <span className="capitalize">{key.replace('_', ' ')}</span>
                                        {sortItem && (
                                            sortItem.direction === 'asc' ? <HiSortAscending /> : <HiSortDescending />
                                        )}
                                    </button>
                                );
                            })}
                        </div>

                        {/* Brands Grid/List */}
                        <div className={`grid gap-6 mb-6 ${brandsViewMode === 'grid'
                            ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                            : 'grid-cols-1'
                            }`}
                        >
                            <AnimatePresence>
                                {processedBrands.data.map((brand, index) => (
                                    <motion.div
                                        key={brand.id}
                                        initial={{ opacity: 0, scale: 0.9 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        exit={{ opacity: 0, scale: 0.9 }}
                                        transition={{ delay: index * 0.05 }}
                                        className={`bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 group ${brandsViewMode === 'list' ? 'flex items-center p-4' : 'p-4'
                                            }`}
                                        onClick={() => toast('Feature coming soon!', { icon: '👏' })}
                                    >
                                        {brandsViewMode === 'grid' ? (
                                            // Grid View
                                            <>
                                                {/* Brand Logo */}
                                                <div className="relative mb-4 aspect-square">
                                                    {brand.logo ? (
                                                        <Image
                                                            src={`https://b2b.instinctfusionx.xyz/public${brand.logo}`}
                                                            alt={brand.name}
                                                            fill
                                                            className="object-contain rounded-lg p-4"
                                                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                                                        />
                                                    ) : (
                                                        <div className="w-full h-full bg-gray-200 rounded-lg flex items-center justify-center">
                                                            <FaStore className="text-gray-400 text-4xl" />
                                                        </div>
                                                    )}
                                                </div>

                                                {/* Brand Info */}
                                                <div className="space-y-2">
                                                    <h3 className="font-semibold text-gray-900 line-clamp-2 group-hover:text-green-400 transition-colors">
                                                        {brand.name}
                                                    </h3>

                                                    {brand.description && (
                                                        <p className="text-sm text-gray-600 line-clamp-2">
                                                            {brand.description}
                                                        </p>
                                                    )}

                                                    {/* Product Count & Website */}
                                                    <div className="space-y-1">
                                                        <div className="flex items-center justify-between">
                                                            <span className="text-sm text-gray-500">
                                                                {brand.product_count} products
                                                            </span>
                                                            {brand.is_featured && (
                                                                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                                                                    Featured
                                                                </span>
                                                            )}
                                                        </div>
                                                        {brand.website && (
                                                            <p className="text-xs text-blue-500 truncate">
                                                                {brand.website}
                                                            </p>
                                                        )}
                                                    </div>

                                                    {/* Action Button */}
                                                    <button className="w-full bg-green-400 text-white px-3 py-2 rounded-lg text-sm font-medium hover:bg-green-500 transition-colors mt-3">
                                                        View Brand
                                                    </button>
                                                </div>
                                            </>
                                        ) : (
                                            // List View
                                            <>
                                                {/* Brand Logo */}
                                                <div className="relative w-16 h-16 flex-shrink-0 mr-4">
                                                    {brand.logo ? (
                                                        <Image
                                                            src={`https://b2b.instinctfusionx.xyz/public${brand.logo}`}
                                                            alt={brand.name}
                                                            fill
                                                            className="object-contain rounded-lg p-2"
                                                            sizes="64px"
                                                        />
                                                    ) : (
                                                        <div className="w-full h-full bg-gray-200 rounded-lg flex items-center justify-center">
                                                            <FaStore className="text-gray-400 text-xl" />
                                                        </div>
                                                    )}
                                                </div>

                                                {/* Brand Info */}
                                                <div className="flex-1 min-w-0">
                                                    <div className="flex justify-between items-start">
                                                        <div className="flex-1 min-w-0 mr-4">
                                                            <h3 className="font-semibold text-gray-900 truncate group-hover:text-green-400 transition-colors">
                                                                {brand.name}
                                                            </h3>
                                                            {brand.description && (
                                                                <p className="text-sm text-gray-600 line-clamp-1 mt-1">
                                                                    {brand.description}
                                                                </p>
                                                            )}
                                                            <div className="flex items-center gap-4 mt-1">
                                                                <p className="text-xs text-gray-500">
                                                                    {brand.product_count} products
                                                                </p>
                                                                {brand.website && (
                                                                    <p className="text-xs text-blue-500 truncate">
                                                                        {brand.website}
                                                                    </p>
                                                                )}
                                                            </div>
                                                        </div>

                                                        <div className="text-right">
                                                            {brand.is_featured && (
                                                                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full mb-2 inline-block">
                                                                    Featured
                                                                </span>
                                                            )}
                                                            <button className="block bg-green-400 text-white px-3 py-1 rounded text-sm hover:bg-green-500 transition-colors">
                                                                View
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </>
                                        )}
                                    </motion.div>
                                ))}
                            </AnimatePresence>
                        </div>

                        {/* Brands Pagination */}
                        {processedBrands.totalPages > 1 && (
                            <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-6 border-t border-gray-200">
                                <div className="text-gray-600 text-sm">
                                    Page {brandsCurrentPage} of {processedBrands.totalPages} • Total: {processedBrands.totalItems} brands
                                </div>
                                <div className="flex gap-2">
                                    <button
                                        onClick={() => setBrandsCurrentPage(prev => Math.max(prev - 1, 1))}
                                        disabled={brandsCurrentPage === 1}
                                        className="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                    >
                                        Previous
                                    </button>

                                    {/* Page Numbers */}
                                    {Array.from({ length: Math.min(5, processedBrands.totalPages) }, (_, i) => {
                                        const pageNum = Math.max(1, Math.min(processedBrands.totalPages - 4, brandsCurrentPage - 2)) + i;
                                        if (pageNum <= processedBrands.totalPages) {
                                            return (
                                                <button
                                                    key={pageNum}
                                                    onClick={() => setBrandsCurrentPage(pageNum)}
                                                    className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${brandsCurrentPage === pageNum
                                                        ? 'bg-green-400 text-white'
                                                        : 'border border-gray-300 text-gray-700 hover:bg-gray-50'
                                                        }`}
                                                >
                                                    {pageNum}
                                                </button>
                                            );
                                        }
                                        return null;
                                    })}

                                    <button
                                        onClick={() => setBrandsCurrentPage(prev => Math.min(prev + 1, processedBrands.totalPages))}
                                        disabled={brandsCurrentPage === processedBrands.totalPages}
                                        className="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                    >
                                        Next
                                    </button>
                                </div>
                            </div>
                        )}
                    </motion.section>
                )}
            </div>
        </div>
    );
}
