'use client';

import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { FaHeart, FaRegHeart, FaStar, FaStarHalfAlt, FaRegStar, FaCheck, FaTruck, FaShieldAlt } from 'react-icons/fa';
import { MdLocalOffer } from 'react-icons/md';
import PrimaryCartBtn from '@/app/components/Buttons/PrimaryCartBtn';
import toast from 'react-hot-toast';

export function RelatedAndReviews() {
    return (
        <div className="mt-12 space-y-10">
            <section className="transform hover:scale-[1.01] transition-transform duration-300">
                <h2 className="text-2xl font-bold mb-6 bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                    Related Products
                </h2>
                <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-100">
                    <div className="flex items-center justify-center py-10">
                        <div className="text-center space-y-4">
                            <div className="w-16 h-16 mx-auto bg-orange-100 rounded-full flex items-center justify-center">
                                <svg className="w-8 h-8 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16m-7 6h7" />
                                </svg>
                            </div>
                            <p className="text-gray-600 text-lg font-medium">
                                Related products will be displayed here based on categories and preferences.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            <section className="transform hover:scale-[1.01] transition-transform duration-300">
                <h2 className="text-2xl font-bold mb-6 bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                    Customer Reviews
                </h2>
                <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-100">
                    <div className="flex items-center justify-center py-10">
                        <div className="text-center space-y-4">
                            <div className="w-16 h-16 mx-auto bg-orange-100 rounded-full flex items-center justify-center">
                                <svg className="w-8 h-8 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                </svg>
                            </div>
                            <p className="text-gray-600 text-lg font-medium">
                                Customer reviews and ratings will be displayed here.
                            </p>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    );
}

export default function PublicSingleProductDetailsById() {
    const [selectedImage, setSelectedImage] = useState('');
    const [isWishlist, setWishlist] = useState(false);
    const [product, setProduct] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    const pathname = usePathname();
    const [, , id] = pathname.split("/");

    useEffect(() => {
        const fetchProduct = async () => {
            try {
                setLoading(true);
                const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/products/${id}`);
                const data = await response.json();

                if (data.success === true && data.data.product.id === parseInt(id)) {
                    setProduct(data.data.product);
                    // Set the first image as selected image
                    if (data.data.product.images && data.data.product.images.length > 0) {
                        setSelectedImage(data.data.product.images[0].url);
                    } else if (data.data.product.image) {
                        setSelectedImage(data.data.product.image);
                    }
                } else {
                    setError('Product not found');
                }
            } catch (error) {
                console.error('Error fetching product:', error);
                setError('Failed to load product');
            } finally {
                setLoading(false);
            }
        };

        if (id) {
            fetchProduct();
        }
    }, [id]);

    const handleAddToWishlist = () => {
        setWishlist(!isWishlist);
        toast.success(isWishlist ? 'Removed from wishlist' : 'Added to wishlist');
    };

    // Calculate discount percentage
    const calculateDiscount = (originalPrice, specialPrice) => {
        if (!originalPrice || !specialPrice) return 0;
        const original = parseFloat(originalPrice);
        const special = parseFloat(specialPrice);
        return Math.round(((original - special) / original) * 100);
    };

    // Calculate savings amount
    const calculateSavings = (originalPrice, specialPrice) => {
        if (!originalPrice || !specialPrice) return 0;
        return (parseFloat(originalPrice) - parseFloat(specialPrice)).toFixed(2);
    };

    // Render star rating
    const renderStarRating = (rating) => {
        const stars = [];
        const fullStars = Math.floor(rating || 0);
        const hasHalfStar = (rating || 0) % 1 !== 0;

        for (let i = 0; i < fullStars; i++) {
            stars.push(<FaStar key={i} className="text-yellow-400" />);
        }

        if (hasHalfStar) {
            stars.push(<FaStarHalfAlt key="half" className="text-yellow-400" />);
        }

        const remainingStars = 5 - Math.ceil(rating || 0);
        for (let i = 0; i < remainingStars; i++) {
            stars.push(<FaRegStar key={`empty-${i}`} className="text-gray-300" />);
        }

        return stars;
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 flex items-center justify-center">
                <div className="bg-white p-8 rounded-2xl shadow-lg text-center">
                    <div className="relative w-32 h-32 mx-auto">
                        <div className="absolute inset-0 rounded-full border-4 border-gray-200"></div>
                        <div className="absolute inset-0 rounded-full border-4 border-t-orange-400 animate-spin"></div>
                    </div>
                    <p className="mt-6 text-lg text-gray-700 font-medium">Loading product details...</p>
                    <p className="mt-2 text-sm text-gray-500">Please wait a moment</p>
                </div>
            </div>
        );
    }

    if (error || !product) {
        return (
            <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 flex items-center justify-center p-4">
                <div className="bg-white rounded-2xl shadow-lg p-8 md:p-12 max-w-md w-full text-center">
                    <div className="mb-8">
                        <svg className="w-16 h-16 text-gray-400 mx-auto mb-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M12 20h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h2 className="text-3xl font-bold text-gray-800 mb-4">Product Not Found</h2>
                        <p className="text-gray-600 text-lg mb-8">{error || 'The product you are looking for does not exist.'}</p>
                    </div>
                    <Link
                        href="/products"
                        className="inline-flex items-center justify-center bg-gradient-to-r from-orange-400 to-orange-500 text-white px-8 py-4 rounded-xl font-semibold shadow-lg hover:from-orange-500 hover:to-orange-600 transform hover:scale-105 transition-all duration-200"
                    >
                        Back to Products
                    </Link>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 text-gray-600">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* Breadcrumb */}
                <nav className="flex mb-8" aria-label="Breadcrumb">
                    <ol className="inline-flex items-center space-x-1 md:space-x-3">
                        <li className="inline-flex items-center">
                            <Link href="/" className="text-gray-700 hover:text-orange-400">Home</Link>
                        </li>
                        <li>
                            <div className="flex items-center">
                                <span className="mx-2 text-gray-400">/</span>
                                <Link href="/products" className="text-gray-700 hover:text-orange-400">Products</Link>
                            </div>
                        </li>
                        <li>
                            <div className="flex items-center">
                                <span className="mx-2 text-gray-400">/</span>
                                <span className="text-gray-500 truncate">{product.name}</span>
                            </div>
                        </li>
                    </ol>
                </nav>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                    {/* Product Images */}
                    <div className="space-y-4">
                        <motion.div
                            initial={{ opacity: 0, scale: 0.9 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.5 }}
                            className="bg-white rounded-2xl p-6 shadow-lg"
                        >
                            <div className="aspect-square overflow-hidden rounded-xl bg-gray-100">
                                <Image
                                    src={selectedImage || product.image || '/images/placeholder.png'}
                                    alt={product.name}
                                    width={600}
                                    height={600}
                                    className="w-full h-full object-contain hover:scale-105 transition-transform duration-300"
                                />
                            </div>
                        </motion.div>

                        {/* Thumbnail Images */}
                        {product.images && product.images.length > 1 && (
                            <div className="flex gap-3 overflow-x-auto pb-2">
                                {product.images.map((img, idx) => (
                                    <button
                                        key={idx}
                                        onClick={() => setSelectedImage(img.url)}
                                        className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-all ${selectedImage === img.url ? 'border-orange-400 ring-2 ring-orange-200' : 'border-gray-200 hover:border-gray-300'
                                            }`}
                                    >
                                        <Image
                                            src={img.url}
                                            alt={img.alt || product.name}
                                            width={80}
                                            height={80}
                                            className="w-full h-full object-contain"
                                        />
                                    </button>
                                ))}
                            </div>
                        )}
                    </div>

                    {/* Product Information */}
                    <div className="space-y-6">
                        <motion.div
                            initial={{ opacity: 0, x: 50 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.5, delay: 0.2 }}
                            className='space-y-5'
                        >
                            {/* Product Title and Brand */}
                            <div className="space-y-4 bg-white/50 backdrop-blur-sm rounded-xl p-6 border border-gray-100/50 shadow-sm">
                                <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                                    {product.name}
                                </h1>
                                <div className="flex items-center gap-4 flex-wrap">
                                    <p className="text-lg text-gray-600 flex items-center gap-2">
                                        <span className="text-gray-400">Brand:</span>
                                        <span className="font-semibold bg-gray-100 px-3 py-1 rounded-full">{product.brand}</span>
                                    </p>
                                    <p className="text-sm text-gray-500 flex items-center gap-2">
                                        <span className="text-gray-400">SKU:</span>
                                        <code className="bg-gray-100 px-2 py-1 rounded font-mono">{product.sku}</code>
                                    </p>
                                </div>
                            </div>

                            {/* Rating */}
                            {product.rating && (
                                <div className="flex items-center space-x-2">
                                    <div className="flex items-center">
                                        {renderStarRating(product.rating)}
                                    </div>
                                    <span className="text-sm text-gray-600">
                                        {product.rating} ({product.review_count || 0} reviews)
                                    </span>
                                </div>
                            )}

                            {/* Pricing Section */}
                            <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100">
                                {/* Individual Price */}
                                <div className="space-y-3">
                                    <h3 className="text-lg font-semibold text-gray-800">Individual Price</h3>
                                    <div className="flex items-center space-x-3">
                                        <div className="flex items-center gap-4 mt-2">
                                            <span className="text-3xl font-bold text-orange-500">
                                                {product.currency} ${
                                                    (
                                                        (
                                                            parseFloat(product.pack_price.per_pack_special_price || product.pack_price.per_pack_price)
                                                        ) +
                                                        parseFloat((product.pack_price.per_pack_special_price || product.pack_price.per_pack_price) * parseFloat(product.pack_price.customer_margin) / 100)
                                                    ).toFixed(2)
                                                }

                                            </span>
                                            {product.pack_price.per_pack_special_price > 0 && (
                                                <>
                                                    <span className="text-sm line-through text-gray-400">
                                                        ${
                                                            (
                                                                parseFloat(product.pack_price.per_pack_price) +
                                                                parseFloat(product.pack_price.per_pack_price) * parseFloat(product.pack_price.customer_margin) / 100
                                                            ).toFixed(2)
                                                        }
                                                    </span>
                                                    <span className="text-sm font-medium px-3 py-1 bg-green-100 text-green-600 rounded-full">
                                                        Save ${
                                                            (
                                                                (
                                                                    parseFloat(product.pack_price.per_pack_price) -
                                                                    parseFloat(product.pack_price.per_pack_special_price)
                                                                ) + (
                                                                    (parseFloat(product.pack_price.per_pack_price) -
                                                                        parseFloat(product.pack_price.per_pack_special_price)) * parseFloat(product.pack_price.customer_margin) / 100
                                                                )
                                                            ).toFixed(2)
                                                        }
                                                    </span>
                                                </>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* Pack Pricing */}
                                {product.pack_price && (
                                    <div className="mt-6 pt-6 border-t border-gray-200">
                                        <h3 className="text-lg font-semibold text-gray-800 mb-3">Pack Pricing</h3>
                                        <div className="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg p-4 border border-orange-200">
                                            <div className="flex items-center justify-between mb-2">
                                                <span className="text-sm text-gray-600">
                                                    {product.pack_price.number_of_products} items per pack
                                                </span>
                                                <MdLocalOffer className="text-orange-500 text-xl" />
                                            </div>
                                            {/* <div className="flex items-center space-x-3">
                                                <span className="text-2xl font-bold text-green-600">
                                                    {product.currency} {parseFloat(product.pack_price.per_pack_special_price).toFixed(2)}
                                                </span>
                                                {parseFloat(product.pack_price.per_pack_special_price) < parseFloat(product.pack_price.per_pack_price) && (
                                                    <>
                                                        <span className="text-lg text-gray-400 line-through">
                                                            {product.currency} {parseFloat(product.pack_price.per_pack_price).toFixed(2)}
                                                        </span>
                                                        <span className="bg-green-100 text-green-600 px-2 py-1 rounded-full text-sm font-medium">
                                                            Save {product.currency} {calculateSavings(product.pack_price.per_pack_price, product.pack_price.per_pack_special_price)}
                                                        </span>
                                                    </>
                                                )}
                                            </div> 
                                            <p className="text-sm text-gray-600 mt-2">
                                                Per item: {product.currency} {(parseFloat(product.pack_price.per_pack_special_price) / product.pack_price.number_of_products).toFixed(2)}
                                            </p> */}
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Stock Status */}
                            <div className="bg-white/50 backdrop-blur-sm rounded-lg p-4 border border-gray-100/50 shadow-sm">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        {product.stock?.is_in_stock ? (
                                            <>
                                                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-100">
                                                    <FaCheck className="text-green-500 text-lg" />
                                                </div>
                                                <div className="flex flex-col">
                                                    <span className="text-green-600 font-medium">In Stock</span>
                                                    <span className="text-sm text-gray-500">{product.stock.quantity} units available</span>
                                                </div>
                                            </>
                                        ) : (
                                            <>
                                                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-red-100/80 shadow-inner">
                                                    <div className="w-4 h-4 bg-red-500 rounded-full animate-pulse shadow-lg"></div>
                                                </div>
                                                <div className="flex flex-col">
                                                    <span className="text-red-600 font-semibold tracking-wide">Out of Stock</span>
                                                    <span className="text-sm text-gray-500/90 font-medium">Currently unavailable</span>
                                                </div>
                                            </>
                                        )}
                                    </div>
                                    <div className="flex items-center gap-2 px-3 py-1.5 rounded-lg bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200">
                                        <div className={`w-2 h-2 rounded-full ${product.stock?.is_in_stock ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></div>
                                        <span className="text-sm font-medium text-gray-600">
                                            {product.stock?.is_in_stock ? "Ready to ship" : "Notify when available"}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            {/* Features */}
                            <div className="flex items-center justify-center gap-8 py-4 px-6 bg-gradient-to-r from-orange-50 to-yellow-50 rounded-xl border border-orange-100 shadow-sm">
                                <div className="flex items-center gap-3 group">
                                    <div className="p-2 bg-white rounded-lg shadow-sm group-hover:shadow-md transition-shadow">
                                        <FaTruck className="text-orange-400 text-xl" />
                                    </div>
                                    <span className="font-medium text-gray-700">Free Delivery</span>
                                </div>
                                <div className="flex items-center gap-3 group">
                                    <div className="p-2 bg-white rounded-lg shadow-sm group-hover:shadow-md transition-shadow">
                                        <FaShieldAlt className="text-orange-400 text-xl" />
                                    </div>
                                    <span className="font-medium text-gray-700">Quality Assured</span>
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="space-y-4 px-5">
                                <PrimaryCartBtn
                                    text="Add to Cart"
                                    cartPosition="left"
                                    tailwindCss="w-full"
                                    product={product}
                                />
                                <button
                                    onClick={handleAddToWishlist}
                                    className={`flex items-center justify-center w-full py-3 rounded-xl transition-all duration-200 ${isWishlist
                                        ? 'bg-red-50 text-red-500 border-2 border-red-300 hover:bg-red-100'
                                        : 'bg-white text-gray-500 border-2 border-gray-200 hover:border-red-300 hover:text-red-500'
                                        }`}
                                >
                                    {isWishlist ? (
                                        <FaHeart className="text-2xl" />
                                    ) : (
                                        <FaRegHeart className="text-2xl" />
                                    )}
                                </button>
                            </div>
                        </motion.div>
                    </div>
                </div>

                {/* Product Details Tabs */}
                <motion.div
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                    className="mt-16"
                >
                    <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
                        <div className="border-b border-gray-200">
                            <nav className="flex space-x-8 px-6">
                                <button className="py-4 px-4 border-b-2 border-orange-400 text-orange-600 font-medium hover:text-orange-700 transition-colors">
                                    Description
                                </button>
                            </nav>
                        </div>

                        <div className="p-8">
                            <div className="prose max-w-none">
                                <h3 className="text-2xl font-semibold mb-6 text-gray-800">Product Description</h3>
                                <p className="text-gray-700 leading-relaxed mb-8 text-lg">
                                    {product.description || 'No description available for this product.'}
                                </p>

                                {product.specifications && (
                                    <div className="bg-gray-50 rounded-xl p-6 mb-8">
                                        <h4 className="text-xl font-semibold mb-4 text-gray-800">Specifications</h4>
                                        <div className="text-gray-700 space-y-2">
                                            {product.specifications}
                                        </div>
                                    </div>
                                )}

                                {product.categories && product.categories.length > 0 && (
                                    <div className="mt-8">
                                        <h4 className="text-xl font-semibold mb-4 text-gray-800">Categories</h4>
                                        <div className="flex flex-wrap gap-3">
                                            {product.categories.map((category, index) => (
                                                <span
                                                    key={index}
                                                    className="bg-gradient-to-r from-orange-50 to-orange-100 text-orange-600 px-4 py-2 rounded-full text-sm font-medium hover:from-orange-100 hover:to-orange-200 transition-colors cursor-pointer shadow-sm"
                                                >
                                                    {category.name}
                                                </span>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </motion.div>

                <RelatedAndReviews />
            </div>
        </div>
    );
}
