"use client";

import { usePathname, useRouter } from 'next/navigation';
import React, { useEffect, useState, useRef } from 'react';
import { FaSearch, FaTimes, FaBox, FaTag, FaStore } from 'react-icons/fa';

// Dummy data
const dummyProducts = [
    "Wireless Headphones", "Smart Watch", "Organic Honey", "Men's Jacket",
    "Women's Perfume", "Baby Diapers", "LED TV", "Laptop", "Smartphone", "Bluetooth Speaker", "Wireless Earbuds", "Noise Cancelling Headphones", "Wireless Mouse", "Wireless Keyboard", "Wireless Mousepad", "Wireless Earbuds", "Noise Cancelling Headphones", "Wireless Mouse", "Wireless Keyboard", "Wireless Mousepad"
];

const dummyCategories = [
    "Electronics", "Grocery", "Women", "Men", "Baby", "Drinks", "Gadgets", "Home", "Health", "Beauty", "Sports", "Automotive", "Jewelry", "Kids", "Toys",
];

const dummyBrands = [
    "Apple", "Samsung", "Sony", "Nike", "Adidas", "Microsoft", "Google", "Amazon", "Dell", "HP", "Canon", "Nikon", "LG", "Panasonic", "Philips", "Bosch", "Siemens", "Xiaomi", "Huawei", "OnePlus", "Diptouch", "UrH2o",
];

// task!! add it later.
const getAllCategories = async function () {
    const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/categories`);
    const data = await response.json();
    return data;
};

export default function SearchBar() {
    const [searchQuery, setSearchQuery] = useState('');
    const [suggestions, setSuggestions] = useState([]);
    const [isInputFocused, setIsInputFocused] = useState(false);
    const [selectedIndex, setSelectedIndex] = useState(-1);
    const router = useRouter();
    const pathname = usePathname();
    const inputRef = useRef(null);
    const suggestionRefs = useRef([]);

    // Handle search submission
    const handleSearchSubmit = (query) => {
        if (query && query.trim()) {
            const encodedQuery = encodeURIComponent(query.trim());
            router.push(`/products?search=${encodedQuery}`);
        }
    };

    // Don't automatically update URL on every keystroke - only on submission
    // This prevents interference with existing search pages

    useEffect(() => {
        const allItems = [...dummyProducts, ...dummyCategories, ...dummyBrands];
        if (searchQuery.trim() === '') {
            setSuggestions([]);
            setSelectedIndex(-1);
            return;
        }
        const filtered = allItems.filter(item =>
            item.toLowerCase().includes(searchQuery.toLowerCase())
        );
        setSuggestions(filtered.slice(0, 6)); // limit to 6 suggestions
        setSelectedIndex(-1);
    }, [searchQuery]);

    // Keyboard navigation
    const handleKeyDown = (e) => {
        if (suggestions.length === 0) return;

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                setSelectedIndex(prev =>
                    prev < suggestions.length - 1 ? prev + 1 : 0
                );
                break;
            case 'ArrowUp':
                e.preventDefault();
                setSelectedIndex(prev =>
                    prev > 0 ? prev - 1 : suggestions.length - 1
                );
                break;
            case 'Enter':
                e.preventDefault();
                if (selectedIndex >= 0) {
                    const selectedItem = suggestions[selectedIndex];
                    setSearchQuery(selectedItem);
                    handleSearchSubmit(selectedItem);
                    setSuggestions([]);
                    setIsInputFocused(false);
                    inputRef.current?.blur();
                } else if (searchQuery.trim()) {
                    handleSearchSubmit(searchQuery);
                    setIsInputFocused(false);
                    inputRef.current?.blur();
                }
                break;
            case 'Escape':
                setSuggestions([]);
                setIsInputFocused(false);
                inputRef.current?.blur();
                break;
        }
    };

    // Clear search
    const clearSearch = () => {
        setSearchQuery('');
        setSuggestions([]);
        setSelectedIndex(-1);
        inputRef.current?.focus();
    };

    // Handle suggestion click
    const handleSuggestionClick = (item) => {
        setSearchQuery(item);
        handleSearchSubmit(item);
        setSuggestions([]);
        setIsInputFocused(false);
        inputRef.current?.blur();
    };

    // Determine if item is a product, category, or brand
    const getItemType = (item) => {
        if (dummyProducts.includes(item)) return 'product';
        if (dummyCategories.includes(item)) return 'category';
        if (dummyBrands.includes(item)) return 'brand';
        return 'product'; // fallback
    };

    return (
        <section
            className='w-full bg-gradient-to-r from-orange-50/30 via-white/20 to-orange-50/30 backdrop-blur-md border-b border-orange-200/50 shadow-sm sticky top-0 z-50'
            role="search"
            aria-label="Product search"
        >
            <div className="max-w-4xl mx-auto px-4 py-4 rounded-2xl">
                {/* Search Input Container */}
                <form onSubmit={(e) => {
                    e.preventDefault();
                    handleSearchSubmit(searchQuery);
                }}>
                    <div className="relative">
                        <div className={`relative flex items-center rounded-2xl transition-all duration-300 ${isInputFocused
                            ? 'transform scale-[1.02] shadow-lg'
                            : 'shadow-md hover:shadow-lg'
                            }`}>
                            {/* Search Icon */}
                            <div className="absolute left-4 z-10">
                                <FaSearch className={`transition-colors duration-200 ${isInputFocused ? 'text-orange-500' : 'text-gray-400'
                                    }`} />
                            </div>

                            {/* Input Field */}
                            <input
                                ref={inputRef}
                                type="text"
                                placeholder='Search products, categories, brands, and more...'
                                className={`w-full pl-12 pr-12 py-4 text-gray-700 bg-white border-2 rounded-2xl placeholder:text-gray-400 transition-all duration-300 focus:outline-none ${isInputFocused
                                    ? 'border-orange-400 ring-4 ring-orange-100'
                                    : 'border-gray-200 hover:border-orange-300'
                                    }`}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                onFocus={() => setIsInputFocused(true)}
                                onBlur={() => setTimeout(() => setIsInputFocused(false), 150)}
                                onKeyDown={handleKeyDown}
                                value={searchQuery}
                                autoComplete="off"
                                aria-label="Search for products, categories, brands, and more..."
                                aria-expanded={suggestions.length > 0}
                                aria-haspopup="listbox"
                                aria-activedescendant={selectedIndex >= 0 ? `suggestion-${selectedIndex}` : undefined}
                            />

                            {/* Clear Button */}
                            {searchQuery && (
                                <button
                                    onClick={clearSearch}
                                    className="absolute right-4 p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200 rounded-full hover:bg-gray-100"
                                    aria-label="Clear search"
                                    type="button"
                                >
                                    <FaTimes />
                                </button>
                            )}
                        </div>

                        {/* Suggestions Dropdown */}
                        {suggestions.length > 0 && isInputFocused && (
                            <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-2xl shadow-xl z-50 overflow-hidden">
                                <ul
                                    className="max-h-80 overflow-y-auto"
                                    role="listbox"
                                    aria-label="Search suggestions"
                                >
                                    {suggestions.map((item, index) => {
                                        const itemType = getItemType(item);
                                        const isSelected = index === selectedIndex;

                                        return (
                                            <li
                                                key={index}
                                                ref={el => suggestionRefs.current[index] = el}
                                                id={`suggestion-${index}`}
                                                className={`flex items-center px-4 py-3 cursor-pointer transition-all duration-200 border-b border-gray-50 last:border-b-0 ${isSelected
                                                    ? 'bg-orange-50 border-orange-100'
                                                    : 'hover:bg-gray-50'
                                                    }`}
                                                onClick={() => handleSuggestionClick(item)}
                                                role="option"
                                                aria-selected={isSelected}
                                            >
                                                {/* Icon */}
                                                <div className={`mr-3 p-2 rounded-lg ${itemType === 'product'
                                                    ? 'bg-orange-100 text-orange-500'
                                                    : itemType === 'category'
                                                        ? 'bg-blue-100 text-blue-500'
                                                        : 'bg-green-100 text-green-500'
                                                    }`}>
                                                    {itemType === 'product' ? (
                                                        <FaBox className="text-sm" />
                                                    ) : itemType === 'category' ? (
                                                        <FaTag className="text-sm" />
                                                    ) : (
                                                        <FaStore className="text-sm" />
                                                    )}
                                                </div>

                                                {/* Content */}
                                                <div className="flex-1 min-w-0">
                                                    <div className="flex items-center justify-between">
                                                        <span className="text-gray-900 font-medium truncate">
                                                            {item}
                                                        </span>
                                                        <span className={`text-xs px-2 py-1 rounded-full ml-2 ${itemType === 'product'
                                                            ? 'bg-orange-100 text-orange-600'
                                                            : itemType === 'category'
                                                                ? 'bg-blue-100 text-blue-600'
                                                                : 'bg-green-100 text-green-600'
                                                            }`}>
                                                            {itemType}
                                                        </span>
                                                    </div>
                                                </div>
                                            </li>
                                        );
                                    })}
                                </ul>
                            </div>
                        )}
                    </div>
                </form>

                {/* Search Tips */}
                {isInputFocused && suggestions.length === 0 && searchQuery.trim() === '' && (
                    <div className="mt-3 text-center">
                        <p className="text-sm text-gray-500">
                            Try searching for "headphones", "electronics", "apple", or "organic"
                        </p>
                    </div>
                )}
            </div>
        </section>
    );
}
