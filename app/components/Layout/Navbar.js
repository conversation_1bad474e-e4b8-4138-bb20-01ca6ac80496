'use client';

import { useState, useEffect, useRef } from 'react';
import {
  BellIcon, MagnifyingGlassIcon, Bars3Icon, ChevronDownIcon, UserIcon, Cog6ToothIcon, ArrowLeftOnRectangleIcon, HomeIcon, ShoppingBagIcon, ShoppingCartIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import Logout from '../Authentication/Logout';
import { useCart } from '../../context/CartContext';

export default function Navbar({ onMenuClick }) {
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const [profileOpen, setProfileOpen] = useState(false);
  const pathname = usePathname();
  const profileDropdownRef = useRef(null);
  const { cartCount } = useCart();

  // Determine admin type and user info based on current route
  const getAdminInfo = () => {
    if (pathname.includes('super-admin')) {
      return {
        type: 'Super Admin',
        initial: 'SA',
        profilePath: '/super-admin/profile',
        settingsPath: '/super-admin/settings'
      };
    } else if (pathname.includes('partner-admin')) {
      return {
        type: 'Partner Admin',
        initial: 'PA',
        profilePath: '/partner-admin/profile',
        settingsPath: '/partner-admin/settings'
      };
    } else if (pathname.includes('customer-admin')) {
      return {
        type: 'Customer',
        initial: 'CU',
        profilePath: '/customer-admin/profile',
        settingsPath: '/customer-admin/settings'
      };
    }
    return {
      type: 'Admin',
      initial: 'A',
      profilePath: '#',
      settingsPath: '#'
    };
  };

  const adminInfo = getAdminInfo();

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (profileDropdownRef.current && !profileDropdownRef.current.contains(event.target)) {
        setProfileOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <header className="bg-white border-b border-gray-200 z-10">
      <div className="px-4 py-3 flex items-center justify-between">
        <div className="flex items-center">
          <button
            onClick={onMenuClick}
            className="p-2 mr-2 md:hidden rounded-md hover:bg-gray-100"
            aria-label="Toggle menu"
          >
            <Bars3Icon className="h-6 w-6 text-gray-500" />
          </button>

          <div className="relative max-w-xs hidden md:block">
            <span className="absolute inset-y-0 left-0 flex items-center pl-3">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-500" />
            </span>
            <input
              type="text"
              placeholder="Search..."
              className="block text-gray-500 w-full pl-10 pr-3 py-2 rounded-md text-sm border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <div className="relative">
            <button
              onClick={() => setNotificationsOpen(!notificationsOpen)}
              className="p-1 rounded-full text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
              <BellIcon className="h-6 w-6" />
            </button>

            {notificationsOpen && (
              <div className="origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                <div className="py-2 px-4 text-sm font-semibold text-orange-400 border-b">
                  Notifications
                </div>
                <div className="max-h-96 overflow-y-auto">
                  <a href="#" className="block px-4 py-3 border-b hover:bg-gray-50">
                    <div className="flex items-start">
                      <div className="h-8 w-8 rounded-full bg-indigo-500 flex items-center justify-center text-white">
                        N
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">New order received</p>
                        <p className="text-xs text-gray-500">10 minutes ago</p>
                      </div>
                    </div>
                  </a>
                  <a href="#" className="block px-4 py-3 hover:bg-gray-50">
                    <div className="flex items-start">
                      <div className="h-8 w-8 rounded-full bg-purple-500 flex items-center justify-center text-white">
                        U
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">User activity detected</p>
                        <p className="text-xs text-gray-500">2 hours ago</p>
                      </div>
                    </div>
                  </a>
                </div>

                <Link href="#" className="block text-center text-sm font-semibold text-orange-400 hover:bg-gray-50 py-2 rounded-md">
                  All notifications
                </Link>
              </div>
            )}
          </div>

          {/* Home and Shopping Buttons */}
          <div className="hidden md:flex items-center space-x-2">
            <Link
              href="/"
              className="flex items-center space-x-2 px-3 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200 shadow-sm"
            >
              <HomeIcon className="h-4 w-4" />
              <span className="text-sm font-medium">Home Page</span>
            </Link>

            <Link
              href="/products"
              className="flex items-center space-x-2 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 shadow-sm"
            >
              <ShoppingBagIcon className="h-4 w-4" />
              <span className="text-sm font-medium">Go Back to Shopping</span>
            </Link>
          </div>

          {/* Mobile Navigation Buttons */}
          <div className="md:hidden flex items-center space-x-1">
            <Link
              href="/"
              className="flex items-center justify-center p-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200"
              title="Home Page"
            >
              <HomeIcon className="h-5 w-5" />
            </Link>

            <Link
              href="/products"
              className="flex items-center justify-center p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200"
              title="Go Back to Shopping"
            >
              <ShoppingBagIcon className="h-5 w-5" />
            </Link>
          </div>

          {/* Cart Icon with Count - Only show for Customer and Partner Admin, not Super Admin */}
          {!pathname.includes('super-admin') && (
            <Link
              href="/cart"
              className="relative p-2 text-gray-400 hover:text-gray-500 transition-colors duration-200 group"
              title="View Cart"
            >
              <ShoppingCartIcon className="h-6 w-6 group-hover:text-orange-500 transition-colors duration-200" />
              {cartCount > 0 && (
                <span className="absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-orange-500 rounded-full min-w-[20px] h-5 animate-pulse">
                  {cartCount > 99 ? '99+' : cartCount}
                </span>
              )}
            </Link>
          )}

          <div className="relative" ref={profileDropdownRef}>
            <button
              onClick={() => setProfileOpen(!profileOpen)}
              className="flex items-center space-x-2 focus:outline-none hover:bg-gray-50 rounded-lg px-2 py-1 transition-colors duration-200"
            >
              <div className="h-8 w-8 rounded-full bg-gradient-to-r from-orange-500 to-orange-600 flex items-center justify-center text-white font-semibold shadow-md">
                {adminInfo.initial}
              </div>
              <span className="hidden md:block text-sm font-medium text-gray-700">{adminInfo.type}</span>
              <ChevronDownIcon className={`h-4 w-4 text-gray-500 transition-transform duration-200 ${profileOpen ? 'rotate-180' : ''}`} />
            </button>

            {profileOpen && (
              <div className="origin-top-right absolute right-0 mt-2 w-56 rounded-xl shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50 border border-gray-100">
                <div className="py-2">
                  {/* Profile Section */}
                  <div className="px-4 py-3 border-b border-gray-100">
                    <div className="flex items-center space-x-3">
                      <div className="h-10 w-10 rounded-full bg-gradient-to-r from-orange-500 to-orange-600 flex items-center justify-center text-white font-semibold">
                        {adminInfo.initial}
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{adminInfo.type}</p>
                        <p className="text-xs text-gray-500">Administrator</p>
                      </div>
                    </div>
                  </div>

                  {/* Menu Items */}
                  <div className="py-1">
                    <Link
                      href={adminInfo.profilePath}
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-600 transition-colors duration-200"
                      onClick={() => setProfileOpen(false)}
                    >
                      <UserIcon className="h-4 w-4 mr-3" />
                      Your Profile
                    </Link>

                    <Link
                      href={adminInfo.settingsPath}
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-600 transition-colors duration-200"
                      onClick={() => setProfileOpen(false)}
                    >
                      <Cog6ToothIcon className="h-4 w-4 mr-3" />
                      Settings
                    </Link>

                    <div className="border-t border-gray-100 my-1"></div>

                    <div className="px-4 py-2">
                      <Logout
                        className="flex items-center w-full text-left text-sm text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors duration-200 rounded-md px-0 py-1"
                        iconOnly={false}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header >
  );
}
