"use client";

import { usePathname } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { FaEdit, FaLock, FaImage, FaTag, FaCog, FaLink, FaSearch } from 'react-icons/fa';
import { FaPlus } from 'react-icons/fa6';
import { MdDescription, MdCategory, MdInfo } from 'react-icons/md';
import { AiOutlineCloudUpload } from 'react-icons/ai';
import { RiParentLine } from 'react-icons/ri';
import Swal from 'sweetalert2';
import contentUpload from '@/app/utils/contentUpload';

const initialCategory = {
    id: null,
    name: '',
    slug: '',
    description: '',
    parent_id: null,
    parent: null,
    image: '',
    is_featured: false,
    is_active: true,
    product_count: 0,
    sub_categories: [],
    children: [],
    section_id: 1,
    commission_rate: 10,
    url: "",
    meta_title: "",
    meta_description: "",
    meta_keywords: "",
    status: true,
    created_at: '',
    updated_at: '',
};

export default function CategoriesForm({ isAddMode, isUpdateMode }) {
    const [category, setCategory] = useState(initialCategory); // Always initialize with initialCategory
    const pathname = usePathname();
    const [, userType, , , id] = pathname.split('/');

    async function getCategoryById() {
        const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/categories/${id}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('superAdminAuthToken')}`,
            }
        });

        const data = await response.json();

        if (data.success === true && data.data.category.id) {
            setCategory(prevCategory => ({
                ...prevCategory, // Keep any default values from initialCategory
                ...data.data.category, // Override with fetched values
                description: data.data.description || '',
            }));
        }
    }

    useEffect(() => {
        if (isUpdateMode) {
            getCategoryById();
        }
    }, [id]);

    const handleChange = (e) => {
        const { name, type, value, files, checked } = e.target;

        setCategory(prev => ({
            ...prev,
            [name]: type === 'file' ? files :
                type === 'checkbox' ? checked :
                    value
        }));
    };

    async function handleAddCategory(category) {
        // image utility function
        const imageResponse = await contentUpload.MultipleImageUpload(category.image, "superAdminAuthToken");
        console.log(imageResponse);

        if (imageResponse && imageResponse instanceof Array && imageResponse.length > 0) {
            const sanizedCategory = {
                // id: category.id, // Backend will auto generate ID
                name: category.name,
                slug: category.slug,
                description: category.description,
                parent_id: category.parent_id,
                image: `${[...imageResponse]}` || ``,
                is_featured: category.is_featured,
                is_active: category.is_active,
                product_count: category.product_count,
                section_id: category.section_id,
                commission_rate: category.commission_rate,
                url: category.url,
                meta_title: category.meta_title,
                meta_description: category.meta_description,
                meta_keywords: category.meta_keywords,
                status: category.status,
                // created_at: category.created_at,
                // updated_at: category.updated_at,
            }
            console.log(sanizedCategory);

            const dataResponse = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/categories`, {
                method: 'POST',
                body: JSON.stringify(sanizedCategory),
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('superAdminAuthToken')}`,
                }
            });

            const data = await dataResponse.json();
            console.log(data);

            if (data.success === true && data.data.category.id) {
                Swal.fire({
                    icon: 'success',
                    title: data.message || 'Category added successfully',
                    showConfirmButton: true,
                    timerProgressBar: true,
                    iconColor: '#48bb78',
                })
            } else {
                const errorMessages = Object.entries(data.errors)
                    .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
                    .join('\n');
                Swal.fire({
                    icon: 'error',
                    title: data.message || 'Category creation failed',
                    text: data.details || errorMessages || 'Category creation failed',
                    showConfirmButton: true,
                    timerProgressBar: true,
                    iconColor: '#f56565',
                })
            }
        }
    }

    async function handleUpdateCategory(category) {
        console.log("update", category);

        const sanizedCategory = {
            // id: category.id, // Backend will auto generate ID
            name: category.name,
            slug: category.slug,
            description: category.description,
            parent_id: category.parent_id,
            image: category.image,
            is_featured: category.is_featured,
            is_active: category.is_active,
            product_count: category.product_count,
            section_id: category.section_id,
            commission_rate: category.commission_rate,
            url: category.url,
            meta_title: category.meta_title,
            meta_description: category.meta_description,
            meta_keywords: category.meta_keywords,
            status: category.status,
            // created_at: category.created_at,
            // updated_at: category.updated_at,
        };
        // Handle image upload if image has changed
        if (category.image instanceof FileList) {
            console.log("update image", category.image);

            const imageResponse = await contentUpload.MultipleImageUpload(category.image, "superAdminAuthToken");
            if (imageResponse && imageResponse instanceof Array && imageResponse.length > 0) {
                sanizedCategory.image = `${[...imageResponse]}`;
            }
        }

        const dataResponse = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/categories/${id}`, {
            method: 'PUT',
            body: JSON.stringify(sanizedCategory),
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('superAdminAuthToken')}`,
            }
        });

        const data = await dataResponse.json();
        console.log(data);

        if (data.success === true && data.data.category.id) {
            Swal.fire({
                icon: 'success',
                title: data.message || 'Category updated successfully',
                showConfirmButton: true,
                timerProgressBar: true,
                iconColor: '#48bb78',
            });
        } else {
            const errorMessages = Object.entries(data.errors)
                .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
                .join('\n');

            Swal.fire({
                icon: 'error',
                title: data.message || 'Category update failed',
                text: data.details || errorMessages || 'Category update failed',
                showConfirmButton: true,
                timerProgressBar: true,
                iconColor: '#f56565',
            });
        }
    }

    // task!!! form validation
    async function handleValidateForm() {
        // Implement form validation logic here
    }

    const handleSubmit = async (e) => {
        e.preventDefault();

        // task!! form validation
        // Validate form
        // if (!handleValidateForm()) {
        //     return;
        // }

        // Handle form submission logic here
        isAddMode && handleAddCategory(category);
        isUpdateMode && handleUpdateCategory(category);
    };

    return (
        <section className="max-w-4xl mx-auto p-6 bg-white rounded-xl shadow-lg text-gray-600 transition-all duration-300 hover:shadow-xl">
            <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                {isAddMode && <>
                    <FaPlus className="text-green-500" />
                    Create New Category
                </>
                }
                {isUpdateMode && <>
                    <FaEdit className="text-blue-500" />
                    Update Category
                </>
                }
            </h2>

            <form className="space-y-8" onSubmit={handleSubmit}>
                {/* System Information */}
                {isUpdateMode && (
                    <div className="space-y-4 bg-gray-50 p-6 rounded-lg border border-gray-200 hover:border-orange-200 transition-colors duration-300">
                        <h3 className="text-lg font-semibold text-gray-700 flex items-center gap-2">
                            <MdInfo className="text-blue-500 text-xl" />
                            System Information
                            <FaLock className="text-gray-400 ml-2" />
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-500">ID</label>
                                <input
                                    type="text"
                                    value={category.id || 'Not assigned'}
                                    readOnly
                                    className="mt-1 block w-full rounded-md bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed p-2"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-500">Created At</label>
                                <input
                                    type="text"
                                    value={category.created_at || 'Not created'}
                                    readOnly
                                    className="mt-1 block w-full rounded-md bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed p-2"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-500">Updated At</label>
                                <input
                                    type="text"
                                    value={category.updated_at || 'Not updated'}
                                    readOnly
                                    className="mt-1 block w-full rounded-md bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed p-2"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-500">Product Count</label>
                                <input
                                    type="number"
                                    value={category.product_count || 0}
                                    readOnly
                                    className="mt-1 block w-full rounded-md bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed p-2"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-500">Parent</label>
                                <input
                                    type="text"
                                    value={category.parent?.name || 'No parent'}
                                    readOnly
                                    className="mt-1 block w-full rounded-md bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed p-2"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-500">Children Count</label>
                                <input
                                    type="number"
                                    value={category.children?.length || 0}
                                    readOnly
                                    className="mt-1 block w-full rounded-md bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed p-2"
                                />
                            </div>
                        </div>
                    </div>
                )}

                {/* Basic Information */}
                <div className="space-y-6 bg-white p-6 rounded-lg border border-gray-200 hover:border-orange-200 transition-colors duration-300">
                    <h3 className="text-lg font-semibold text-gray-700 flex items-center gap-2">
                        <MdCategory className="text-orange-500 text-xl" />
                        Basic Information
                    </h3>

                    <div className="">
                        <div className="">
                            <label className=" text-sm font-medium text-gray-700 flex items-center gap-2">
                                <FaTag className="text-purple-500" />
                                Name
                            </label>
                            <input
                                type="text"
                                name="name"
                                value={category.name}
                                onChange={handleChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 p-2"
                            />
                        </div>

                        <div className="">
                            <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                                <FaLink className="text-indigo-500" />
                                Slug
                            </label>
                            <input
                                type="text"
                                name="slug"
                                value={category.slug}
                                onChange={handleChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 p-2"
                            />
                        </div>

                        <div className="col-span-2">
                            <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                                <MdDescription className="text-teal-500" />
                                Description
                            </label>
                            <textarea
                                name="description"
                                value={category.description}
                                onChange={handleChange}
                                rows={3}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 p-2"
                            />
                        </div>

                        <div className="">
                            <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                                <RiParentLine className="text-cyan-500" />
                                Parent ID
                            </label>
                            <input
                                type="number"
                                name="parent_id"
                                value={category.parent_id || ''}
                                onChange={handleChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 p-2"
                            />
                        </div>
                    </div>
                </div>

                {/* Settings */}
                <div className="space-y-6 bg-white p-6 rounded-lg border border-gray-200 hover:border-orange-200 transition-colors duration-300">
                    <h3 className="text-lg font-semibold text-gray-700 flex items-center gap-2">
                        <FaCog className="text-gray-500 animate-spin-slow" />
                        Settings
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Commission Rate (%)</label>
                            <input
                                type="number"
                                name="commission_rate"
                                value={category.commission_rate}
                                onChange={handleChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 p-2"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700">Section ID</label>
                            <input
                                type="number"
                                name="section_id"
                                value={category.section_id}
                                onChange={handleChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 p-2"
                            />
                        </div>
                    </div>

                    <div className="flex space-x-4">
                        <label className="flex items-center">
                            <input
                                type="checkbox"
                                name="is_featured"
                                checked={category.is_featured}
                                onChange={handleChange}
                                className="w-5 h-5 rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">Featured</span>
                        </label>

                        <label className="flex items-center">
                            <input
                                type="checkbox"
                                name="is_active"
                                checked={category.is_active}
                                onChange={handleChange}
                                className="rounded w-5 h-5  border-gray-300 text-orange-600 focus:ring-orange-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">Active</span>
                        </label>

                        <label className="flex items-center">
                            <input
                                type="checkbox"
                                name="status"
                                checked={category.status}
                                onChange={handleChange}
                                className="rounded w-5 h-5  border-gray-300 text-orange-600 focus:ring-orange-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">Status</span>
                        </label>
                    </div>
                </div>

                {/* SEO Information */}
                <div className="space-y-6 bg-white p-6 rounded-lg border border-gray-200 hover:border-orange-200 transition-colors duration-300">
                    <h3 className="text-lg font-semibold text-gray-700 flex items-center gap-2">
                        <FaSearch className="text-blue-500" />
                        SEO Information
                    </h3>

                    <div className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Meta Title</label>
                            <input
                                type="text"
                                name="meta_title"
                                value={category.meta_title}
                                onChange={handleChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 p-2"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700">Meta Description</label>
                            <textarea
                                name="meta_description"
                                value={category.meta_description}
                                onChange={handleChange}
                                rows={2}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 p-2"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700">Meta Keywords</label>
                            <input
                                type="text"
                                name="meta_keywords"
                                value={category.meta_keywords}
                                onChange={handleChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 p-2"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700">URL</label>
                            <input
                                type="url"
                                name="url"
                                value={category.url}
                                onChange={handleChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 p-2"
                            />
                        </div>

                    </div>
                    <div className='w-full'>
                        <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                            <FaImage className="text-emerald-500 text-2xl" />
                            Image Upload
                        </label>
                        <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-orange-400 transition-all duration-300 -hover:shadow-md">
                            <div className="space-y-2 text-center">
                                <AiOutlineCloudUpload className="mx-auto h-12 w-12 text-purple-400 -hover:text-orange-500 transition-colors duration-300" />
                                <div className="flex text-sm text-gray-600">
                                    <label className="relative cursor-pointer bg-white rounded-md font-medium text-orange-600 hover:text-orange-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-orange-500">
                                        <span>Upload Image (up to 5MB JPG, PNG, )</span>
                                        <input
                                            type="file"
                                            name="image"
                                            accept="image/*"
                                            onChange={handleChange}
                                            className="sr-only"
                                        />
                                    </label>
                                </div>
                                <p className="text-xs text-gray-500">PNG, JPG, GIF and webp up to 5MB</p>

                            </div>
                        </div>
                    </div>

                    {/* task!! show previous image if */}
                    <div>

                    </div>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-4 pt-4">
                    {/* <button
                        type="button"
                        className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                    >
                        Cancel
                    </button> */}

                    {
                        isAddMode && <button
                            type="submit"
                            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-400 hover:bg-green-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 flex items-center"
                        >
                            <FaPlus className='text-3xl' /><span className='mx-1 text-xl font-semibold'>Create Category</span>
                        </button>
                    }
                    {
                        isUpdateMode && <button
                            type="submit"
                            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-400 hover:bg-orange-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 flex items-center"
                        >
                            <FaEdit className='text-3xl' /><span className='mx-1 text-xl font-semibold'>Update Category</span>
                        </button>
                    }

                </div>
            </form>
        </section >
    )
}
