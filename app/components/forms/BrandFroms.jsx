"use client";

import { usePathname } from 'next/navigation';
import React, { useState, useEffect } from 'react';
import Swal from 'sweetalert2';
import { FaEdit, FaPlus, FaGlobe, FaStar, FaCheck, FaInfoCircle, FaUser, FaCalendarAlt, FaImage, FaTag, FaFont, FaAlignLeft, FaExclamationTriangle } from 'react-icons/fa';

const getBrand = async function (id) {
    try {
        const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/admin/brands/${id}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('superAdminAuthToken')}`
            }
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.message || 'Failed to fetch brand');
        }

        return data;
    } catch (error) {
        console.error('Error fetching brand:', error.message);
        return { success: false, message: error.message };
    }
};

const postBrand = async function (senitaizedData) {
    // Implement the logic to post the brand senitaizedData
    console.log(senitaizedData, "---------------------------///////////////////////");

    const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/admin/brands`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('superAdminAuthToken')}`
        },
        body: JSON.stringify(senitaizedData),
    });

    const data = await response.json();

    return data;
};

const putBrand = async function (id, senitaizedData) {
    // Implement the logic to update the brand senitaizedData
    console.log(id, senitaizedData, "---------------------------///////////////////////");
    const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/admin/brands/${id}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('superAdminAuthToken')}`
        },
        body: JSON.stringify(senitaizedData),
    });

    const data = await response.json();

    return data;
};

export default function BrandFroms({ isAddMode, isUpdateMode }) {
    // console.log({ isAddMode }, { isUpdateMode });

    const [brand, setBrand] = useState({});
    const [fetchBrandData, setFetchBrandData] = useState({});
    const [errors, setErrors] = useState({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const pathname = usePathname();
    const [, , , , id] = pathname.split('/');

    useEffect(() => {
        const fetchBrand = async function () {
            const data = await getBrand(id);
            console.log(data);

            if (data.success) {
                console.log(data);
                isUpdateMode && setFetchBrandData(data.data.brand); // Ensure this line is executed
                // Create a deep copy of the brand data using JSON parse/stringify
                isUpdateMode && setBrand(JSON.parse(JSON.stringify(data.data.brand)));

            } else {
                console.error('Error:', data.message);
            }
        };

        console.log(id, isUpdateMode); // must be a number and isUpdateMode is true
        if (typeof Number(id) === "number" && isUpdateMode) { // Convert id to a number and check if it's not NaN
            fetchBrand();
        }

        console.log(brand);
    }, [id, isUpdateMode]); // Add isUpdateMode to the dependency array

    const validateForm = () => {
        const newErrors = {};

        // Name validation
        if (!brand.name || brand.name.trim() === '') {
            newErrors.name = 'Brand name is required';
        } else if (brand.name.length < 2) {
            newErrors.name = 'Brand name must be at least 2 characters';
        } else if (brand.name.length > 50) {
            newErrors.name = 'Brand name cannot exceed 50 characters';
        }

        // Slug validation - optional but must follow pattern if provided
        if (brand.slug && brand.slug.trim() !== '' && !/^[a-z0-9-]+$/.test(brand.slug)) {
            newErrors.slug = 'Slug can only contain lowercase letters, numbers, and hyphens';
        }

        // Website validation (optional field)
        if (brand.website && !isValidUrl(brand.website)) {
            newErrors.website = 'Please enter a valid URL';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const isValidUrl = (url) => {
        try {
            new URL(url);
            return true;
        } catch (e) {
            return false;
        }
    };

    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        setBrand((prevBrand) => ({
            ...prevBrand,
            [name]: type === 'checkbox' ? checked : value,
        }));

        // Clear error for this field when user starts typing
        if (errors[name]) {
            setErrors({
                ...errors,
                [name]: undefined
            });
        }
    };

    // task! create a brand (post) | review
    async function handleAddBrand(data) {
        setIsSubmitting(true);

        try {
            const senitaizedData = {
                name: brand.name,
                description: brand.description,
                website: brand.website,
                is_featured: brand.is_featured,
                is_active: brand.is_active,
                logo: brand.logo,
                slug: brand.slug,
            };

            const response = await postBrand(senitaizedData);
            console.log(response);

            if (response.success === true && response.data?.brand?.id) {
                Swal.fire({
                    title: 'Success!',
                    text: 'Brand added successfully',
                    icon: 'success',
                    confirmButtonText: 'OK'
                });
                // Reset form after successful submission
                setBrand({});
            } else {
                Swal.fire({
                    title: 'Error!',
                    text: response.message || 'Something went wrong! Brand not added',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        } catch (error) {
            console.error('Error adding brand:', error);
            Swal.fire({
                title: 'Error!',
                text: 'An unexpected error occurred',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    // task! update a brand (put) | review
    async function handleUpdateBrand(data) {
        setIsSubmitting(true);

        try {
            const senitaizedData = {
                name: brand.name,
                description: brand.description,
                website: brand.website,
                is_featured: brand.is_featured,
                is_active: brand.is_active,
                logo: brand.logo,
                slug: brand.slug,
            };

            const response = await putBrand(id, senitaizedData);
            console.log(response);

            if (response.success === true && response.data.brand.id) {
                Swal.fire({
                    title: 'Success!',
                    text: 'Brand updated successfully',
                    icon: 'success',
                    confirmButtonText: 'OK'
                });
            } else {
                Swal.fire({
                    title: 'Error!',
                    text: response.message || 'Something went wrong! Brand not updated',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        } catch (error) {
            console.error('Error updating brand:', error);
            Swal.fire({
                title: 'Error!',
                text: 'An unexpected error occurred',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleSubmitForm = (e) => {
        e.preventDefault();

        // Validate form before submission
        const isValid = validateForm();
        if (!isValid) {
            // Scroll to the first error
            const firstErrorField = Object.keys(errors)[0];
            const errorElement = document.querySelector(`[name="${firstErrorField}"]`);
            errorElement?.scrollIntoView({ behavior: 'smooth', block: 'center' });
            return;
        }

        isAddMode && handleAddBrand(brand);
        isUpdateMode && handleUpdateBrand(brand);
    };

    return (
        <section className='bg-white rounded-lg shadow-md p-6 max-w-3xl mx-auto text-gray-600'>
            <h2 className='text-2xl font-bold mb-6 text-orange-500 border-b pb-2 flex items-center'>
                {isAddMode ? (
                    <>
                        <FaPlus className="mr-2 text-green-500" /> Add New Brand
                    </>
                ) : (
                    <>
                        <FaEdit className="mr-2 text-blue-500" /> Update Brand
                    </>
                )}
            </h2>

            <form onSubmit={handleSubmitForm} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Left column */}
                    <div className="space-y-4">
                        {brand.id && (
                            <div className="mb-4">
                                <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                                    <FaTag className="mr-2 text-gray-500" /> ID (read only)
                                </label>
                                <input
                                    type="text"
                                    name="id"
                                    value={brand.id || ''}
                                    readOnly
                                    className="mt-1 block w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-md shadow-sm text-gray-500"
                                />
                            </div>
                        )}

                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                                <FaFont className="mr-2 text-blue-500" /> Name*
                            </label>
                            <input
                                type="text"
                                name="name"
                                value={brand.name || ''}
                                onChange={handleChange}
                                className={`mt-1 block w-full px-3 py-2 border ${errors.name ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500`}
                                required
                            />
                            {errors.name && (
                                <p className="mt-1 text-sm text-red-600 flex items-center">
                                    <FaExclamationTriangle className="mr-1" /> {errors.name}
                                </p>
                            )}
                        </div>

                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                                <FaTag className="mr-2 text-purple-500" /> Slug
                            </label>
                            <input
                                type="text"
                                name="slug"
                                value={brand.slug || ''}
                                onChange={handleChange}
                                className={`mt-1 block w-full px-3 py-2 border ${errors.slug ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500`}
                            />
                            {errors.slug && (
                                <p className="mt-1 text-sm text-red-600 flex items-center">
                                    <FaExclamationTriangle className="mr-1" /> {errors.slug}
                                </p>
                            )}
                            <p className="mt-1 text-xs text-gray-500">
                                Use lowercase letters, numbers, and hyphens only
                            </p>
                        </div>

                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                                <FaGlobe className="mr-2 text-green-500" /> Website
                            </label>
                            <input
                                type="url"
                                name="website"
                                value={brand.website || ''}
                                onChange={handleChange}
                                className={`mt-1 block w-full px-3 py-2 border ${errors.website ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500`}
                            />
                            {errors.website && (
                                <p className="mt-1 text-sm text-red-600 flex items-center">
                                    <FaExclamationTriangle className="mr-1" /> {errors.website}
                                </p>
                            )}
                        </div>

                        <div className="flex space-x-6">
                            <div className="flex items-center bg-yellow-50 p-2 rounded-md border border-yellow-200">
                                <input
                                    type="checkbox"
                                    id="is_featured"
                                    name="is_featured"
                                    checked={brand.is_featured || false}
                                    onChange={handleChange}
                                    className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                                />
                                <label htmlFor="is_featured" className="ml-2 block text-sm text-gray-700 flex items-center">
                                    <FaStar className="mr-1 text-yellow-500" /> Featured
                                </label>
                            </div>

                            <div className="flex items-center bg-green-50 p-2 rounded-md border border-green-200">
                                <input
                                    type="checkbox"
                                    id="is_active"
                                    name="is_active"
                                    checked={brand.is_active || false}
                                    onChange={handleChange}
                                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                                />
                                <label htmlFor="is_active" className="ml-2 block text-sm text-gray-700 flex items-center">
                                    <FaCheck className="mr-1 text-green-500" /> Active
                                </label>
                            </div>
                        </div>
                    </div>

                    {/* Right column */}
                    <div className="space-y-4">
                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                                <FaAlignLeft className="mr-2 text-indigo-500" /> Description
                            </label>
                            <textarea
                                name="description"
                                value={brand.description || ''}
                                onChange={handleChange}
                                rows="4"
                                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500"
                            ></textarea>
                        </div>

                        {brand.logo && (
                            <div className="mb-4">
                                <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                                    <FaImage className="mr-2 text-pink-500" /> Logo
                                </label>
                                <div className="mt-1 border border-gray-300 rounded-md p-2 bg-gray-50">
                                    <img
                                        src={brand.logo}
                                        alt={`${brand.name} logo`}
                                        className="h-24 w-auto object-contain mx-auto"
                                    />
                                </div>
                            </div>
                        )}

                        {isUpdateMode && (
                            <div className="space-y-4 bg-blue-50 p-4 rounded-md border border-blue-200">
                                <h3 className="text-sm font-medium text-gray-700 flex items-center">
                                    <FaInfoCircle className="mr-2 text-blue-500" /> Additional Information (read only)
                                </h3>

                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-xs text-gray-500 flex items-center">
                                            <FaUser className="mr-1 text-gray-400" /> Created By
                                        </label>
                                        <div className="text-sm">{brand.creator?.name || 'N/A'}</div>
                                    </div>

                                    <div>
                                        <label className="block text-xs text-gray-500">Email</label>
                                        <div className="text-sm">{brand.creator?.email || 'N/A'}</div>
                                    </div>

                                    <div>
                                        <label className="block text-xs text-gray-500 flex items-center">
                                            <FaCalendarAlt className="mr-1 text-gray-400" /> Created At
                                        </label>
                                        <div className="text-sm">{brand.created_at ? new Date(brand.created_at).toLocaleDateString() : 'N/A'}</div>
                                    </div>

                                    <div>
                                        <label className="block text-xs text-gray-500 flex items-center">
                                            <FaCalendarAlt className="mr-1 text-gray-400" /> Updated At
                                        </label>
                                        <div className="text-sm">{brand.updated_at ? new Date(brand.updated_at).toLocaleDateString() : 'N/A'}</div>
                                    </div>

                                    <div>
                                        <label className="block text-xs text-gray-500">Product Count</label>
                                        <div className="text-sm">{brand.product_count || 0}</div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                <div className="pt-4 border-t flex justify-end border-orange-400">
                    {isAddMode && (
                        <button
                            type="submit"
                            disabled={isSubmitting}
                            className={`px-6 py-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold rounded-md shadow transition duration-150 ease-in-out flex items-center ${isSubmitting ? 'opacity-70 cursor-not-allowed' : ''}`}
                        >
                            {isSubmitting ? (
                                <>
                                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Processing...
                                </>
                            ) : (
                                <>
                                    <FaPlus className="h-5 w-5 mr-2" />
                                    Add Brand
                                </>
                            )}
                        </button>
                    )}

                    {isUpdateMode && (
                        <button
                            type="submit"
                            disabled={isSubmitting}
                            className={`px-6 py-2 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold rounded-md shadow transition duration-150 ease-in-out flex items-center ${isSubmitting ? 'opacity-70 cursor-not-allowed' : ''}`}
                        >
                            {isSubmitting ? (
                                <>
                                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Processing...
                                </>
                            ) : (
                                <>
                                    <FaEdit className="h-5 w-5 mr-2" />
                                    Update Brand
                                </>
                            )}
                        </button>
                    )}
                </div>
            </form>
        </section>
    );
}
