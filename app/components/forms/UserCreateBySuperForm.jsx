"use client";

import { usePathname } from 'next/navigation';
import React, { useEffect, useState } from 'react'
import Swal from 'sweetalert2';
import { <PERSON>a<PERSON>ser, FaEnvelope, FaUserTag, FaLock, FaLockOpen, FaPhone, FaMapMarkerAlt } from 'react-icons/fa';
import { MdSecurity, MdContactMail, MdAccountBox, MdAdminPanelSettings } from 'react-icons/md';
import { BsFillPersonVcardFill, BsCalendarCheck, BsCalendarX } from 'react-icons/bs';
import { IoMdTime } from 'react-icons/io';
import { RiAdminFill, RiUserSettingsFill, RiTeamFill } from 'react-icons/ri';
import { AiFillCheckCircle } from 'react-icons/ai';

/* 
{
    "id": 4,
    "name": "Updated Admin Name",
    "email": "<EMAIL>",
    "username": "admin_user",
    "email_verified_at": null,
    "phone": "************",
    "address": "123 Admin St, Admin City",
    "profile_image": null,
    "role_id": 2,
        "is_active": true,
        "created_at": "2025-05-12T19:38:07.000000Z",
        "updated_at": "2025-05-19T13:15:12.000000Z",
        "role": {
            "id": 2,
            "name": "Admin", 
            "slug": "admin",
            "description": "Has access to manage most aspects of the system",
            "created_at": "2025-05-12T03:10:24.000000Z",
            "updated_at": "2025-05-12T03:10:24.000000Z"
            }
            },
            */

// any kind of user can be created and updated from this component
export default function UserCreatedBySuperForm({ isAddMode, isUpdateMode, superAdmin, admin, supervisor, partner, user }) {

    const initialFormData = {
        // Basic Info
        name: "",
        email: "",
        username: "",  // Changed from null to ""

        // Authentication
        password: "",
        password_confirmation: "",
        email_verified_at: null,  // Keep as null since it's a timestamp

        // Contact Info
        phone: "",  // Changed from null to ""
        address: "", // Changed from null to ""

        // Profile
        profile_image: "",  // Changed from null to ""

        // Role & Status
        role_id: 2,
        is_active: true,

        // Timestamps
        created_at: null,  // Keep as null since it's a timestamp
        updated_at: null   // Keep as null since it's a timestamp
    };

    const [formData, setFormData] = useState(initialFormData);

    const pathname = usePathname();
    const [, userType, , , id,] = pathname.split("/");

    // Fetch user data when in update mode and ID is available
    useEffect(() => {
        const fetchUserData = async () => {
            if (isUpdateMode && id) {
                try {
                    const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/admin/users/${id}`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${localStorage.getItem("superAdminAuthToken")}`
                        }
                    });

                    const result = await response.json();
                    console.log(result);

                    if (result.success === true && result.data.user.id === parseInt(id)) {
                        // Update form data with fetched user data
                        console.log("from it");

                        setFormData({
                            name: result.data.user.name || "",
                            email: result.data.user.email || "",
                            username: result.data.user.username || "",
                            phone: result.data.user.phone || "",
                            address: result.data.user.address || "",
                            profile_image: result.data.user.profile_image || "",
                            role_id: result.data.user.role_id || 2,
                            is_active: result.data.user.is_active ?? true,
                            created_at: result.data.user.created_at || "",
                            updated_at: result.data.user.updated_at || "",
                            password: "",
                            password_confirmation: "",
                            email_verified_at: result.data.user.email_verified_at || ""
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: result.message || 'Failed to fetch user data',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                } catch (error) {
                    console.error('Error fetching user data:', error);
                    Swal.fire({
                        title: 'Error!',
                        text: 'Failed to fetch user data',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            }
        };

        if (isUpdateMode && id) fetchUserData();
    }, [isUpdateMode, id]);

    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));
    };

    // create new admin by superadmin (post) | review
    async function handleAddUser(userData) {
        const sanitizedData = {
            name: userData.name,
            email: userData.email,
            password: userData.password,
            password_confirmation: userData.password_confirmation,
            role_id: userData.role_id,
            is_active: userData.is_active,
            phone: userData.phone,
            address: userData.address,
        };
        console.log(sanitizedData);

        const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/admin/users`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem("superAdminAuthToken")}`
            },
            body: JSON.stringify(sanitizedData)
        });

        const result = await response.json();
        console.log(result);
        if (result.success === true) {
            Swal.fire({
                title: 'Success!',
                text: 'User added successfully!',
                icon: 'success',
                confirmButtonText: 'OK'
            });
        } else if (result.success === false) {
            if (result.success === false && result.errors) {
                const errorMessages = Object.values(result.errors)
                    .flat()
                    .join('\n');

                Swal.fire({
                    title: result.message || 'Error!',
                    text: errorMessages,
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            } else {
                Swal.fire({
                    title: 'Error!',
                    text: 'User creation failed!',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        }
    }

    async function handleUpdateUser(userData) {
        const sanitizedData = {
            name: userData.name,
            email: userData.email,
            password: userData.password,
            password_confirmation: userData.password_confirmation,
            role_id: userData.role_id,
            is_active: userData.is_active,
            phone: userData.phone,
            address: userData.address,
        };
        console.log(sanitizedData);

        const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/admin/users/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem("superAdminAuthToken")}`
            },
            body: JSON.stringify(sanitizedData)
        });

        const result = await response.json();
        console.log(result);

        if (result.success === true && result.data.user.id === parseInt(id)) {
            Swal.fire({
                title: 'Success!',
                text: 'User updated successfully!',
                icon: 'success',
                confirmButtonText: 'OK'
            });
        } else if (result.success === false) {
            if (result.errors) {
                const errorMessages = Object.values(result.errors)
                    .flat()
                    .join('\n');

                Swal.fire({
                    title: result.message || 'Error!',
                    text: errorMessages,
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            } else {
                Swal.fire({
                    title: 'Error!',
                    text: result.message || 'User update failed!',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        }
    }

    function validateForm() {


    }

    function handleSubmit(event) {
        event.preventDefault();

        // task!!! need to add validation and setErrors.

        if (isAddMode) {
            handleAddUser(formData);
        }
        if (isUpdateMode) {
            handleUpdateUser(formData);
        }
    };

    return (
        <section className="p-8 bg-white rounded-xl shadow-2xl max-w-4xl mx-auto text-gray-600">
            <form onSubmit={handleSubmit} className="space-y-8">
                {/* Basic Info Section */}
                <div className="space-y-6">
                    <h3 className="text-xl font-bold text-gray-900 border-b pb-2 flex items-center gap-2">
                        <BsFillPersonVcardFill className="text-blue-500" />
                        Basic Information
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="transition-all duration-200 hover:transform hover:scale-[1.02] relative">
                            <FaUser className="absolute top-9 left-3 text-gray-400" />
                            <label className="block text-sm font-semibold text-gray-700 mb-1">Name</label>
                            <input
                                type="text"
                                name="name"
                                value={formData.name}
                                onChange={handleChange}
                                className="mt-1 pl-10 block w-full rounded-lg border border-gray-300 px-4 py-3 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                            />
                        </div>
                        <div className="transition-all duration-200 hover:transform hover:scale-[1.02] relative">
                            <FaEnvelope className="absolute top-9 left-3 text-gray-400" />
                            <label className="block text-sm font-semibold text-gray-700 mb-1">Email</label>
                            <input
                                type="email"
                                name="email"
                                value={formData.email}
                                onChange={handleChange}
                                className="mt-1 pl-10 block w-full rounded-lg border border-gray-300 px-4 py-3 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                            />
                        </div>
                        <div className="transition-all duration-200 hover:transform hover:scale-[1.02] relative">
                            <FaUserTag className="absolute top-9 left-3 text-gray-400" />
                            <label className="block text-sm font-semibold text-gray-700 mb-1">Username</label>
                            <input
                                type="text"
                                name="username"
                                value={formData.username}
                                onChange={handleChange}
                                className="mt-1 pl-10 block w-full rounded-lg border border-gray-300 px-4 py-3 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                            />
                        </div>
                    </div>
                </div>

                {/* Authentication Section */}
                <div className="space-y-6">
                    <h3 className="text-xl font-bold text-gray-900 border-b pb-2 flex items-center gap-2">
                        <MdSecurity className="text-purple-500" />
                        Authentication
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="transition-all duration-200 hover:transform hover:scale-[1.02] relative">
                            <FaLock className="absolute top-9 left-3 text-gray-400" />
                            <label className="block text-sm font-semibold text-gray-700 mb-1">Password</label>
                            <input
                                type="password"
                                name="password"
                                value={formData.password}
                                onChange={handleChange}
                                className="mt-1 pl-10 block w-full rounded-lg border border-gray-300 px-4 py-3 shadow-sm focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all duration-200"
                            />
                        </div>
                        <div className="transition-all duration-200 hover:transform hover:scale-[1.02] relative">
                            <FaLockOpen className="absolute top-9 left-3 text-gray-400" />
                            <label className="block text-sm font-semibold text-gray-700 mb-1">Confirm Password</label>
                            <input
                                type="password"
                                name="password_confirmation"
                                value={formData.password_confirmation}
                                onChange={handleChange}
                                className="mt-1 pl-10 block w-full rounded-lg border border-gray-300 px-4 py-3 shadow-sm focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all duration-200"
                            />
                        </div>
                    </div>
                </div>

                {/* Contact Info Section */}
                <div className="space-y-6">
                    <h3 className="text-xl font-bold text-gray-900 border-b pb-2 flex items-center gap-2">
                        <MdContactMail className="text-green-500" />
                        Contact Information
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="transition-all duration-200 hover:transform hover:scale-[1.02] relative">
                            <FaPhone className="absolute top-9 left-3 text-gray-400" />
                            <label className="block text-sm font-semibold text-gray-700 mb-1">Phone</label>
                            <input
                                type="tel"
                                name="phone"
                                value={formData.phone}
                                onChange={handleChange}
                                className="mt-1 pl-10 block w-full rounded-lg border border-gray-300 px-4 py-3 shadow-sm focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                            />
                        </div>
                        <div className="transition-all duration-200 hover:transform hover:scale-[1.02] relative">
                            <FaMapMarkerAlt className="absolute top-9 left-3 text-gray-400" />
                            <label className="block text-sm font-semibold text-gray-700 mb-1">Address</label>
                            <input
                                type="text"
                                name="address"
                                value={formData.address}
                                onChange={handleChange}
                                className="mt-1 pl-10 block w-full rounded-lg border border-gray-300 px-4 py-3 shadow-sm focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                            />
                        </div>
                    </div>
                </div>

                {/* Profile Section */}
                <div className="space-y-6">
                    <h3 className="text-xl font-bold text-gray-900 border-b pb-2 flex items-center gap-2">
                        <MdAccountBox className="text-red-500" />
                        Profile
                    </h3>
                </div>

                {/* Role & Status Section */}
                <div className="space-y-6">
                    <h3 className="text-xl font-bold text-gray-900 border-b pb-2 flex items-center gap-2">
                        <MdAdminPanelSettings className="text-amber-500" />
                        Role & Status
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="transition-all duration-200 hover:transform hover:scale-[1.02] relative">
                            <RiUserSettingsFill className="absolute top-9 left-3 text-gray-400" />
                            <label className="block text-sm font-semibold text-gray-700 mb-1">Role ID</label>
                            <select
                                name="role_id"
                                value={formData.role_id}
                                onChange={handleChange}
                                className="mt-1 pl-10 block w-full rounded-lg border border-gray-300 px-4 py-3 shadow-sm focus:border-amber-500 focus:ring-2 focus:ring-amber-200 transition-all duration-200"
                            >
                                <option value="1">Super Admin</option>
                                <option value="2">Admin</option>
                                <option value="3">Supervisor</option>
                                <option value="4">Partner</option>
                                <option value="5">User(Regular Buyer)</option>
                            </select>
                        </div>
                        <div className="flex items-center">
                            <label className="flex items-center cursor-pointer gap-2">
                                <input
                                    type="checkbox"
                                    name="is_active"
                                    checked={formData.is_active}
                                    onChange={handleChange}
                                    className="w-5 h-5 rounded border-gray-300 text-amber-600 shadow-sm focus:border-amber-500 focus:ring-2 focus:ring-amber-200 transition-all duration-200"
                                />
                                <AiFillCheckCircle className="text-amber-500" />
                                <span className="text-sm font-medium text-gray-700">Active Status</span>
                            </label>
                        </div>
                    </div>

                    {/* read only */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                        <div className="transition-all duration-200 hover:transform hover:scale-[1.02] bg-gray-50 p-4 rounded-lg">
                            <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                                <BsCalendarCheck className="text-teal-500" />
                                Created at:
                            </label>
                            <p className="text-gray-600 font-medium flex items-center gap-2">
                                <IoMdTime className="text-teal-400" />
                                {formData.created_at || "Not created yet"} (read only)
                            </p>
                        </div>
                        <div className="transition-all duration-200 hover:transform hover:scale-[1.02] bg-gray-50 p-4 rounded-lg">
                            <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                                <BsCalendarX className="text-indigo-500" />
                                Updated at:
                            </label>
                            <p className="text-gray-600 font-medium flex items-center gap-2">
                                <IoMdTime className="text-indigo-400" />
                                {formData.updated_at || "No updates yet"} (read only)
                            </p>
                        </div>
                    </div>
                </div>

                {/* Submit Button */}
                <div className="flex justify-end pt-4">
                    <button
                        type="submit"
                        className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-lg font-semibold rounded-lg shadow-lg hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-4 focus:ring-purple-200 transform transition-all duration-200 hover:scale-[1.02] active:scale-[0.98] flex items-center gap-2"
                    >
                        {isAddMode && (
                            <>
                                <RiAdminFill className="text-xl" />
                                Create Admin
                            </>
                        )}
                        {isUpdateMode && (
                            <>
                                <RiTeamFill className="text-xl" />
                                Update Admin
                            </>
                        )}
                    </button>
                </div>
            </form>
        </section>
    );
}
