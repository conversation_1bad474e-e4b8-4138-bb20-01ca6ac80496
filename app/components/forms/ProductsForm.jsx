'use client';

import React, { useEffect, useState } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import contentUpload from '@/app/utils/contentUpload';
import Swal from 'sweetalert2';

const API_BASE_URL = 'https://b2b.instinctfusionx.xyz/public/api/v1';

// task!!!! upload products in bulk (post)
export default function ProductsForm({ isAddMode, isUpdateMode }) {
    const router = useRouter()
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [error, setError] = useState(null)
    const [success, setSuccess] = useState(null)
    const [isUploading, setIsUploading] = useState(false)
    const [products, setProducts] = useState([{
        name: '',
        sku: '',
        brand_id: '',
        category_id: '',
        description: '',
        currency_code: 'AUD',
        number_of_products: 1,
        per_pack_price: '',
        per_pack_special_price: '',
        customer_margin: '',
        partner_margin: '',
        customer_margin_type: 'percentage',
        partner_margin_type: 'percentage',
        delivery_fee: '',
        min_order_quantity: 1,
        min_order_value: '',
        free_shipping_threshold: '',
        products_per_pallet: '',
        pallet_delivery_fee: '',
        add_bulk_prices: false,
        bulk_prices: [],
        default_customer_margin: '',
        default_partner_margin: '',
        default_customer_margin_type: 'percentage',
        default_partner_margin_type: 'percentage',
        quantity: '',
        meta_title: '',
        meta_description: '',
        meta_keywords: '',
        images: []
    }]);
    const [categoriesOptions, setCategoriesOptions] = useState([]);
    const [brandsOptions, setBrandsOptions] = useState([]);

    useEffect(() => {
        getCategories();
        getBrands();
    }, [])

    const pathname = usePathname();
    const [, userType, , , id] = pathname.split('/');
    // console.log(userType, id);

    useEffect(() => {

    }, [])

    async function getCategories() {
        const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/categories`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                // 'Authorization': `Bearer ${adminType === "super-admin" && localStorage.getItem('superAdminAuthToken') || adminType === "partner-admin" && localStorage.getItem('partnerAuthToken')}`
            }
        });
        const data = await response.json();
        // console.log(data.data.categories.data);

        if (data.success === true && data.data.categories.data.length > 0) {
            const categories = data.data.categories.data.map((category, index) => {
                // console.log(index);
                return {
                    name: category.name,
                    value: category.id,
                };
            });
            // console.log(categories);
            setCategoriesOptions([...categories]);
        }

        return data.data.categories;
    }

    async function getBrands() {
        const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/brands`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                // 'Authorization': `Bearer ${adminType === "super-admin" && localStorage.getItem('superAdminAuthToken') || adminType === "partner-admin" && localStorage.getItem('partnerAuthToken')}`
            }
        });
        const data = await response.json();
        // console.log(data);
        // console.log(data.data.brands.data);

        if (data.success === true && data.data.brands.data.length > 0) {
            const brands = data.data.brands.data.map((brand, index) => {
                // console.log(brand.name);
                return {
                    name: brand.name,
                    value: brand.id,
                };
            });
            // console.log(brands);
            setBrandsOptions([...brands]);
        }

        return data.data.brands;
    }


    const handleImageUpload = async (index, event) => {
        const files = event.target.files;
        console.log(files);

        if (files) {
            setIsUploading(true);
            setError(null);
            try {
                const uploadedImages = await contentUpload.MultipleImageUpload(files, "partnerAuthToken");
                console.log(uploadedImages);
                if (uploadedImages.length > 0) {
                    const updatedProducts = [...products];
                    updatedProducts[index].images = [...updatedProducts[index].images, ...uploadedImages.map(url => `https://b2b.instinctfusionx.xyz${url}}`)];
                    setProducts(updatedProducts);
                }
            } catch (error) {
                setError(error.message);
            } finally {
                setIsUploading(false);
            }
        }
    };

    const removeImage = (productIndex, imageIndex) => {
        const updatedProducts = [...products];
        updatedProducts[productIndex].images.splice(imageIndex, 1);
        setProducts(updatedProducts);
    };

    const handleSubmit = async (e) => {
        e.preventDefault()
        setIsSubmitting(true)
        setError(null)
        setSuccess(null)

        try {
            // Validate required fields
            const invalidProducts = products.filter(product =>
                !product.name ||
                !product.sku ||
                !product.brand_id ||
                !product.category_id ||
                !product.description ||
                !product.per_pack_price ||
                !product.quantity ||
                !product.images ||
                product.images.length === 0
            );

            if (invalidProducts.length > 0) {
                throw new Error('Please fill in all required fields and upload at least one image for each product');
            }

            // Get the token
            const token = localStorage.getItem('partnerAuthToken');
            if (!token) {
                throw new Error('Authentication token not found. Please log in again.');
            }

            // Format the products data
            const formattedProducts = products.map(product => ({
                name: product.name,
                sku: product.sku,
                brand_id: parseInt(product.brand_id),
                category_id: parseInt(product.category_id),
                description: product.description,
                currency_code: product.currency_code,
                number_of_products: parseInt(product.number_of_products),
                per_pack_price: parseFloat(product.per_pack_price),
                per_pack_special_price: product.per_pack_special_price ? parseFloat(product.per_pack_special_price) : null,
                customer_margin: product.customer_margin ? parseFloat(product.customer_margin) : null,
                partner_margin: product.partner_margin ? parseFloat(product.partner_margin) : null,
                customer_margin_type: product.customer_margin_type,
                partner_margin_type: product.partner_margin_type,
                delivery_fee: product.delivery_fee ? parseFloat(product.delivery_fee) : null,
                min_order_quantity: parseInt(product.min_order_quantity),
                min_order_value: product.min_order_value ? parseFloat(product.min_order_value) : null,
                free_shipping_threshold: product.free_shipping_threshold ? parseFloat(product.free_shipping_threshold) : null,
                products_per_pallet: product.products_per_pallet ? parseInt(product.products_per_pallet) : null,
                pallet_delivery_fee: product.pallet_delivery_fee ? parseFloat(product.pallet_delivery_fee) : null,
                add_bulk_prices: product.add_bulk_prices,
                bulk_prices: product.bulk_prices,
                default_customer_margin: product.default_customer_margin ? parseFloat(product.default_customer_margin) : null,
                default_partner_margin: product.default_partner_margin ? parseFloat(product.default_partner_margin) : null,
                default_customer_margin_type: product.default_customer_margin_type,
                default_partner_margin_type: product.default_partner_margin_type,
                quantity: parseInt(product.quantity),
                meta_title: product.meta_title,
                meta_description: product.meta_description,
                meta_keywords: product.meta_keywords,
                images: product.images
            }));

            // Log the request payload
            console.log('Request Payload:', {
                products: formattedProducts
            });

            const response = await fetch(`${API_BASE_URL}/vendor/products/bulk-import`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                    'Accept': 'application/json'
                },
                body: JSON.stringify({ products: formattedProducts })
            });

            // Log response details
            console.log('Response Status:', response.status);
            console.log('Response Headers:', Object.fromEntries(response.headers.entries()));

            const responseText = await response.text();
            console.log('Raw Response:', responseText);

            let data;
            try {
                data = JSON.parse(responseText);
                console.log('Parsed Response:', data);
            } catch (parseError) {
                console.error('Parse Error:', parseError);
                throw new Error('Invalid response from server');
            }

            if (!response.ok) {
                if (data.message === 'Unauthenticated') {
                    window.location.href = '/login';
                    throw new Error('Session expired. Please log in again.');
                }
                throw new Error(data.message || 'Failed to import products');
            }

            if (data.success === true) {
                setSuccess({
                    message: data.message,
                    successCount: data.data.success_count,
                    failedCount: data.data.failed_count,
                    importedProducts: data.data.imported_products
                });

                // Wait for 2 seconds to show the success message before redirecting
                setTimeout(() => {
                    router.push('/partner-admin/products');
                }, 2000);
            } else if (data.success === false && data.errors) {
                // Format validation errors for display
                let errorMessage = '<ul style="text-align: left; list-style-type: none; padding-left: 0;">';
                for (const field in data.errors) {
                    errorMessage += `<li><strong>${field.replace('_', ' ')}:</strong> ${data.errors[field].join(', ')}</li>`;
                }
                errorMessage += '</ul>';
                throw new Error(errorMessage);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: data.message,
                    confirmButtonText: 'OK'
                });

                throw new Error(data.message || 'Failed to import products');
            }
        } catch (error) {
            console.error('Submission Error:', error);
            setError(error.message);

            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: error.message,
                confirmButtonText: 'OK'
            });
        } finally {
            setIsSubmitting(false);
        }
    }

    const addProduct = () => {
        setProducts([...products, {
            name: '',
            sku: '',
            brand_id: '',
            category_id: '',
            description: '',
            currency_code: 'AUD',
            number_of_products: 1,
            per_pack_price: '',
            per_pack_special_price: '',
            customer_margin: '',
            partner_margin: '',
            customer_margin_type: 'percentage',
            partner_margin_type: 'percentage',
            delivery_fee: '',
            min_order_quantity: 1,
            min_order_value: '',
            free_shipping_threshold: '',
            products_per_pallet: '',
            pallet_delivery_fee: '',
            add_bulk_prices: false,
            bulk_prices: [],
            default_customer_margin: '',
            default_partner_margin: '',
            default_customer_margin_type: 'percentage',
            default_partner_margin_type: 'percentage',
            quantity: '',
            meta_title: '',
            meta_description: '',
            meta_keywords: '',
            images: []
        }])
    }

    const handleProductChange = (index, field, value) => {
        console.log(index, field, value);

        const updatedProducts = [...products]
        updatedProducts[index][field] = value
        setProducts(updatedProducts)
    }

    return (
        <div className="p-6 bg-white text-gray-600">
            <h1 className="text-2xl font-bold mb-6 text-gray-900">Multiple Product Upload</h1>

            {error && (
                <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                    {error}
                </div>
            )}

            {success && (
                <div className="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
                    <p className="font-semibold">{success.message}</p>
                    <p className="mt-2">Successfully imported {success.successCount} products</p>
                    {success.failedCount > 0 && (
                        <p className="text-yellow-700">Failed to import {success.failedCount} products</p>
                    )}
                    {success.importedProducts.length > 0 && (
                        <div className="mt-2">
                            <p className="font-semibold">Imported Products:</p>
                            <ul className="list-disc list-inside">
                                {success.importedProducts.map((product, index) => (
                                    <li key={index}>
                                        {product.name} (SKU: {product.sku})
                                    </li>
                                ))}
                            </ul>
                        </div>
                    )}
                </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
                {products.map((product, index) => (
                    <div key={index} className="border border-gray-200 p-6 rounded-lg space-y-4 bg-white shadow-sm">
                        <h2 className="text-xl font-semibold text-gray-900">Product {index + 1}</h2>

                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Name</label>
                                <input
                                    type="text"
                                    value={product.name}
                                    onChange={(e) => handleProductChange(index, 'name', e.target.value)}
                                    className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 py-3 px-4"
                                    required
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700">SKU</label>
                                <input
                                    type="text"
                                    value={product.sku}
                                    onChange={(e) => handleProductChange(index, 'sku', e.target.value)}
                                    className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 py-3 px-4"
                                    required
                                />
                            </div>

                            {/* <div>
                                <label className="block text-sm font-medium text-gray-700">Brand ID</label>
                                <input
                                    type="number"
                                    value={product.brand_id}
                                    onChange={(e) => handleProductChange(index, 'brand_id', e.target.value)}
                                    className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 py-3 px-4"
                                    required
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700">Category ID</label>
                                <input
                                    type="number"
                                    value={product.category_id}
                                    onChange={(e) => handleProductChange(index, 'category_id', e.target.value)}
                                    className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 py-3 px-4"
                                    required
                                />
                            </div> */}

                            <div>
                                <label className="block text-sm font-medium mb-1">Brand</label>
                                <select
                                    name="brand_id"
                                    value={product.brand_id}
                                    onChange={(e) => handleProductChange(index, 'brand_id', e.target.value)}
                                    className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                >
                                    <option value="">Select Brands</option>
                                    {brandsOptions.map((brand, index) => (
                                        <option key={index} value={brand.value}>{brand.name}</option>
                                    ))}
                                </select>
                            </div>

                            <div>
                                <label className="block text-sm font-medium mb-1">Category</label>
                                <select
                                    name="category_id"
                                    value={product.category_id}
                                    onChange={(e) => handleProductChange(index, 'category_id', e.target.value)}
                                    className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-400"
                                >
                                    <option value="">Select Category</option>
                                    {categoriesOptions.map((category, index) => (
                                        <option key={index} value={category.value}>{category.name}</option>
                                    ))}
                                </select>
                            </div>


                            <div className="col-span-2">
                                <label className="block text-sm font-medium text-gray-700">Description</label>
                                <textarea
                                    value={product.description}
                                    onChange={(e) => handleProductChange(index, 'description', e.target.value)}
                                    className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 py-3 px-4"
                                    rows="3"
                                    required
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700">Per Pack Price</label>
                                <input
                                    type="number"
                                    step="0.01"
                                    value={product.per_pack_price}
                                    onChange={(e) => handleProductChange(index, 'per_pack_price', e.target.value)}
                                    className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 py-3 px-4"
                                    required
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700">Per Pack Special Price</label>
                                <input
                                    type="number"
                                    step="0.01"
                                    value={product.per_pack_special_price}
                                    onChange={(e) => handleProductChange(index, 'per_pack_special_price', e.target.value)}
                                    className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 py-3 px-4"
                                    required
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700">Delivery Fee</label>
                                <input
                                    type="number"
                                    step="0.01"
                                    value={product.delivery_fee}
                                    onChange={(e) => handleProductChange(index, 'delivery_fee', e.target.value)}
                                    className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 py-3 px-4"
                                    required
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700">Quantity</label>
                                <input
                                    type="number"
                                    value={product.quantity}
                                    onChange={(e) => handleProductChange(index, 'quantity', e.target.value)}
                                    className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 py-3 px-4"
                                    required
                                />
                            </div>

                            {/* Image Upload Section */}
                            <div className="col-span-2">
                                <label className="block text-sm font-medium text-gray-700 mb-2">Product Images</label>
                                <div className="space-y-4">
                                    <div className="flex items-center justify-center w-full">
                                        <label className={`flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}>
                                            <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                                <svg className="w-8 h-8 mb-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2" />
                                                </svg>
                                                <p className="mb-2 text-sm text-gray-500">
                                                    {isUploading ? 'Uploading...' : (
                                                        <>
                                                            <span className="font-semibold">Click to upload</span> or drag and drop
                                                        </>
                                                    )}
                                                </p>
                                                <p className="text-xs text-gray-500">PNG, JPG or JPEG (MAX. 800x400px)</p>
                                            </div>
                                            <input
                                                type="file"
                                                className="hidden"
                                                multiple
                                                accept="image/*"
                                                onChange={(e) => handleImageUpload(index, e)}
                                                disabled={isUploading}
                                            />
                                        </label>
                                    </div>

                                    {/* super-admin */}
                                    {userType === 'super-admin' && <section>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700">Customer Margin (%)</label>
                                            <input
                                                type="number"
                                                value={product.customer_margin}
                                                onChange={(e) => handleProductChange(index, 'customer_margin', e.target.value)}
                                                className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 py-3 px-4"
                                                required
                                            />
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700">Partner Margin (%)</label>
                                            <input
                                                type="number"
                                                value={product.partner_margin}
                                                onChange={(e) => handleProductChange(index, 'partner_margin', e.target.value)}
                                                className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 py-3 px-4"
                                                required
                                            />
                                        </div>
                                    </section>}

                                    {/* Display uploaded images */}
                                    {product.images.length > 0 && (
                                        <div className="grid grid-cols-4 gap-4 mt-4">
                                            {product.images.map((image, imageIndex) => (
                                                <div key={imageIndex} className="relative">
                                                    <img
                                                        src={image}
                                                        alt={`Product ${index + 1} image ${imageIndex + 1}`}
                                                        className="w-full h-24 object-cover rounded-lg"
                                                    />
                                                    <button
                                                        type="button"
                                                        onClick={() => removeImage(index, imageIndex)}
                                                        className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                                                    >
                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                                        </svg>
                                                    </button>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                ))}

                <div className="flex justify-between">
                    <button
                        type="button"
                        onClick={addProduct}
                        className="inline-flex items-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                        disabled={isSubmitting}
                    >
                        Add Another Product
                    </button>

                    <button
                        type="submit"
                        className="inline-flex items-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={isSubmitting}
                    >
                        {isSubmitting ? 'Uploading...' : 'Upload Products'}
                    </button>
                </div>
            </form>
        </div>
    )
}
