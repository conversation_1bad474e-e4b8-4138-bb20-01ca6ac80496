import React from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'

export default function PrimaryGradientBtn({ text, tailwindCss, navLink }) {
    // text: string, tailwindCss: string, navLink: string

    return (
        <button>
            <motion.div
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.97 }}
            >
                <Link
                    href={navLink ? navLink : '#'}
                    className={`group px-8 py-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-white font-semibold rounded-xl hover:to-yellow-300 hover:from-orange-500 transition duration-300 inline-flex items-center shadow-lg relative overflow-hidden group hover:shadow-red-500/30 ${tailwindCss}`}>
                    <span className="absolute right-0 w-8 h-32 -mt-12 transition-all duration-1000 transform translate-x-12 bg-white opacity-10 rotate-12 group-hover:-translate-x-20 ease"></span>
                    {text ? text : 'Title Please!'}
                    <svg className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                </Link>
            </motion.div>

            <style jsx>{`
        .perspective-effect {
          transform: perspective(1000px) rotateX(10deg) rotateY(-10deg);
        }
        .transform-3d {
          transform: perspective(1000px) rotateX(70deg);
        }
      `}</style>
        </button>
    )
}
