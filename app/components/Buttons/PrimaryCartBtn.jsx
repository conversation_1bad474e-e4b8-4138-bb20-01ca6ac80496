"use client";

import Link from 'next/link';
import React, { useState } from 'react'
import { BsBoxes } from 'react-icons/bs';
import { FaCartPlus, FaPallet } from 'react-icons/fa';
import { <PERSON>aEye, FaMinus, FaPlus } from 'react-icons/fa6';
import toast from 'react-hot-toast';
import { useCart } from '../../context/CartContext';
import AuthRequiredPopup from '../UI/AuthRequiredPopup';

export default function PrimaryCartBtn({ text, tailwindCss, cartPosition, product }) {
    const [quantity, setQuantity] = useState(0);
    const [showAuthPopup, setShowAuthPopup] = useState(false);
    const { incrementCartCount } = useCart();


    function handleQuantityChange(e) {
        console.log(e);

        // For manual input, we need e.target.value, not e
        const value = e.target?.value || e;

        // Handle increment/decrement buttons
        if (typeof e === 'number' && quantity > -1) {
            setQuantity(Math.max(0, quantity + e));
            return;
        }

        // Handle backspace and empty input
        if (value === '') {
            setQuantity('');
            return;
        }

        // Only allow integer numbers
        if (!/^\d+$/.test(value)) {
            return;
        }

        // Convert to integer and validate
        const newValue = parseInt(value);
        if (isNaN(newValue)) {
            return;
        }

        // Ensure minimum value is 1
        setQuantity(Math.max(1, newValue));
    };

    // Add to Cart Button
    async function handleAddToCart() {
        try {
            // Check if quantity is valid
            if (!quantity || quantity <= 0) {
                toast.error('Please select a valid quantity');
                return;
            }

            // Check authentication first
            const token = localStorage.getItem('userAuthToken') ||
                localStorage.getItem('partnerAuthToken') ||
                localStorage.getItem('superAdminAuthToken');

            if (!token) {
                setShowAuthPopup(true);
                return;
            }

            // Then check for cart
            const cartId = localStorage.getItem('cartId');
            if (!cartId) {
                toast.error('No cart found. Please try again later.');
                return;
            }

            const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/carts/${cartId}/items`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    product_id: product.id,
                    quantity: quantity,
                    is_bulk_pricing: false,
                    notes: ''
                })
            });

            const data = await response.json();
            console.log(data);

            if (data.success === true && data.data.cart.id) {
                toast.success(data.message || 'Product added to cart successfully!');
                // Update cart count in navbar
                incrementCartCount(quantity);
            } else {
                toast.error(data.message || 'Failed to add product to cart',
                    {
                        duration: 5000, // Duration in milliseconds (e.g., 5 seconds)
                    }
                );
            }
        } catch (error) {
            console.error('Error adding to cart:', error);
            toast.error('Failed to add product to cart', {
                duration: 5000, // Duration in milliseconds (e.g., 5 seconds)
            });
        }
    }

    return (
        <>
            <AuthRequiredPopup
                isOpen={showAuthPopup}
                onClose={() => setShowAuthPopup(false)}
            />
            <div className='space-y-4 w-full px-3 text-gray-600'>
            <div className='flex items-center justify-between rounded-xl bg-orange-50'>
                <button
                    className="bg-orange-400 hover:bg-orange-500 transition-colors duration-200 rounded-l-lg px-4 py-3 text-white"
                    onClick={() => handleQuantityChange(-1)}
                >
                    <FaMinus className='text-3xl' />
                </button>
                <input
                    type="text"
                    className="text-center w-full text-xl font-semibold text-orange-600 bg-transparent focus:outline-none"
                    value={quantity}
                    onChange={(e) => handleQuantityChange(e.target.value)}
                />
                <button
                    className="bg-orange-400 hover:bg-orange-500 transition-colors duration-200 rounded-r-lg px-4 py-3 text-white"
                    onClick={() => handleQuantityChange(1)}
                >
                    <FaPlus className='text-3xl' />
                </button>
            </div>

            <div>
                <p className='flex items-center border-b-3 py-2 border-orange-400'>
                    <BsBoxes
                        className='text-4xl text-indigo-600' />
                    <span
                        className='mx-2 text-gray-500 text-sm'>Buy Bulks</span>
                </p>

                <div className="grid grid-cols-2 items-center justify-between gap-3 py-3">
                    {!!product.min_order_quantity > 0 && <button
                        className="flex-1 p-2 rounded-lg bg-orange-100 hover:bg-orange-200 transition-colors duration-200 text-orange-600 font-medium hover:outline-1"
                        onClick={() => handleQuantityChange(parseInt(product.min_order_quantity))}
                    >
                        <p className='font-semibold'>Buy {parseInt(product.min_order_quantity)} Packs</p>
                        <p className=''>Per Pack ${
                            (
                                (
                                    parseFloat(product.per_pack_special_price || product.per_pack_price)
                                ) +
                                parseFloat((product.per_pack_special_price || product.per_pack_price) * parseFloat(product.customer_margin) / 100)
                            ).toFixed(2)
                        }</p>
                        {
                            product?.per_pack_special_price > 0 && <>
                                <span className="text-sm line-through text-gray-400">
                                    ${
                                        (
                                            parseFloat(product.per_pack_price) +
                                            parseFloat(product.per_pack_price) * parseFloat(product.customer_margin) / 100
                                        ).toFixed(2)
                                    }
                                </span>
                                <span className="text-sm font-medium px-2 py-1 bg-red-100 text-red-600 rounded inline-block">
                                    Save ${
                                        (
                                            (
                                                parseFloat(product.per_pack_price) - parseFloat(product.per_pack_special_price)
                                            ) + (
                                                (parseFloat(product.per_pack_price) - parseFloat(product.per_pack_special_price)) * parseFloat(product.customer_margin) / 100
                                            )
                                        ).toFixed(2)}
                                </span>
                            </>
                        }
                    </button>
                    }

                    {
                        (product.bulk_prices.length > 0 && product?.bulk_prices.map((bulkPrice, index) =>
                            <button key={index}
                                className="flex-1 p-2 rounded-lg bg-orange-100 hover:bg-orange-200 transition-colors duration-200 text-orange-600 font-medium hover:outline-1"
                                onClick={() => handleQuantityChange(parseInt(bulkPrice?.number_of_packs))}
                            >
                                <p className='font-semibold'>Buy {parseInt(bulkPrice?.number_of_packs)} Packs</p>
                                <p className=''>Per Pack ${
                                    (
                                        (
                                            parseFloat(bulkPrice.per_pack_special_price || bulkPrice.per_pack_price)
                                        ) +
                                        parseFloat((bulkPrice.per_pack_special_price || bulkPrice.per_pack_price) * parseFloat(bulkPrice.customer_margin) / 100)
                                    ).toFixed(2)
                                }</p>
                                {
                                    bulkPrice?.per_pack_special_price > 0 && <>
                                        <span className="text-sm line-through text-gray-400">
                                            ${
                                                (
                                                    parseFloat(bulkPrice.per_pack_price) +
                                                    parseFloat(bulkPrice.per_pack_price) * parseFloat(bulkPrice.customer_margin) / 100
                                                ).toFixed(2)
                                            }
                                        </span>
                                        <span className="text-sm font-medium px-2 py-1 bg-red-100 text-red-600 rounded inline-block">
                                            Save ${
                                                (
                                                    (
                                                        parseFloat(bulkPrice.per_pack_price) - parseFloat(bulkPrice.per_pack_special_price)
                                                    ) + (
                                                        (parseFloat(bulkPrice.per_pack_price) - parseFloat(bulkPrice.per_pack_special_price)) * parseFloat(bulkPrice.customer_margin) / 100
                                                    )
                                                ).toFixed(2)}
                                        </span>
                                    </>
                                }
                            </button>)) || (
                            <div className="w-full p-4 text-center bg-orange-50 rounded-lg">
                                <p className="text-orange-600 font-medium">No Bulk Offers Available</p>
                            </div>
                        )
                    }
                </div>
            </div>

            <div>
                <p className='flex items-center border-b-3 py-2 border-orange-400'>
                    <FaPallet
                        className='text-4xl text-purple-600' />
                    <span
                        className='mx-2 text-gray-500 text-sm'>Buy Pallets {product.pallet_delivery_fee === true ? `(Free Delivery)` : ``} </span>
                </p>
                {
                    product.products_per_pallet > 0 ?
                        <div className="grid grid-cols-2 items-center justify-between gap-3 py-3">
                            <button
                                className="flex-1 p-2 rounded-lg bg-orange-100 hover:bg-orange-200 transition-colors duration-200 text-orange-600 font-medium hover:outline-1"
                                onClick={() => handleQuantityChange(parseInt(product.products_per_pallet))}
                            >
                                <p className='font-semibold'>Buy {product.products_per_pallet} Packs</p>
                                {/* <p className=''>Per Pack ${100}</p> */}
                            </button>
                        </div> :
                        <div className="grid grid-cols-2 items-center justify-between gap-3 py-3">
                            <button
                                className="flex-1 p-2 rounded-lg bg-orange-100 hover:bg-orange-200 transition-colors duration-200 text-orange-600 font-medium hover:outline-1"
                            >
                                <p className='font-semibold'>Not Available</p>
                                {/* <p className=''>Per Pack ${100}</p> */}
                            </button>
                        </div>
                }
            </div>

            <div className='flex flex-col gap-5 '>
                <button className={`flex p-3 text-xl rounded-lg bg-orange-400 hover:bg-orange-500 transition-colors duration-200 font-semibold text-white items-center justify-center gap-3 w-full shadow-sm hover:shadow-md ${tailwindCss}`}
                    onClick={handleAddToCart}>
                    {cartPosition === 'left' && <FaCartPlus className="text-3xl" />}
                    {text || 'Add to Cart'}
                    {cartPosition === 'right' && <FaCartPlus className="text-3xl" />}
                </button>

                {product && (
                    <Link href={`/products/${product.id}`}>
                        <button className={`flex p-3 text-xl rounded-lg bg-gray-400 hover:bg-gray-500 transition-colors duration-200 font-semibold text-white items-center justify-center gap-3 w-full shadow-sm hover:shadow-md ${tailwindCss}`}>
                            <FaEye className="text-3xl" /> <span>View Details</span>
                        </button>
                    </Link>
                )}
            </div>
        </div>
        </>
    )
}
