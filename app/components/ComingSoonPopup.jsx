'use client';

import { useState, useEffect } from 'react';
import { FaTimes, FaRocket, FaStar, FaBell } from 'react-icons/fa';
import { MdUpdate, MdNotifications } from 'react-icons/md';

export default function ComingSoonPopup({ isOpen, onClose, featureName = "This feature" }) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
    }
  }, [isOpen]);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => {
      onClose();
    }, 300);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className={`absolute inset-0 bg-black transition-opacity duration-300 ${
          isVisible ? 'opacity-50' : 'opacity-0'
        }`}
        onClick={handleClose}
      />
      
      {/* Popup */}
      <div 
        className={`relative bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300 ${
          isVisible ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
        }`}
      >
        {/* Close Button */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors duration-200 z-10"
        >
          <FaTimes className="w-5 h-5" />
        </button>

        {/* Header with gradient background */}
        <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-t-2xl p-6 text-center">
          <div className="flex justify-center mb-4">
            <div className="bg-white bg-opacity-20 rounded-full p-3">
              <FaRocket className="w-8 h-8 text-white" />
            </div>
          </div>
          <h2 className="text-2xl font-bold text-white mb-2">
            Exciting Feature Coming Soon!
          </h2>
          <div className="flex justify-center space-x-1">
            {[...Array(5)].map((_, i) => (
              <FaStar key={i} className="w-4 h-4 text-yellow-300" />
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="text-center mb-6">
            <div className="flex justify-center mb-4">
              <div className="bg-orange-100 rounded-full p-3">
                <MdUpdate className="w-6 h-6 text-orange-600" />
              </div>
            </div>
            
            <h3 className="text-xl font-semibold text-gray-900 mb-3">
              {featureName} is Under Development
            </h3>
            
            <p className="text-gray-700 leading-relaxed mb-4">
              We're working hard to bring you amazing new features! This functionality will be available soon with our upcoming updates.
            </p>
            
            <div className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-4 mb-4">
              <div className="flex items-center justify-center mb-2">
                <FaBell className="w-5 h-5 text-orange-600 mr-2" />
                <span className="font-semibold text-orange-800">Stay Tuned!</span>
              </div>
              <p className="text-sm text-orange-700">
                New features will come gradually with next updates. We appreciate your patience!
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            <button
              onClick={handleClose}
              className="flex-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white py-3 px-6 rounded-lg font-semibold hover:from-orange-600 hover:to-orange-700 transition-all duration-300 flex items-center justify-center space-x-2"
            >
              <MdNotifications className="w-5 h-5" />
              <span>Got It!</span>
            </button>
            
            <button
              onClick={handleClose}
              className="flex-1 border-2 border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:border-orange-500 hover:text-orange-600 transition-all duration-300"
            >
              Close
            </button>
          </div>
        </div>

        {/* Decorative elements */}
        <div className="absolute -top-2 -right-2 w-4 h-4 bg-orange-400 rounded-full opacity-60"></div>
        <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-orange-300 rounded-full opacity-40"></div>
      </div>
    </div>
  );
}
