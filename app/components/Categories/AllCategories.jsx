'use client';

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import {
    FaTag,
    FaTh,
    Fa<PERSON>ist,
    FaSearch,
    FaFilter,
    FaSort,
    FaSortUp,
    FaSortDown,
    FaStar,
    FaBox,
    FaTimes
} from 'react-icons/fa';
import { HiSortAscending, HiSortDescending } from 'react-icons/hi';

export default function AllCategories({ categoriesData }) {
    // State Management
    const [searchQuery, setSearchQuery] = useState('');
    const [viewMode, setViewMode] = useState('grid');
    const [sortConfig, setSortConfig] = useState([
        { key: 'name', direction: 'asc' }
    ]);
    const [filters, setFilters] = useState({
        is_featured: '',
        is_active: '',
        has_products: ''
    });
    const [isLoading, setIsLoading] = useState(false);
    const [currentData, setCurrentData] = useState(categoriesData);

    // Extract categories data and pagination info
    const categories = currentData?.success ? currentData.data.categories.data : [];
    const paginationInfo = currentData?.success ? currentData.data.categories : null;

    // API call function for pagination and filtering
    const fetchCategoriesData = async (page = 1, perPage = 15, searchTerm = '', filterParams = {}) => {
        setIsLoading(true);
        try {
            // Build query parameters
            const params = new URLSearchParams({
                page: page.toString(),
                per_page: perPage.toString(),
            });

            if (searchTerm.trim()) {
                params.append('search', searchTerm.trim());
            }

            // Add filter parameters
            Object.entries(filterParams).forEach(([key, value]) => {
                if (value) {
                    params.append(key, value);
                }
            });

            const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/categories?${params.toString()}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            setCurrentData(data);
        } catch (error) {
            console.error('Error fetching categories:', error);
        } finally {
            setIsLoading(false);
        }
    };

    // Handle pagination
    const handlePageChange = (page) => {
        fetchCategoriesData(page, paginationInfo?.per_page || 15, searchQuery, filters);
    };

    // Handle search
    const handleSearch = (query) => {
        setSearchQuery(query);
        fetchCategoriesData(1, paginationInfo?.per_page || 15, query, filters);
    };

    // Handle filter changes
    const handleFilterChange = (filterKey, value) => {
        const newFilters = { ...filters, [filterKey]: value };
        setFilters(newFilters);
        fetchCategoriesData(1, paginationInfo?.per_page || 15, searchQuery, newFilters);
    };

    // Handle items per page change
    const handleItemsPerPageChange = (perPage) => {
        fetchCategoriesData(1, perPage, searchQuery, filters);
    };

    // Process data for display (server-side pagination, so just use the data as-is)
    const processedData = useMemo(() => {
        return {
            data: categories,
            totalItems: paginationInfo?.total || 0,
            totalPages: paginationInfo?.last_page || 1,
            currentPage: paginationInfo?.current_page || 1,
            perPage: paginationInfo?.per_page || 15,
            from: paginationInfo?.from || 0,
            to: paginationInfo?.to || 0
        };
    }, [categories, paginationInfo]);

    // Sort handler (for client-side sorting if needed)
    const handleSort = (key) => {
        setSortConfig(prev => {
            const existing = prev.find(sort => sort.key === key);
            if (existing) {
                return prev.map(sort =>
                    sort.key === key
                        ? { ...sort, direction: sort.direction === 'asc' ? 'desc' : 'asc' }
                        : sort
                );
            } else {
                return [...prev, { key, direction: 'asc' }];
            }
        });
    };

    // Clear all filters
    const clearFilters = () => {
        setFilters({
            is_featured: '',
            is_active: '',
            has_products: ''
        });
        setSearchQuery('');
        setSortConfig([{ key: 'name', direction: 'asc' }]);
        // Reset to first page with cleared filters
        fetchCategoriesData(1, paginationInfo?.per_page || 15, '', {
            is_featured: '',
            is_active: '',
            has_products: ''
        });
    };

    return (
        <div className="min-h-screen bg-gray-50 text-gray-600">
            {/* Header Section */}
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white shadow-sm border-b"
            >
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <div className="text-center">
                        <div className="relative">
                            <h1 className="text-5xl md:text-6xl font-extrabold bg-gradient-to-r from-gray-900 via-orange-600 to-gray-900 bg-clip-text text-transparent mb-6 leading-tight">
                                Product Categories
                            </h1>

                            {/* Enhanced decorative elements */}
                            <div className="flex items-center justify-center gap-3 mb-6">
                                <div className="h-0.5 w-12 bg-gradient-to-r from-transparent to-orange-400 rounded-full"></div>
                                <div className="h-2 w-2 bg-orange-400 rounded-full animate-pulse"></div>
                                <div className="h-0.5 w-24 bg-orange-400 rounded-full"></div>
                                <div className="h-2 w-2 bg-orange-400 rounded-full animate-pulse"></div>
                                <div className="h-0.5 w-12 bg-gradient-to-l from-transparent to-orange-400 rounded-full"></div>
                            </div>

                            <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium">
                                Discover our carefully curated collection of product categories designed to meet your business needs.
                                <span className="text-orange-500 font-semibold"> Browse, search, and find exactly what you're looking for.</span>
                            </p>

                            {/* Subtle background decoration */}
                            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 w-32 h-32 bg-orange-100 rounded-full opacity-20 blur-3xl -z-10"></div>
                        </div>

                        {/* Enhanced Stats Card */}
                        <div className="mt-8 relative max-w-md mx-auto">
                            <div className="bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600 rounded-3xl p-8 text-white shadow-2xl transform hover:scale-105 transition-all duration-300">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-orange-100 text-sm font-medium uppercase tracking-wider">Total Categories</p>
                                        <p className="text-4xl font-bold mt-2 bg-gradient-to-r from-white to-orange-100 bg-clip-text text-transparent">
                                            {processedData.totalItems}
                                        </p>
                                        <p className="text-orange-200 text-xs mt-1">Available for browsing</p>
                                    </div>
                                    <div className="relative">
                                        <div className="absolute inset-0 bg-white/20 rounded-full blur-xl"></div>
                                        <FaTag className="h-12 w-12 text-white/90 relative z-10" />
                                    </div>
                                </div>

                                {/* Decorative elements */}
                                <div className="absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full blur-2xl"></div>
                                <div className="absolute bottom-4 left-4 w-16 h-16 bg-orange-300/20 rounded-full blur-xl"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </motion.div>

            {/* Search and Filter Section */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                    className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-gray-100 p-8 mb-8"
                >
                    {/* Enhanced Search Bar */}
                    <div className="flex flex-col lg:flex-row gap-6 mb-8">
                        <div className="flex-1">
                            <label className="block text-sm font-semibold text-gray-700 mb-3">
                                Search Categories
                            </label>
                            <div className="relative group">
                                <div className="absolute inset-0 bg-gradient-to-r from-orange-400 to-orange-500 rounded-2xl blur opacity-25 group-hover:opacity-40 transition-opacity"></div>
                                <div className="relative">
                                    <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-hover:text-orange-500 transition-colors z-10" />
                                    <input
                                        type="text"
                                        placeholder="Search by name, ID, slug, or description..."
                                        value={searchQuery}
                                        onChange={(e) => handleSearch(e.target.value)}
                                        className="w-full pl-12 pr-12 py-4 bg-white border-2 border-gray-200 rounded-2xl focus:outline-none focus:ring-4 focus:ring-orange-100 focus:border-orange-400 transition-all duration-300 text-gray-700 placeholder-gray-400 shadow-sm"
                                        aria-label="Search categories"
                                    />
                                    {searchQuery && (
                                        <button
                                            onClick={() => handleSearch('')}
                                            className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500 transition-colors z-10 p-1 hover:bg-red-50 rounded-full"
                                            aria-label="Clear search"
                                        >
                                            <FaTimes />
                                        </button>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Enhanced View Toggle */}
                        <div className="flex flex-col gap-3">
                            <label className="text-sm font-semibold text-gray-700">
                                View Mode
                            </label>
                            <div className="flex items-center gap-1 bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl p-1.5 shadow-inner">
                                <button
                                    onClick={() => setViewMode('grid')}
                                    className={`p-3 rounded-xl transition-all duration-300 flex items-center gap-2 ${viewMode === 'grid'
                                        ? 'bg-gradient-to-r from-orange-400 to-orange-500 text-white shadow-lg transform scale-105'
                                        : 'text-gray-600 hover:bg-white hover:shadow-md'
                                        }`}
                                    aria-label="Grid view"
                                >
                                    <FaTh className="text-sm" />
                                    <span className="text-xs font-medium hidden sm:block">Grid</span>
                                </button>
                                <button
                                    onClick={() => setViewMode('list')}
                                    className={`p-3 rounded-xl transition-all duration-300 flex items-center gap-2 ${viewMode === 'list'
                                        ? 'bg-gradient-to-r from-orange-400 to-orange-500 text-white shadow-lg transform scale-105'
                                        : 'text-gray-600 hover:bg-white hover:shadow-md'
                                        }`}
                                    aria-label="List view"
                                >
                                    <FaList className="text-sm" />
                                    <span className="text-xs font-medium hidden sm:block">List</span>
                                </button>
                            </div>
                        </div>

                        {/* Enhanced Items per page */}
                        <div className="flex flex-col gap-3">
                            <label htmlFor="items-per-page" className="text-sm font-semibold text-gray-700">
                                Items per Page
                            </label>
                            <div className="relative">
                                <select
                                    id="items-per-page"
                                    value={processedData.perPage}
                                    onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
                                    className="appearance-none bg-white border-2 border-gray-200 rounded-xl px-4 py-3 pr-10 focus:outline-none focus:ring-4 focus:ring-orange-100 focus:border-orange-400 transition-all duration-300 text-gray-700 font-medium shadow-sm hover:shadow-md"
                                >
                                    <option value={12}>12 items</option>
                                    <option value={24}>24 items</option>
                                    <option value={48}>48 items</option>
                                    <option value={96}>96 items</option>
                                </select>
                                <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
                                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Enhanced Filters and Sort */}
                    <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl p-6 border border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                            <FaFilter className="text-orange-500" />
                            Filters & Sorting
                        </h3>

                        <div className="flex flex-col xl:flex-row gap-6 items-start xl:items-end">
                            {/* Enhanced Filters */}
                            <div className="flex flex-wrap gap-4">
                                <div className="flex flex-col gap-2">
                                    <label htmlFor="featured-filter" className="text-sm font-semibold text-gray-700">
                                        Featured Status
                                    </label>
                                    <div className="relative">
                                        <select
                                            id="featured-filter"
                                            value={filters.is_featured}
                                            onChange={(e) => handleFilterChange('is_featured', e.target.value)}
                                            className="appearance-none bg-white border-2 border-gray-200 rounded-xl px-4 py-2.5 pr-10 focus:outline-none focus:ring-4 focus:ring-orange-100 focus:border-orange-400 transition-all duration-300 text-gray-700 font-medium shadow-sm hover:shadow-md min-w-[140px]"
                                        >
                                            <option value="">All Categories</option>
                                            <option value="true">Featured Only</option>
                                            <option value="false">Not Featured</option>
                                        </select>
                                        <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
                                            <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>

                                <div className="flex flex-col gap-2">
                                    <label htmlFor="active-filter" className="text-sm font-semibold text-gray-700">
                                        Activity Status
                                    </label>
                                    <div className="relative">
                                        <select
                                            id="active-filter"
                                            value={filters.is_active}
                                            onChange={(e) => handleFilterChange('is_active', e.target.value)}
                                            className="appearance-none bg-white border-2 border-gray-200 rounded-xl px-4 py-2.5 pr-10 focus:outline-none focus:ring-4 focus:ring-orange-100 focus:border-orange-400 transition-all duration-300 text-gray-700 font-medium shadow-sm hover:shadow-md min-w-[140px]"
                                        >
                                            <option value="">All Status</option>
                                            <option value="true">Active Only</option>
                                            <option value="false">Inactive Only</option>
                                        </select>
                                        <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
                                            <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>

                                <div className="flex flex-col gap-2">
                                    <label htmlFor="products-filter" className="text-sm font-semibold text-gray-700">
                                        Product Availability
                                    </label>
                                    <div className="relative">
                                        <select
                                            id="products-filter"
                                            value={filters.has_products}
                                            onChange={(e) => handleFilterChange('has_products', e.target.value)}
                                            className="appearance-none bg-white border-2 border-gray-200 rounded-xl px-4 py-2.5 pr-10 focus:outline-none focus:ring-4 focus:ring-orange-100 focus:border-orange-400 transition-all duration-300 text-gray-700 font-medium shadow-sm hover:shadow-md min-w-[140px]"
                                        >
                                            <option value="">All Categories</option>
                                            <option value="true">With Products</option>
                                            <option value="false">Empty Categories</option>
                                        </select>
                                        <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
                                            <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Enhanced Sort Controls */}
                            <div className="flex flex-col gap-3">
                                <span className="text-sm font-semibold text-gray-700">Sort Options</span>
                                <div className="flex flex-wrap gap-2">
                                    {['name', 'product_count', 'slug', 'id'].map(key => {
                                        const sortItem = sortConfig.find(sort => sort.key === key);
                                        return (
                                            <button
                                                key={key}
                                                onClick={() => handleSort(key)}
                                                className={`px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 flex items-center gap-2 shadow-sm hover:shadow-md ${sortItem
                                                    ? 'bg-gradient-to-r from-orange-400 to-orange-500 text-white transform scale-105'
                                                    : 'bg-white text-gray-700 hover:bg-orange-50 border-2 border-gray-200 hover:border-orange-200'
                                                    }`}
                                            >
                                                <span className="capitalize">{key.replace('_', ' ')}</span>
                                                {sortItem && (
                                                    <div className="bg-white/20 rounded-full p-1">
                                                        {sortItem.direction === 'asc' ? <HiSortAscending className="text-xs" /> : <HiSortDescending className="text-xs" />}
                                                    </div>
                                                )}
                                            </button>
                                        );
                                    })}
                                </div>
                            </div>

                            {/* Enhanced Clear Filters */}
                            <div className="flex flex-col gap-3">
                                <span className="text-sm font-semibold text-gray-700">Reset</span>
                                <button
                                    onClick={clearFilters}
                                    className="px-6 py-2.5 bg-gradient-to-r from-red-400 to-red-500 text-white rounded-xl hover:from-red-500 hover:to-red-600 transition-all duration-300 text-sm font-medium flex items-center gap-2 shadow-sm hover:shadow-md transform hover:scale-105"
                                >
                                    <FaTimes />
                                    Clear All
                                </button>
                            </div>
                        </div>
                    </div>
                </motion.div>

                {/* Categories Grid */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="bg-white rounded-2xl shadow-lg p-6"
                >
                    {/* Results Info */}
                    <div className="flex justify-between items-center mb-6">
                        <h2 className="text-xl font-semibold text-gray-900">
                            Categories ({processedData.totalItems})
                        </h2>
                        {searchQuery && (
                            <p className="text-sm text-gray-600">
                                Showing results for "{searchQuery}"
                            </p>
                        )}
                    </div>

                    {/* Loading State */}
                    {isLoading && (
                        <div className="flex justify-center items-center py-12">
                            <div className="flex flex-col items-center gap-4">
                                <div className="w-12 h-12 border-4 border-orange-200 border-t-orange-500 rounded-full animate-spin"></div>
                                <p className="text-gray-600 font-medium">Loading categories...</p>
                            </div>
                        </div>
                    )}

                    {/* Categories Grid/List */}
                    {!isLoading && processedData.data.length > 0 ? (
                        <div className={`grid gap-6 mb-8 ${viewMode === 'grid'
                            ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                            : 'grid-cols-1'
                            }`}
                        >
                            <AnimatePresence>
                                {processedData.data.map((category, index) => (
                                    <motion.div
                                        key={category.id}
                                        initial={{ opacity: 0, scale: 0.9 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        exit={{ opacity: 0, scale: 0.9 }}
                                        transition={{ delay: index * 0.05 }}
                                        className={`bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 group ${viewMode === 'list' ? 'flex items-center p-4' : 'p-4'
                                            }`}
                                    >
                                        {viewMode === 'grid' ? (
                                            // Grid View
                                            <>
                                                {/* Category Image */}
                                                <div className="relative mb-4 aspect-square">
                                                    {category.image ? (
                                                        <Image
                                                            src={category.image}
                                                            alt={category.name}
                                                            fill
                                                            className="object-cover rounded-lg"
                                                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                                                        />
                                                    ) : (
                                                        <div className="w-full h-full bg-gray-200 rounded-lg flex items-center justify-center">
                                                            <FaTag className="text-gray-400 text-4xl" />
                                                        </div>
                                                    )}

                                                    {/* Featured Badge */}
                                                    {category.is_featured && (
                                                        <div className="absolute top-2 right-2 bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center gap-1">
                                                            <FaStar className="text-xs" />
                                                            Featured
                                                        </div>
                                                    )}
                                                </div>

                                                {/* Category Info */}
                                                <div className="space-y-3">
                                                    <div>
                                                        <h3 className="font-semibold text-gray-900 line-clamp-2 group-hover:text-orange-400 transition-colors text-lg">
                                                            {category.name}
                                                        </h3>
                                                        <p className="text-xs text-gray-500 mt-1">
                                                            ID: {category.id} • Slug: {category.slug}
                                                        </p>
                                                    </div>

                                                    {category.description && (
                                                        <p className="text-sm text-gray-600 line-clamp-2">
                                                            {category.description}
                                                        </p>
                                                    )}

                                                    {/* Stats */}
                                                    <div className="flex items-center justify-between">
                                                        <div className="flex items-center gap-2">
                                                            <FaBox className="text-gray-400 text-sm" />
                                                            <span className="text-sm text-gray-600">
                                                                {category.product_count} products
                                                            </span>
                                                        </div>
                                                        <div className="flex items-center gap-2">
                                                            <span className={`text-xs px-2 py-1 rounded-full ${category.is_active
                                                                ? 'bg-green-100 text-green-800'
                                                                : 'bg-red-100 text-red-800'
                                                                }`}>
                                                                {category.is_active ? 'Active' : 'Inactive'}
                                                            </span>
                                                        </div>
                                                    </div>

                                                    {/* Action Button */}
                                                    <Link
                                                        href={`/products/category/${category.id}?per_page=12`}
                                                        className="block w-full bg-orange-400 text-white px-4 py-3 rounded-lg text-sm font-medium hover:bg-orange-500 transition-colors text-center mt-4"
                                                    >
                                                        View Products
                                                    </Link>
                                                </div>
                                            </>
                                        ) : (
                                            // List View
                                            <>
                                                {/* Category Image */}
                                                <div className="relative w-20 h-20 flex-shrink-0 mr-4">
                                                    {category.image ? (
                                                        <Image
                                                            src={category.image}
                                                            alt={category.name}
                                                            fill
                                                            className="object-cover rounded-lg"
                                                            sizes="80px"
                                                        />
                                                    ) : (
                                                        <div className="w-full h-full bg-gray-200 rounded-lg flex items-center justify-center">
                                                            <FaTag className="text-gray-400 text-xl" />
                                                        </div>
                                                    )}
                                                </div>

                                                {/* Category Info */}
                                                <div className="flex-1 min-w-0">
                                                    <div className="flex justify-between items-start">
                                                        <div className="flex-1 min-w-0 mr-4">
                                                            <div className="flex items-center gap-2 mb-1">
                                                                <h3 className="font-semibold text-gray-900 truncate group-hover:text-orange-400 transition-colors">
                                                                    {category.name}
                                                                </h3>
                                                                {category.is_featured && (
                                                                    <span className="bg-orange-100 text-orange-600 px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                                                                        <FaStar className="text-xs" />
                                                                        Featured
                                                                    </span>
                                                                )}
                                                            </div>
                                                            <p className="text-xs text-gray-500 mb-1">
                                                                ID: {category.id} • Slug: {category.slug}
                                                            </p>
                                                            {category.description && (
                                                                <p className="text-sm text-gray-600 line-clamp-1">
                                                                    {category.description}
                                                                </p>
                                                            )}
                                                            <div className="flex items-center gap-4 mt-2">
                                                                <div className="flex items-center gap-1">
                                                                    <FaBox className="text-gray-400 text-xs" />
                                                                    <span className="text-xs text-gray-600">
                                                                        {category.product_count} products
                                                                    </span>
                                                                </div>
                                                                <span className={`text-xs px-2 py-1 rounded-full ${category.is_active
                                                                    ? 'bg-green-100 text-green-800'
                                                                    : 'bg-red-100 text-red-800'
                                                                    }`}>
                                                                    {category.is_active ? 'Active' : 'Inactive'}
                                                                </span>
                                                            </div>
                                                        </div>

                                                        <div className="text-right">
                                                            <Link
                                                                href={`/products/category/${category.id}?per_page=12`}
                                                                className="bg-orange-400 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-orange-500 transition-colors"
                                                            >
                                                                View Products
                                                            </Link>
                                                        </div>
                                                    </div>
                                                </div>
                                            </>
                                        )}
                                    </motion.div>
                                ))}
                            </AnimatePresence>
                        </div>
                    ) : !isLoading ? (
                        // No Results
                        <div className="text-center py-12">
                            <FaTag className="mx-auto text-gray-300 text-6xl mb-4" />
                            <h3 className="text-xl font-semibold text-gray-900 mb-2">No categories found</h3>
                            <p className="text-gray-600 mb-4">
                                {searchQuery
                                    ? `No categories match your search for "${searchQuery}"`
                                    : 'No categories match your current filters'
                                }
                            </p>
                            <button
                                onClick={clearFilters}
                                className="bg-orange-400 text-white px-6 py-2 rounded-lg hover:bg-orange-500 transition-colors"
                            >
                                Clear Filters
                            </button>
                        </div>
                    ) : null}

                    {/* API-based Pagination */}
                    {processedData.totalPages > 1 && (
                        <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-6 border-t border-gray-200">
                            <div className="text-gray-600 text-sm">
                                Showing {processedData.from} to {processedData.to} of {processedData.totalItems} categories
                                <span className="ml-2 text-gray-500">
                                    (Page {processedData.currentPage} of {processedData.totalPages})
                                </span>
                            </div>
                            <div className="flex gap-2">
                                {/* Previous Button */}
                                <button
                                    onClick={() => handlePageChange(processedData.currentPage - 1)}
                                    disabled={processedData.currentPage === 1 || isLoading}
                                    className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
                                >
                                    {isLoading && processedData.currentPage > 1 ? (
                                        <div className="w-4 h-4 border-2 border-gray-300 border-t-orange-400 rounded-full animate-spin"></div>
                                    ) : null}
                                    Previous
                                </button>

                                {/* Page Numbers */}
                                {Array.from({ length: Math.min(5, processedData.totalPages) }, (_, i) => {
                                    const pageNum = Math.max(1, Math.min(processedData.totalPages - 4, processedData.currentPage - 2)) + i;
                                    if (pageNum <= processedData.totalPages) {
                                        return (
                                            <button
                                                key={pageNum}
                                                onClick={() => handlePageChange(pageNum)}
                                                disabled={isLoading}
                                                className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${processedData.currentPage === pageNum
                                                    ? 'bg-orange-400 text-white shadow-md'
                                                    : 'border border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50'
                                                    }`}
                                            >
                                                {isLoading && processedData.currentPage === pageNum ? (
                                                    <div className="w-4 h-4 border-2 border-white border-t-orange-200 rounded-full animate-spin"></div>
                                                ) : (
                                                    pageNum
                                                )}
                                            </button>
                                        );
                                    }
                                    return null;
                                })}

                                {/* Next Button */}
                                <button
                                    onClick={() => handlePageChange(processedData.currentPage + 1)}
                                    disabled={processedData.currentPage === processedData.totalPages || isLoading}
                                    className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
                                >
                                    Next
                                    {isLoading && processedData.currentPage < processedData.totalPages ? (
                                        <div className="w-4 h-4 border-2 border-gray-300 border-t-orange-400 rounded-full animate-spin"></div>
                                    ) : null}
                                </button>
                            </div>
                        </div>
                    )}
                </motion.div>
            </div>
        </div>
    );
}
