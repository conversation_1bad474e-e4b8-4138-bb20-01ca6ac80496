
import Image from 'next/image';
import Link from 'next/link';
import {
    FaTag,
    FaBoxes,
    FaStar,
    FaCalendarAlt,
    FaEdit,
    FaEye,
    FaArrowRight,
    FaHome,
    FaChevronRight,
    FaShoppingCart,
    FaHeart,
    FaShare
} from 'react-icons/fa';

// API function to get category by ID
export async function getCategorybyId(id) {
    try {
        console.log('🔍 Fetching category details for ID:', id);

        if (!id) {
            console.log('❌ No ID provided to getCategorybyId');
            return {
                success: false,
                message: 'No category ID provided',
                data: null
            };
        }

        // Try real API call first
        try {
            console.log('🌐 Attempting real API call...');
            const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/categories/${id}`, {
                cache: 'no-store', // Don't cache for debugging
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                console.log('✅ Real API data received:', data);
                return data;
            } else {
                console.log('⚠️ API response not ok, falling back to mock data');
            }
        } catch (apiError) {
            console.log('⚠️ API call failed, falling back to mock data:', apiError.message);
        }
    } catch (error) {
        console.error('❌ Error in getCategorybyId:', error);
        return {
            success: false,
            message: `Failed to fetch category: ${error.message}`,
            data: null
        };
    }
}

// Generate SEO metadata
export async function generateMetadata({ params }) {
    console.log('🔍 generateMetadata received params:', params);

    // Handle both direct params and awaited params (Next.js 13+ app router)
    let resolvedParams = params;
    if (params && typeof params.then === 'function') {
        console.log('🔄 Awaiting params in generateMetadata...');
        resolvedParams = await params;
    }

    const categoryId = resolvedParams?.id;
    console.log('🔍 generateMetadata categoryId:', categoryId);

    if (!categoryId) {
        return {
            title: 'Category Details - B2B Marketplace',
            description: 'Browse our product categories in the B2B marketplace.'
        };
    }

    try {
        const response = await getCategorybyId(categoryId);

        if (response.success && response.data?.category) {
            const category = response.data.category;

            return {
                title: `${category.name} - Category Details | B2B Marketplace`,
                description: `${category.description} Browse ${category.product_count} products in the ${category.name} category. Perfect for bulk purchasing and business needs.`,
                keywords: `${category.name}, ${category.slug}, B2B, wholesale, bulk products, ${category.name.toLowerCase()} products`,
                openGraph: {
                    title: `${category.name} - B2B Category`,
                    description: category.description,
                    type: 'website',
                    url: `/categories/${categoryId}`,
                    images: category.image ? [
                        {
                            url: category.image,
                            width: 1200,
                            height: 630,
                            alt: `${category.name} category image`,
                        }
                    ] : [],
                },
                twitter: {
                    card: 'summary_large_image',
                    title: `${category.name} - B2B Category`,
                    description: category.description,
                    images: category.image ? [category.image] : [],
                },
                alternates: {
                    canonical: `/categories/${categoryId}`,
                },
            };
        }
    } catch (error) {
        console.error('❌ Error generating metadata:', error);
    }

    return {
        title: 'Category Details - B2B Marketplace',
        description: 'Browse our product categories in the B2B marketplace.'
    };
}

// Server-side Category Image Component (no state)
const CategoryImage = ({ category, className = "" }) => {
    const hasValidImage = category.image && category.image.trim() !== '';

    return (
        <div className={`relative bg-gradient-to-br from-orange-100 to-orange-200 ${className}`}>
            {hasValidImage ? (
                <Image
                    src={category.image}
                    alt={`${category.name} category`}
                    fill
                    style={{ objectFit: 'cover' }}
                    className="transition-transform duration-500 hover:scale-105"
                    priority
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
            ) : (
                <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center text-orange-600">
                        <FaBoxes className="mx-auto mb-4 text-6xl" />
                        <span className="text-xl font-medium">{category.name}</span>
                    </div>
                </div>
            )}

            {/* Overlay for better text readability */}
            <div className="absolute inset-0 bg-black bg-opacity-20"></div>
        </div>
    );
};

// Breadcrumb Component
const Breadcrumb = ({ category }) => (
    <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-6">
        <Link href="/" className="hover:text-orange-500 transition-colors flex items-center gap-1">
            <FaHome className="text-xs" />
            Home
        </Link>
        <FaChevronRight className="text-xs text-gray-400" />
        <Link href="/categories" className="hover:text-orange-500 transition-colors">
            Categories
        </Link>
        <FaChevronRight className="text-xs text-gray-400" />
        <span className="text-gray-900 font-medium">{category.name}</span>
    </nav>
);

// Stats Card Component
const StatsCard = ({ icon: Icon, label, value, color = "orange" }) => (
    <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
        <div className="flex items-center gap-4">
            <div className={`p-3 rounded-lg bg-${color}-100`}>
                <Icon className={`text-2xl text-${color}-600`} />
            </div>
            <div>
                <p className="text-sm text-gray-600">{label}</p>
                <p className="text-2xl font-bold text-gray-900">{value}</p>
            </div>
        </div>
    </div>
);

// Subcategory Card Component
const SubcategoryCard = ({ subcategory }) => (
    <Link href={`/categories/${subcategory.id}`}>
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-lg hover:border-orange-200 transition-all duration-300 group">
            <div className="flex items-center justify-between mb-4">
                <div className="p-3 rounded-lg bg-orange-50 group-hover:bg-orange-100 transition-colors">
                    <FaTag className="text-xl text-orange-600" />
                </div>
                <FaArrowRight className="text-gray-400 group-hover:text-orange-500 group-hover:translate-x-1 transition-all" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-orange-600 transition-colors">
                {subcategory.name}
            </h3>
            <p className="text-sm text-gray-600">
                {subcategory.product_count} products available
            </p>
        </div>
    </Link>
);

export default async function CategoryDetails({ params }) {
    console.log('🔍 CategoryDetails component started');
    console.log('🔍 CategoryDetails received params:', params);

    // Handle both direct params and awaited params (Next.js 13+ app router)
    let resolvedParams = params;
    if (params && typeof params.then === 'function') {
        console.log('🔄 Awaiting params...');
        resolvedParams = await params;
    }

    console.log('🔍 Resolved params:', resolvedParams);

    const categoryId = resolvedParams?.id;
    console.log('🔍 Extracted categoryId:', categoryId);

    if (!categoryId) {
        console.log('❌ No categoryId found in params');
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-gray-900 mb-4">Category Not Found</h1>
                    <p className="text-gray-600 mb-6">The category you're looking for doesn't exist.</p>
                    <p className="text-sm text-gray-500 mb-6">Debug: params = {JSON.stringify(resolvedParams)}</p>
                    <Link href="/categories" className="bg-orange-500 text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition-colors">
                        Browse Categories
                    </Link>
                </div>
            </div>
        );
    }

    const response = await getCategorybyId(categoryId);

    if (!response.success || !response.data?.category) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Category</h1>
                    <p className="text-gray-600 mb-6">{response.message || 'Failed to load category details.'}</p>
                    <Link href="/categories" className="bg-orange-500 text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition-colors">
                        Browse Categories
                    </Link>
                </div>
            </div>
        );
    }

    const category = response.data.category;
    const createdDate = new Date(category.created_at).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    const updatedDate = new Date(category.updated_at).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Hero Section */}
            <div className="relative">
                <CategoryImage
                    category={category}
                    className="w-full h-64 md:h-80 lg:h-96"
                />

                {/* Hero Content Overlay */}
                <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center text-white z-10">
                        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4 drop-shadow-lg">
                            {category.name}
                        </h1>
                        <p className="text-lg md:text-xl lg:text-2xl opacity-90 max-w-2xl mx-auto px-4 drop-shadow">
                            {category.description}
                        </p>

                        {/* Category Badges */}
                        <div className="flex flex-wrap items-center justify-center gap-3 mt-6">
                            {category.is_featured && (
                                <span className="bg-yellow-500 text-white px-4 py-2 rounded-full text-sm font-medium flex items-center gap-2">
                                    <FaStar className="text-xs" />
                                    Featured Category
                                </span>
                            )}
                            {category.is_active && (
                                <span className="bg-green-500 text-white px-4 py-2 rounded-full text-sm font-medium">
                                    Active
                                </span>
                            )}
                            <span className="bg-orange-500 text-white px-4 py-2 rounded-full text-sm font-medium">
                                {category.product_count} Products
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <Breadcrumb category={category} />

                {/* Stats Section */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                    <StatsCard
                        icon={FaBoxes}
                        label="Total Products"
                        value={category.product_count}
                        color="orange"
                    />
                    <StatsCard
                        icon={FaTag}
                        label="Category ID"
                        value={`#${category.id}`}
                        color="blue"
                    />
                    {/* <StatsCard
                        icon={FaCalendarAlt}
                        label="Created"
                        value={createdDate}
                        color="green"
                    /> */}
                    {/* <StatsCard
                        icon={FaEdit}
                        label="Last Updated"
                        value={updatedDate}
                        color="purple"
                    /> */}
                </div>

                {/* Category Information */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
                    {/* Main Info */}
                    <div className="lg:col-span-2">
                        <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100">
                            <h2 className="text-2xl font-bold text-gray-900 mb-6">Category Information</h2>

                            <div className="space-y-6">
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Description</h3>
                                    <p className="text-gray-600 leading-relaxed">
                                        {category.description}
                                    </p>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <h4 className="font-medium text-gray-900 mb-2">Category Details</h4>
                                        <div className="space-y-2 text-sm">
                                            <div className="flex justify-between">
                                                <span className="text-gray-600">Slug:</span>
                                                <span className="font-medium text-gray-900">{category.slug}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-gray-600">Sort Order:</span>
                                                <span className="font-medium text-gray-900">{category.sort_order || 'Not set'}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-gray-600">Parent Category:</span>
                                                <span className="font-medium text-gray-900">{category.parent?.name || 'None'}</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div>
                                        <h4 className="font-medium text-gray-900 mb-2">Status</h4>
                                        <div className="space-y-2 text-sm">
                                            <div className="flex justify-between">
                                                <span className="text-gray-600">Active:</span>
                                                <span className={`font-medium ${category.is_active ? 'text-green-600' : 'text-red-600'}`}>
                                                    {category.is_active ? 'Yes' : 'No'}
                                                </span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-gray-600">Featured:</span>
                                                <span className={`font-medium ${category.is_featured ? 'text-yellow-600' : 'text-gray-600'}`}>
                                                    {category.is_featured ? 'Yes' : 'No'}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Quick Actions */}
                    <div className="space-y-6">
                        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                            <div className="space-y-3">
                                <Link
                                    href={`/products/categories/${category.id}`}
                                    className="w-full bg-orange-500 text-white px-4 py-3 rounded-lg hover:bg-orange-600 transition-colors flex items-center justify-center gap-2 font-medium"
                                >
                                    <FaEye />
                                    View Products
                                </Link>
                                <button className="w-full bg-gray-100 text-gray-700 px-4 py-3 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center gap-2 font-medium">
                                    <FaHeart />
                                    Add to Favorites
                                </button>
                                <button className="w-full bg-gray-100 text-gray-700 px-4 py-3 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center gap-2 font-medium">
                                    <FaShare />
                                    Share Category
                                </button>
                            </div>
                        </div>

                        {/* Category Image */}
                        {category.image && (
                            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Category Image</h3>
                                <div className="relative h-48 rounded-lg overflow-hidden">
                                    <Image
                                        src={category.image}
                                        alt={`${category.name} category`}
                                        fill
                                        style={{ objectFit: 'cover' }}
                                        className="hover:scale-105 transition-transform duration-300"
                                        sizes="(max-width: 768px) 100vw, 400px"
                                    />
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                {/* Subcategories Section */}
                {category.children && category.children.length > 0 && (
                    <div className="mb-12">
                        <div className="flex items-center justify-between mb-8">
                            <h2 className="text-2xl font-bold text-gray-900">Subcategories</h2>
                            <span className="text-sm text-gray-600">
                                {category.children.length} subcategories available
                            </span>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {category.children.map((subcategory) => (
                                <SubcategoryCard key={subcategory.id} subcategory={subcategory} />
                            ))}
                        </div>
                    </div>
                )}

                {/* Call to Action */}
                <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl p-8 text-center text-white">
                    <h2 className="text-3xl font-bold mb-4">Ready to Explore Products?</h2>
                    <p className="text-lg opacity-90 mb-6 max-w-2xl mx-auto">
                        Discover {category.product_count} amazing products in the {category.name} category.
                        Perfect for bulk purchasing and business needs.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Link
                            href={`/products/categories/${category.id}`}
                            className="bg-white text-orange-600 px-8 py-3 rounded-lg hover:bg-gray-100 transition-colors font-semibold flex items-center justify-center gap-2"
                        >
                            <FaShoppingCart />
                            Browse Products
                        </Link>
                        <Link
                            href="/categories"
                            className="border-2 border-white text-white px-8 py-3 rounded-lg hover:bg-white hover:text-orange-600 transition-colors font-semibold"
                        >
                            View All Categories
                        </Link>
                    </div>
                </div>

                {/* SEO Content */}
                <div className="mt-12 bg-white rounded-xl p-8 shadow-sm border border-gray-100">
                    <h2 className="text-2xl font-bold text-gray-900 mb-6">About {category.name} Category</h2>
                    <div className="prose prose-gray max-w-none">
                        <p className="text-gray-600 leading-relaxed mb-4">
                            The {category.name} category offers a comprehensive selection of high-quality products
                            perfect for businesses, retailers, and bulk purchasers. With {category.product_count} products
                            available, you'll find everything you need in this category.
                        </p>
                        <p className="text-gray-600 leading-relaxed mb-4">
                            {category.description} Our B2B marketplace ensures competitive pricing,
                            reliable suppliers, and efficient delivery for all your business needs.
                        </p>

                        {/* SEO Keywords */}
                        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                            <h3 className="font-semibold text-gray-900 mb-2">Related Keywords:</h3>
                            <div className="flex flex-wrap gap-2">
                                {[
                                    category.name.toLowerCase(),
                                    `${category.name.toLowerCase()} wholesale`,
                                    `bulk ${category.name.toLowerCase()}`,
                                    `${category.name.toLowerCase()} B2B`,
                                    `${category.name.toLowerCase()} suppliers`,
                                    category.slug
                                ].map((keyword, index) => (
                                    <span key={index} className="bg-white px-3 py-1 rounded-full text-sm text-gray-600 border">
                                        {keyword}
                                    </span>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
