'use client';
import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import PrimaryGradientBtn from '../Buttons/PrimaryGradientBtn';
import ComingSoonPopup from '../ComingSoonPopup';
import { useComingSoon } from '../../hooks/useComingSoon';

export default function AutoReorder() {
  const [hovered, setHovered] = useState(null);
  const { isPopupOpen, featureName, showComingSoon, hideComingSoon } = useComingSoon();

  const features = [
    {
      id: 1,
      title: "Customize frequency",
      description: "Choose daily, weekly, monthly, or custom intervals for your recurring orders.",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      )
    },
    {
      id: 2,
      title: "Pause, resume, or cancel anytime",
      description: "Full flexibility to adjust your subscription as your business needs change.",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    },
    {
      id: 3,
      title: "Get notifications before processing",
      description: "We'll remind you before each auto-order is processed so you can make any last-minute adjustments.",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
        </svg>
      )
    }
  ];

  return (
    <section className="py-24 bg-gradient-to-br from-white via-gray-50 to-gray-100">
      <div className="container mx-auto px-6 md:px-12">
        <motion.div
          className="flex flex-col md:flex-row items-center gap-12 md:gap-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          {/* Content */}
          <div className="w-full md:w-1/2">
            <div className="relative">
              <div className="absolute -top-10 -left-10 w-20 h-20 bg-orange-400 opacity-5 rounded-full blur-xl"></div>
              <motion.div
                className="w-20 h-1.5 bg-gradient-to-r from-yellow-300 to-orange-400 rounded-full mb-6"
                initial={{ width: 0 }}
                whileInView={{ width: 80 }}
                viewport={{ once: true }}
                transition={{ delay: 0.2, duration: 0.8 }}
              ></motion.div>
            </div>
            <motion.h2
              className="text-4xl md:text-5xl font-bold mb-6 text-gray-900 leading-tight"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.1 }}
            >
              Never Run Out of <span className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-400">Essentials</span>
            </motion.h2>
            <motion.p
              className="text-xl text-gray-600 mb-10 leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              Set up automated recurring orders on a weekly or monthly basis. Perfect for frequently ordered items that your business relies on.
            </motion.p>
            <motion.div
              className="space-y-6 mb-10"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.3 }}
            >
              {features.map((feature, index) => (
                <motion.div
                  key={feature.id}
                  className="flex items-start gap-4"
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                  whileHover={{ scale: 1.01, x: 5 }}
                  onHoverStart={() => setHovered(feature.id)}
                  onHoverEnd={() => setHovered(null)}
                >
                  <div className={`p-3 rounded-xl bg-gradient-to-br ${hovered === feature.id ? 'from-yellow-200 to-orange-400 text-white' : 'from-yellow-50 to-orange-50 text-orange-400'} transition-colors duration-300 flex-shrink-0`}>
                    {feature.icon}
                  </div>
                  <div>
                    <h3 className="font-semibold text-xl text-gray-900 mb-1">{feature.title}</h3>
                    <p className="text-gray-600">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <button
                onClick={() => showComingSoon('Auto-Order')}
                className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-8 py-4 rounded-lg font-semibold hover:from-orange-600 hover:to-orange-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                Set Auto-Order
              </button>
            </motion.div>
          </div>

          {/* Visual representation */}
          <div className="w-full md:w-1/2 relative">
            <motion.div
              className="relative z-10"
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
              whileHover={{ scale: 1.02 }}
            >
              <div className="bg-gradient-to-br from-yellow-50 to-orange-100 rounded-2xl p-4 sm:p-6 shadow-xl shadow-red-200/50">
                <div className="relative h-[300px] sm:h-[350px] md:h-[400px] rounded-xl overflow-hidden">
                  {/* Decorative elements with responsive sizing */}
                  <div className="absolute -top-10 -right-10 w-24 h-24 sm:w-32 sm:h-32 md:w-40 md:h-40 bg-purple-400 rounded-full mix-blend-multiply filter blur-2xl opacity-10 transform transition-transform duration-700 hover:scale-110"></div>
                  <div className="absolute -bottom-10 -left-10 w-24 h-24 sm:w-32 sm:h-32 md:w-40 md:h-40 bg-orange-300 rounded-full mix-blend-multiply filter blur-2xl opacity-10 transform transition-transform duration-700 hover:scale-110"></div>

                  {/* Responsive image container */}
                  <div className="relative w-full h-full">
                    <Image
                      src="https://images.unsplash.com/photo-1586892477838-2b96e85e0f96?q=80&w=1000&auto=format&fit=crop"
                      alt="Auto-reorder scheduling interface"
                      fill
                      sizes="(max-width: 640px) 100vw, (max-width: 768px) 80vw, 600px"
                      className="object-contain rounded-lg z-10 transition-transform duration-500 hover:scale-105"
                      priority
                    />
                  </div>
                </div>

                {/* Floating badge with responsive positioning and animation */}
                <motion.div
                  className="absolute top-4 sm:top-6 md:top-8 right-4 sm:right-6 md:right-8 flex space-x-2 z-20"
                  animate={{
                    y: [0, -10, 0],
                  }}
                  transition={{
                    duration: 0.5,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  <div className="relative">
                    <span className="px-3 py-2 bg-gradient-to-r from-green-400 to-emerald-500 text-white text-xs sm:text-sm font-semibold rounded-full shadow-lg inline-block backdrop-blur-sm">
                      Save time & money
                    </span>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>

      <ComingSoonPopup
        isOpen={isPopupOpen}
        onClose={hideComingSoon}
        featureName={featureName}
      />
    </section>
  );
}
