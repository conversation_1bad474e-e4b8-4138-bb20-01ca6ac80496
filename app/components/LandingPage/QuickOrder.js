'use client';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import PrimaryGradientBtn from '../Buttons/PrimaryGradientBtn';
import ComingSoonPopup from '../ComingSoonPopup';
import { useComingSoon } from '../../hooks/useComingSoon';

export default function QuickOrder() {
  const [orderRows, setOrderRows] = useState([
    { id: 1, code: '', name: '', quantity: 1, price: 0 },
    { id: 2, code: '', name: '', quantity: 1, price: 0 },
    { id: 3, code: '', name: '', quantity: 1, price: 0 },
  ]);
  const [totalItems, setTotalItems] = useState(3);
  const [totalPrice, setTotalPrice] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [notification, setNotification] = useState({ show: false, message: '', type: '' });
  const { isPopupOpen, featureName, showComingSoon, hideComingSoon } = useComingSoon();

  // Calculate totals whenever orderRows change
  useEffect(() => {
    const items = orderRows.reduce((sum, row) => sum + row.quantity, 0);
    const price = orderRows.reduce((sum, row) => sum + (row.price * row.quantity), 0);
    setTotalItems(items);
    setTotalPrice(price);
  }, [orderRows]);

  const handleAddRow = () => {
    const newId = orderRows.length > 0 ? Math.max(...orderRows.map(row => row.id)) + 1 : 1;
    setOrderRows([...orderRows, { id: newId, code: '', name: '', quantity: 1, price: 0 }]);
    showNotification('New row added', 'info');
  };

  const handleRemoveRow = (id) => {
    if (orderRows.length > 1) {
      setOrderRows(orderRows.filter(row => row.id !== id));
      showNotification('Row removed', 'info');
    } else {
      showNotification('Cannot remove the last row', 'error');
    }
  };

  const handleChange = (id, field, value) => {
    // For price field, ensure it's converted to a number
    if (field === 'price') {
      value = parseFloat(value) || 0;
    }

    setOrderRows(orderRows.map(row =>
      row.id === id ? { ...row, [field]: value } : row
    ));
  };

  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setIsUploading(true);

      // Simulate file processing
      setTimeout(() => {
        setIsUploading(false);
        showNotification('File uploaded successfully! Processing your products...', 'success');

        // In a real implementation, you would parse the file here
        // For demo purposes, let's add some mock data
        const mockProducts = [
          { id: Date.now() + 1, code: 'P001', name: 'Premium Widget', quantity: 5, price: 29.99 },
          { id: Date.now() + 2, code: 'P002', name: 'Deluxe Gadget', quantity: 3, price: 49.99 },
        ];

        setOrderRows([...orderRows, ...mockProducts]);
      }, 1500);
    }
  };

  const showNotification = (message, type) => {
    setNotification({ show: true, message, type });
    setTimeout(() => setNotification({ show: false, message: '', type: '' }), 3000);
  };

  const handleSubmit = () => {
    // Validate form
    const hasEmptyFields = orderRows.some(row => !row.code || !row.name);
    if (hasEmptyFields) {
      showNotification('Please fill in all product details', 'error');
      return;
    }

    showNotification('Products added to pallet successfully!', 'success');
    // In a real implementation, you would submit the order here
  };

  return (
    <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
      <div className="container mx-auto px-4 md:px-12">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-12">
            <motion.h2
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-4xl font-extrabold text-gray-900 mb-4"
            >
              Quick Order <span className="text-orange-500">Express</span>
            </motion.h2>
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              className="text-xl text-gray-600 max-w-3xl mx-auto"
            >
              Streamlined ordering for power buyers. Add products quickly and efficiently with our enhanced order system.
            </motion.p>
          </div>

          {/* Notification */}
          <AnimatePresence>
            {notification.show && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className={`mb-6 p-4 rounded-lg shadow-md ${notification.type === 'success' ? 'bg-green-100 text-green-800 border-l-4 border-green-500' :
                  notification.type === 'error' ? 'bg-red-100 text-red-800 border-l-4 border-red-500' :
                    'bg-orange-100 text-orange-800 border-l-4 border-orange-400'
                  }`}
              >
                {notification.message}
              </motion.div>
            )}
          </AnimatePresence>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.5 }}
            className="bg-white rounded-2xl p-8 shadow-xl border border-gray-100"
          >
            <div className="mb-8">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
                <h3 className="text-2xl font-bold text-gray-800 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  Product Entry
                </h3>

                <div className="flex gap-4">
                  <button
                    onClick={handleAddRow}
                    className="flex items-center px-4 py-2 bg-orange-100 text-orange-500 rounded-lg hover:bg-orange-200 transition-all duration-200"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Add Row
                  </button>

                  <label className={`cursor-pointer flex items-center gap-2 px-4 py-2 ${isUploading ? 'bg-orange-100' : 'bg-orange-400 hover:bg-orange-500'} text-white rounded-lg transition-all duration-200`}>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    {isUploading ? 'Uploading...' : 'Import Products'}
                    <input
                      type="file"
                      accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                      className="hidden"
                      onChange={handleFileUpload}
                      disabled={isUploading}
                    />
                  </label>
                </div>
              </div>

              <div className="overflow-x-auto rounded-xl border border-gray-200 shadow-sm">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Product Code
                      </th>
                      <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Product Name
                      </th>
                      <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Quantity
                      </th>
                      <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Unit Price
                      </th>
                      <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Total
                      </th>
                      <th scope="col" className="relative px-6 py-4">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    <AnimatePresence>
                      {orderRows.map((row) => (
                        <motion.tr
                          key={row.id}
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                        >
                          <td className="px-6 py-3 whitespace-nowrap">
                            <input
                              type="text"
                              placeholder="e.g. SKU123"
                              className="border border-gray-300 rounded-lg shadow-sm focus:border-orange-500 focus:ring-2 focus:ring-orange-200 focus:ring-opacity-50 text-sm p-3 w-full"
                              value={row.code}
                              onChange={(e) => handleChange(row.id, 'code', e.target.value)}
                            />
                          </td>
                          <td className="px-6 py-3 whitespace-nowrap">
                            <input
                              type="text"
                              placeholder="Product name..."
                              className="border border-gray-300 rounded-lg shadow-sm focus:border-orange-400 focus:ring-2 focus:ring-orange-200 focus:ring-opacity-50 text-sm p-3 w-full"
                              value={row.name}
                              onChange={(e) => handleChange(row.id, 'name', e.target.value)}
                            />
                          </td>
                          <td className="px-6 py-3 whitespace-nowrap">
                            <div className="flex items-center">
                              <button
                                className="bg-gray-200 hover:bg-gray-300 rounded-l-lg p-2"
                                onClick={() => handleChange(row.id, 'quantity', Math.max(1, row.quantity - 1))}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                                </svg>
                              </button>
                              <input
                                type="number"
                                min="1"
                                className="border-t border-b border-gray-300 text-center focus:outline-none focus:ring-0 text-sm py-2 w-14"
                                value={row.quantity}
                                onChange={(e) => handleChange(row.id, 'quantity', parseInt(e.target.value) || 1)}
                              />
                              <button
                                className="bg-gray-200 hover:bg-gray-300 rounded-r-lg p-2"
                                onClick={() => handleChange(row.id, 'quantity', row.quantity + 1)}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                              </button>
                            </div>
                          </td>
                          <td className="px-6 py-3 whitespace-nowrap">
                            <div className="relative">
                              <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">$</span>
                              <input
                                type="number"
                                min="0"
                                step="0.01"
                                className="border border-gray-300 rounded-lg pl-8 shadow-sm focus:border-orange-400 focus:ring-2 focus:ring-orange-200 focus:ring-opacity-50 text-sm p-3 w-full"
                                value={row.price}
                                onChange={(e) => handleChange(row.id, 'price', parseFloat(e.target.value) || 0)}
                              />
                            </div>
                          </td>
                          <td className="px-6 py-3 whitespace-nowrap font-medium text-gray-900">
                            ${(row.price * row.quantity).toFixed(2)}
                          </td>
                          <td className="px-6 py-3 whitespace-nowrap text-right">
                            <button
                              onClick={() => handleRemoveRow(row.id)}
                              className="text-red-500 hover:text-red-700 transition-colors p-2"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </button>
                          </td>
                        </motion.tr>
                      ))}
                    </AnimatePresence>
                  </tbody>
                </table>
              </div>

              {/* Order Summary */}
              <div className="mt-8 bg-gray-50 border border-gray-200 rounded-xl p-6">
                <h4 className="text-lg font-bold text-gray-800 mb-4">Order Summary</h4>
                <div className="flex flex-col md:flex-row justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex justify-between py-2 border-b border-gray-200">
                      <span className="text-gray-600">Total Items:</span>
                      <span className="font-medium">{totalItems}</span>
                    </div>
                    <div className="flex justify-between py-2 border-b border-gray-200">
                      <span className="text-gray-600">Total Products:</span>
                      <span className="font-medium">{orderRows.length}</span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className="flex justify-between py-2 border-b border-gray-200">
                      <span className="text-gray-600">Subtotal:</span>
                      <span className="font-medium">${totalPrice.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between py-3 items-center">
                      <span className="text-gray-800 font-semibold">Total Price:</span>
                      <span className="text-xl font-bold text-orange-500">${totalPrice.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-8 flex flex-col sm:flex-row justify-end gap-4">
                <button className="px-6 py-3 bg-gray-200 text-gray-800 font-medium rounded-xl hover:bg-gray-300 transition duration-200">
                  Save Draft
                </button>
                <button
                  onClick={handleSubmit}
                  className="px-8 py-3 bg-orange-400 text-white font-medium rounded-xl hover:bg-orange-500 transition duration-200 flex items-center justify-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Add All to Pallet
                </button>
              </div>
            </div>
          </motion.div>

          <div className="text-center mt-12">
            <button
              onClick={() => showComingSoon('Quick Order Mode')}
              className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-8 py-4 rounded-lg font-semibold hover:from-orange-600 hover:to-orange-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              Continue to Quick Order Mode
            </button>

            <p className="text-gray-500 mt-4 max-w-md mx-auto">
              Our powerful order system supports direct entry, CSV uploads, and copy-paste from spreadsheets for maximum efficiency
            </p>
          </div>
        </div>
      </div>

      <ComingSoonPopup
        isOpen={isPopupOpen}
        onClose={hideComingSoon}
        featureName={featureName}
      />
    </section>
  );
}
