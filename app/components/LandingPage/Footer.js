'use client';
import Link from 'next/link';
import Image from 'next/image';
import { FaFacebookF, FaTwitter, FaLinkedinIn, FaInstagram, FaHome, FaList, FaEnvelope, FaInfoCircle } from 'react-icons/fa';
import { MdPhone, MdEmail, MdHelp, MdPayment, MdLocalShipping, MdAssignment } from 'react-icons/md';
import { BiSupport, BiCategory } from 'react-icons/bi';
import { BsQuestionCircle } from 'react-icons/bs';
import ComingSoonPopup from '../ComingSoonPopup';
import { useComingSoon } from '../../hooks/useComingSoon';

export default function Footer() {
  const currentYear = new Date().getFullYear();
  const { isPopupOpen, featureName, showComingSoon, hideComingSoon } = useComingSoon();

  return (
    <footer className="bg-gray-900 text-gray-300">
      <div className="container mx-auto px-6 md:px-12 pt-16 pb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
          {/* Company & Logo */}
          <div>
            <Link href="/" className="text-2xl font-bold text-white mb-4 block">
              <span className='text-orange-500'>D</span>iptouch
            </Link>
            <p className="text-gray-400 mb-6">
              The digital platform revolutionizing B2B distribution and wholesale purchasing.
            </p>
            <div className="flex gap-4">
              <button className="text-blue-400 hover:text-white p-2 hover:bg-gray-800 rounded-full transition-all duration-300">
                <FaFacebookF className="h-5 w-5" />
              </button>
              <button className="text-sky-400 hover:text-white p-2 hover:bg-gray-800 rounded-full transition-all duration-300">
                <FaTwitter className="h-5 w-5" />
              </button>
              <button className="text-blue-400 hover:text-white p-2 hover:bg-gray-800 rounded-full transition-all duration-300">
                <FaLinkedinIn className="h-5 w-5" />
              </button>
              <button className="text-pink-400 hover:text-white p-2 hover:bg-gray-800 rounded-full transition-all duration-300">
                <FaInstagram className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-6">Quick Links</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/" className="hover:text-white transition duration-300 flex items-center gap-2 group">
                  <FaHome className="h-4 w-4 text-red-400 group-hover:text-white transition-colors duration-300" />
                  <span>Home</span>
                </Link>
              </li>
              <li>
                <Link href="/categories" className="hover:text-white transition duration-300 flex items-center gap-2 group">
                  <BiCategory className="h-4 w-4 text-green-400 group-hover:text-white transition-colors duration-300" />
                  <span>Categories</span>
                </Link>
              </li>
              <li>
                <Link href="/contact" className="hover:text-white transition duration-300 flex items-center gap-2 group">
                  <FaEnvelope className="h-4 w-4 text-blue-400 group-hover:text-white transition-colors duration-300" />
                  <span>Contact</span>
                </Link>
              </li>
              <li>
                <button
                  onClick={() => showComingSoon('FAQs')}
                  className="hover:text-white transition duration-300 flex items-center gap-2 group"
                >
                  <BsQuestionCircle className="h-4 w-4 text-purple-400 group-hover:text-white transition-colors duration-300" />
                  <span>FAQs</span>
                </button>
              </li>
              <li>
                <Link href="/about" className="hover:text-white transition duration-300 flex items-center gap-2 group">
                  <FaInfoCircle className="h-4 w-4 text-orange-400 group-hover:text-white transition-colors duration-300" />
                  <span>About</span>
                </Link>
              </li>
            </ul>
          </div>

          {/* Help Links */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-6 flex items-center gap-2">
              <BiSupport className="h-5 w-5 text-teal-400" />
              Help & Support
            </h3>
            <div className="grid grid-cols-1 gap-3">
              <h4 className="text-white text-sm font-medium">For Buyers</h4>
              <ul className="space-y-2 mb-4">
                <li>
                  <button
                    onClick={() => showComingSoon('Order Tracking')}
                    className="text-gray-400 hover:text-white transition duration-300 flex items-center gap-2 group"
                  >
                    <MdLocalShipping className="h-4 w-4 text-indigo-400 group-hover:text-white transition-colors duration-300" />
                    <span>Track Order</span>
                  </button>
                </li>
                <li>
                  <button
                    onClick={() => showComingSoon('Return Policy')}
                    className="text-gray-400 hover:text-white transition duration-300 flex items-center gap-2 group"
                  >
                    <MdAssignment className="h-4 w-4 text-yellow-400 group-hover:text-white transition-colors duration-300" />
                    <span>Return Policy</span>
                  </button>
                </li>
                <li>
                  <button
                    onClick={() => showComingSoon('Payment Methods')}
                    className="text-gray-400 hover:text-white transition duration-300 flex items-center gap-2 group"
                  >
                    <MdPayment className="h-4 w-4 text-emerald-400 group-hover:text-white transition-colors duration-300" />
                    <span>Payment Methods</span>
                  </button>
                </li>
              </ul>

              <h4 className="text-white text-sm font-medium">For Distributors</h4>
              <ul className="space-y-2">
                <li>
                  <button
                    onClick={() => showComingSoon('Listing Guide')}
                    className="text-gray-400 hover:text-white transition duration-300 flex items-center gap-2 group"
                  >
                    <FaList className="h-4 w-4 text-cyan-400 group-hover:text-white transition-colors duration-300" />
                    <span>Listing Guide</span>
                  </button>
                </li>
                <li>
                  <button
                    onClick={() => showComingSoon('Subscription Plans')}
                    className="text-gray-400 hover:text-white transition duration-300 flex items-center gap-2 group"
                  >
                    <MdHelp className="h-4 w-4 text-rose-400 group-hover:text-white transition-colors duration-300" />
                    <span>Subscription Plans</span>
                  </button>
                </li>
              </ul>
            </div>
          </div>

          {/* Contact & App Download */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-6">Contact & Apps</h3>
            <div className="space-y-4 mb-6">
              <p className="flex items-center group cursor-pointer">
                <MdPhone className="h-5 w-5 mr-2 text-green-400 group-hover:text-white transition-colors duration-300" />
                <span className="group-hover:text-white transition-colors duration-300">+****************</span>
              </p>
              <p className="flex items-center group cursor-pointer">
                <MdEmail className="h-5 w-5 mr-2 text-blue-400 group-hover:text-orange-400 transition-colors duration-300" />
                <span className='text-orange-400'><EMAIL></span>
              </p>
            </div>

            <h4 className="text-white text-sm font-medium mb-4">Download Our App</h4>
            <div className="flex flex-col gap-3">
              <button
                onClick={() => showComingSoon('iOS App')}
                className="relative w-32 h-10 hover:opacity-80 transition-opacity"
              >
                <Image
                  src="/images/logos/ios-app-img.svg"
                  alt="Download on App Store"
                  fill
                  className="object-contain rounded"
                />
              </button>
              <button
                onClick={() => showComingSoon('Android App')}
                className="relative w-32 h-10 hover:opacity-80 transition-opacity"
              >
                <Image
                  src="/images/logos/android-app-img.svg"
                  alt="Get it on Google Play"
                  fill
                  className="object-contain rounded"
                />
              </button>
            </div>
          </div>
        </div>

        <hr className="my-8 border-gray-700" />

        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0">
            <p className="text-sm text-gray-400">
              &copy; {currentYear} <span className='text-orange-500'>D</span>iptouch Distributors. All rights reserved.
            </p>
          </div>
          <div className="flex gap-4">
            <button
              onClick={() => showComingSoon('Privacy Policy')}
              className="text-sm text-gray-400 hover:text-white transition duration-300"
            >
              Privacy Policy
            </button>
            <button
              onClick={() => showComingSoon('Terms of Use')}
              className="text-sm text-gray-400 hover:text-white transition duration-300"
            >
              Terms of Use
            </button>
          </div>
        </div>
      </div>

      <ComingSoonPopup
        isOpen={isPopupOpen}
        onClose={hideComingSoon}
        featureName={featureName}
      />
    </footer>
  );
}
