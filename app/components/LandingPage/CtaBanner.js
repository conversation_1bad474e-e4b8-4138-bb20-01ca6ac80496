'use client';
import Link from 'next/link';
import { motion } from 'framer-motion';
import ComingSoonPopup from '../ComingSoonPopup';
import { useComingSoon } from '../../hooks/useComingSoon';

export default function CtaBanner() {
  const { isPopupOpen, featureName, showComingSoon, hideComingSoon } = useComingSoon();

  return (
    <section className="py-20 bg-gradient-to-r from-orange-400
    via-orange-500 to-yellow-200 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(255,255,255,0.05),transparent_70%)]"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(255,255,255,0.08),transparent_50%)]"></div>

      {/* Decorative circles */}
      <div className="absolute top-0 right-0 w-64 h-64 bg-yellow-400/20 rounded-full filter blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-orange-500/20 rounded-full filter blur-3xl"></div>

      {/* Decorative grid lines */}
      <div className="absolute inset-0 bg-grid-white/[0.05] bg-[size:40px_40px]"></div>

      <div className="container mx-auto px-6 md:px-12 relative z-10">
        <motion.div
          className="flex flex-col lg:flex-row items-center justify-between gap-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <div className="mb-6 lg:mb-0 text-center lg:text-left max-w-2xl">
            <motion.div
              className="inline-block mb-4 bg-gradient-to-r from-yellow-400 to-orange-400 px-4 py-1 rounded-full"
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <span className="text-white font-medium text-md uppercase tracking-wider">For Distributors</span>
            </motion.div>

            <motion.h2
              className="text-3xl md:text-5xl font-bold text-indigo-500 mb-6 leading-tight"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Are You a Distributor Ready to <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-200 to-blue-200">Go Digital?</span>
            </motion.h2>

            <motion.p
              className="text-white text-xl max-w-xl leading-relaxed"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              Join thousands of businesses using Diptouch to grow and scale online.
              Expand your reach and simplify your operations with our industry-leading platform.
            </motion.p>

            {/* Statistics */}
            <motion.div
              className="mt-8 grid grid-cols-3 gap-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="text-center">
                <div className="text-3xl font-bold text-white">5K+</div>
                <div className="text-blue-200 text-sm">Distributors</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white">97%</div>
                <div className="text-blue-200 text-sm">Satisfaction</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white">24/7</div>
                <div className="text-blue-200 text-sm">Support</div>
              </div>
            </motion.div>
          </div>

          <motion.div
            className="flex flex-col gap-4 sm:min-w-[300px]"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <div className="bg-white/10 backdrop-blur-lg p-8 rounded-2xl shadow-2xl border border-white/10">
              <h3 className="text-indigo-500 font-bold text-center text-2xl mb-4">Get Started Today</h3>

              <div className="flex flex-col gap-4">
                {/* Sign Up Link */}
                <motion.div
                  whileHover={{ y: -3, x: 0, scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <Link
                    href="/signup/partner"
                    className="w-full px-8 py-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-indigo-500 font-bold text-xl rounded-xl hover:shadow-lg hover:shadow-red-600/30 transition duration-300 text-center block"
                  >
                    Sign Up Now
                  </Link>
                </motion.div>

                <motion.div
                  whileHover={{ y: -3, x: 0, scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <button
                    onClick={() => showComingSoon('Demo Request')}
                    className="w-full px-8 py-4 bg-transparent border-2 border-white text-indigo-500 font-bold text-xl rounded-xl hover:shadow-red-600/30 transition duration-300 text-center flex items-center justify-center gap-2"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                    </svg>
                    Request a Demo
                  </button>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>

      <ComingSoonPopup
        isOpen={isPopupOpen}
        onClose={hideComingSoon}
        featureName={featureName}
      />
    </section>
  );
}
