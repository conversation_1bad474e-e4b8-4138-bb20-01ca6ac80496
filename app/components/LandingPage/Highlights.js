'use client';
import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import PrimaryGradientBtn from '../Buttons/PrimaryGradientBtn';
// import PrimaryCartBtn from './Buttons/PrimaryCartBtn';

export default function Highlights() {
  const [activeTab, setActiveTab] = useState('distributors');

  const topDistributors = [
    {
      id: 1,
      name: "TechElite Inc.",
      image: "https://images.unsplash.com/photo-1560179707-f14e90ef3623?q=80&w=100&auto=format&fit=crop",
      category: "Electronics",
      rating: 4.9,
      totalOrders: 1243
    },
    {
      id: 2,
      name: "GlobalFoods Distributors",
      image: "https://images.unsplash.com/photo-1542838132-92c53300491e?q=80&w=100&auto=format&fit=crop",
      category: "Food & Beverages",
      rating: 4.8,
      totalOrders: 987
    },
    {
      id: 3,
      name: "HomeGoods Direct",
      image: "https://images.unsplash.com/photo-1606836576983-8b458e75221d?q=80&w=100&auto=format&fit=crop",
      category: "Home & Garden",
      rating: 4.7,
      totalOrders: 812
    }
  ];

  const buyerFavorites = [
    {
      id: 1,
      name: "Premium Bluetooth Speaker",
      image: "https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?q=80&w=200&auto=format&fit=crop",
      price: 79.99,
      purchaseCount: 348
    },
    {
      id: 2,
      name: "Organic Coffee Bean Bundle",
      image: "https://images.unsplash.com/photo-1602143407151-7111542de6e8?q=80&w=200&auto=format&fit=crop",
      price: 34.99,
      purchaseCount: 296
    },
    {
      id: 3,
      name: "Eco-Friendly Water Bottles (6pk)",
      image: "https://images.unsplash.com/photo-1602143407151-7111542de6e8?q=80&w=200&auto=format&fit=crop",
      price: 29.99,
      purchaseCount: 275
    }
  ];

  const marketTrends = [
    {
      id: 1,
      title: "Sustainable Packaging Demand Rises 40%",
      category: "Sustainability",
      image: "https://images.unsplash.com/photo-1566576721346-d4a3b4eaeb55?q=80&w=200&auto=format&fit=crop",
      excerpt: "Buyers are increasingly requesting eco-friendly packaging options across all product categories."
    },
    {
      id: 2,
      title: "Tech Accessories See Surge in Wholesale Orders",
      category: "Market Analysis",
      image: "https://images.unsplash.com/photo-1519389950473-47ba0277781c?q=80&w=200&auto=format&fit=crop",
      excerpt: "Mobile accessories and smart home products lead the growth in the technology sector."
    },
    {
      id: 3,
      title: "Supply Chain Innovations Reduce Delivery Times",
      category: "Logistics",
      image: "https://images.unsplash.com/photo-1566576721346-d4a3b4eaeb55?q=80&w=200&auto=format&fit=crop",
      excerpt: "New technologies and partnerships are helping distributors improve their delivery metrics."
    }
  ];

  /* const tabVariants = {
    inactive: { opacity: 0.6, scale: 0.95 },
    active: { opacity: 1, scale: 1 }
  }; */

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1 }
  };

  return (
    <section className="py-24 bg-gradient-to-b from-white to-gray-50">
      <div className="container mx-auto px-6 md:px-12">
        <div className="text-center mb-12">
          <motion.h2
            className="text-4xl md:text-5xl font-bold text-gray-900 mb-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.1 }}
          >
            <span className='text-orange-500'>D</span>iptouch <span className="">Highlights</span>
          </motion.h2>

          <motion.div
            className="w-20 h-1.5 bg-gradient-to-r from-yellow-200 to-orange-600 rounded-full mb-6 mx-auto"
            initial={{ width: 0 }}
            whileInView={{ width: 200 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2, duration: 0.8 }}
          ></motion.div>
        </div>

        {/* Content based on active tab */}
        <div className="mt-8">
          <AnimatePresence mode="wait">
            {/* Top-Rated Distributors */}
            <motion.h2
              key={"distributorsTitle"}
              className='text-3xl md:text-4xl font-bold text-orange-500 pt-10'
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              exit="hidden">Top Rated Distributors</motion.h2>
            <motion.div
              key="distributors"
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 my-10"
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
            >
              {topDistributors.map((distributor, index) => (
                <motion.div
                  key={distributor.id}
                  className="bg-white rounded-2xl overflow-hidden shadow-xl hover:shadow-2xl transition-shadow duration-300 border border-gray-100"
                  variants={itemVariants}
                  whileHover={{ y: -5, transition: { duration: 0.3 } }}
                >
                  <div className="p-8">
                    <div className="flex items-center mb-6">
                      <div className="relative h-16 w-16 rounded-xl overflow-hidden mr-4 shadow-md">
                        <Image
                          src={distributor.image}
                          alt={distributor.name}
                          fill
                          sizes="(max-width: 768px) 100vw, 100px"
                          className="object-cover"
                        />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900">{distributor.name}</h3>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-500 mt-1">
                          {distributor.category}
                        </span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center mb-5">
                      <div className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <span className="ml-1 text-gray-900 font-semibold">{distributor.rating}</span>
                      </div>
                      <div className="flex items-center text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                        </svg>
                        <span className="text-sm font-medium">{distributor.totalOrders.toLocaleString()} orders</span>
                      </div>
                    </div>
                    <div className="h-1 w-full bg-gray-100 rounded mb-5">
                      <motion.div
                        className="h-full bg-gradient-to-r from-yellow-100 to-orange-400 rounded"
                        initial={{ width: 0 }}
                        whileInView={{ width: `${(distributor.rating / 5) * 100}%` }}
                        viewport={{ once: true }}
                        transition={{ delay: 0.4 + index * 0.1, duration: 1 }}
                      ></motion.div>
                    </div>
                    <Link href={`/distributors/${distributor.id}`} className="block text-center py-3 bg-gradient-to-r text-white rounded-xl shadow-md hover:shadow-lg font-medium flex-1 p-3 bg-orange-400 items-center justify-center gap-2 group relative bg-orange-400font-medium hover:bg-orange-500 transition-all duration-300">
                      <span className="absolute right-0 w-8 h-32 -mt-12 transition-all duration-1000 transform translate-x-12 bg-white opacity-10 rotate-12 group-hover:-translate-x-40 ease"></span>
                      View Products
                    </Link>
                  </div>
                </motion.div>
              ))}
            </motion.div>


            {/* Buyer Favorites */}
            <motion.h2
              key="buyerFavorites"
              className='text-3xl md:text-4xl font-bold text-orange-500 pt-10'
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              exit="hidden">Buyer Favorites</motion.h2>
            <motion.div
              key="favorites"
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 my-10"
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
            >
              {buyerFavorites.map((product) => (
                <motion.div
                  key={product.id}
                  className="bg-white rounded-2xl overflow-hidden shadow-xl hover:shadow-2xl transition-shadow duration-300 border border-gray-100"
                  variants={itemVariants}
                  whileHover={{ y: -5, transition: { duration: 0.3 } }}
                >
                  <div className="relative h-56 w-full overflow-hidden">
                    <Image
                      src={product.image}
                      alt={product.name}
                      fill
                      sizes="(max-width: 768px) 100vw, 200px"
                      className="object-cover transition-transform duration-500 hover:scale-110"
                    />
                    <div className="absolute top-3 right-3 bg-white rounded-full p-1.5 shadow-md">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="absolute bottom-3 left-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-md font-medium shadow-md">
                      Best Seller
                    </div>
                  </div>
                  <div className="p-6">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-xl font-bold text-gray-900">{product.name}</h3>
                      <div className="flex flex-col items-end">
                        <span className="text-2xl font-bold text-orange-500">${product.price}</span>
                        <span className="text-sm text-gray-500">Wholesale price</span>
                      </div>
                    </div>
                    <div className="flex items-center text-gray-600 mb-5">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                      </svg>
                      <span className="text-sm font-medium">Purchased <span className="font-semibold text-gray-900">{product.purchaseCount}</span> times this month</span>
                    </div>
                    <div className="space-y-3">
                      {/* <PrimaryCartBtn text="Add to Pallet" /> */}

                      <Link href={`/products/${product.id}`} className="w-full py-3 bg-gray-100 text-gray-800 rounded-xl hover:bg-gray-200 transition duration-300 font-medium flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        View Details
                      </Link>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>


            {/* Market Trends */}
            <motion.h2
              key="trendsTitle"
              className='text-3xl md:text-4xl font-bold text-orange-500 pt-10'
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              exit="hidden">Market Trends</motion.h2>
            <motion.div
              key="trends"
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 my-10"
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
            >
              {marketTrends.map((trend) => (
                <motion.div
                  key={trend.id}
                  variants={itemVariants}
                  whileHover={{ y: -5, transition: { duration: 0.3 } }}
                >
                  <Link
                    href={`/blog/trends/${trend.id}`}
                    className="block h-full bg-white rounded-2xl overflow-hidden shadow-xl hover:shadow-2xl transition-shadow duration-300 border border-gray-100"
                  >
                    <div className="relative h-48 w-full overflow-hidden">
                      <Image
                        src={trend.image}
                        alt={trend.title}
                        fill
                        sizes="(max-width: 768px) 100vw, 200px"
                        className="object-cover transition-transform duration-500 hover:scale-110"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                      <div className="absolute bottom-3 left-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-md">
                        {trend.category}
                      </div>
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">{trend.title}</h3>
                      <p className="text-gray-600 text-base mb-5 line-clamp-3">{trend.excerpt}</p>
                      <div className="flex items-center text-orange-00 font-medium">
                        Read more
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </div>
                    </div>
                  </Link>
                </motion.div>
              ))}
            </motion.div>

          </AnimatePresence>
        </div>

        <div className="text-center mt-16">
          <PrimaryGradientBtn text={'View All'} />
        </div>
      </div>
    </section>
  );
}
