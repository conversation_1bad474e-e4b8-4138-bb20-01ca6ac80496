'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { motion, useInView, useAnimation } from 'framer-motion';
import ComingSoonPopup from '../ComingSoonPopup';
import { useComingSoon } from '../../hooks/useComingSoon';

export default function HowItWorks() {
  const [activeTab, setActiveTab] = useState('buyers');
  const [isVisible, setIsVisible] = useState(false);
  const { isPopupOpen, featureName, showComingSoon, hideComingSoon } = useComingSoon();
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, threshold: 0.1 });
  const controls = useAnimation();

  useEffect(() => {
    setIsVisible(true);
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 50,
      scale: 0.95
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  const stepVariants = {
    hidden: {
      opacity: 0,
      x: -30
    },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <section
      ref={ref}
      className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50 relative overflow-hidden"
      aria-labelledby="how-it-works-heading"
      role="region"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden" aria-hidden="true">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-orange-200 rounded-full opacity-20 blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-amber-200 rounded-full opacity-20 blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-yellow-200 rounded-full opacity-10 blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header Section */}
        <motion.div
          className="text-center mb-12 sm:mb-16 lg:mb-20"
          variants={itemVariants}
          initial="hidden"
          animate={controls}
        >
          <div className="inline-flex items-center justify-center p-2 bg-orange-100 rounded-full mb-6">
            <span className="bg-orange-400 text-white text-sm font-semibold px-4 py-2 rounded-full">
              How It Works
            </span>
          </div>

          <h2
            id="how-it-works-heading"
            className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight"
          >
            How{' '}
            <span className="bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent">
              Diptouch
            </span>{' '}
            Works
          </h2>

          <p className="text-lg sm:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Diptouch streamlines B2B interactions with a platform designed for both buyers and distributors,
            making wholesale commerce simple, secure, and efficient.
          </p>
        </motion.div>

        {/* Tab Navigation */}
        <motion.div
          className="flex justify-center mb-12 sm:mb-16"
          variants={itemVariants}
          initial="hidden"
          animate={controls}
        >
          <div className="bg-white rounded-2xl p-2 shadow-lg border border-gray-200">
            <div className="flex space-x-2" role="tablist" aria-label="User type selection">
              <button
                onClick={() => setActiveTab('buyers')}
                className={`px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-orange-200 focus:ring-opacity-50 ${activeTab === 'buyers'
                  ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                role="tab"
                aria-selected={activeTab === 'buyers'}
                aria-controls="buyers-panel"
                id="buyers-tab"
              >
                <span className="flex items-center gap-2">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                  </svg>
                  For Buyers
                </span>
              </button>
              <button
                onClick={() => setActiveTab('distributors')}
                className={`px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-orange-200 focus:ring-opacity-50 ${activeTab === 'distributors'
                  ? 'bg-gradient-to-r from-emerald-500 to-green-600 text-white shadow-lg'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                role="tab"
                aria-selected={activeTab === 'distributors'}
                aria-controls="distributors-panel"
                id="distributors-tab"
              >
                <span className="flex items-center gap-2">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                  For Distributors
                </span>
              </button>
            </div>
          </div>
        </motion.div>

        {/* Tab Content */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={controls}
        >
          {/* Buyers Panel */}
          {activeTab === 'buyers' && (
            <motion.div
              id="buyers-panel"
              role="tabpanel"
              aria-labelledby="buyers-tab"
              className="bg-white rounded-3xl shadow-2xl border border-gray-200 overflow-hidden"
              variants={itemVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
            >
              <div className="relative">
                {/* Header Section */}
                <div className="bg-gradient-to-r from-blue-500 to-indigo-600 px-8 sm:px-12 py-12 sm:py-16 text-white relative overflow-hidden">
                  <div className="absolute top-0 right-0 w-64 h-64 bg-white opacity-10 rounded-full -mr-32 -mt-32" aria-hidden="true"></div>
                  <div className="absolute bottom-0 left-0 w-80 h-80 bg-white opacity-5 rounded-full -ml-40 -mb-40" aria-hidden="true"></div>

                  <div className="relative z-10 text-center">
                    <div className="inline-flex items-center justify-center w-20 h-20 sm:w-24 sm:h-24 bg-white/20 backdrop-blur-sm rounded-2xl mb-6">
                      <svg className="w-10 h-10 sm:w-12 sm:h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                      </svg>
                    </div>
                    <h3 className="text-3xl sm:text-4xl font-bold mb-4">For Buyers</h3>
                    <p className="text-blue-100 text-lg sm:text-xl">Streamlined wholesale purchasing made simple</p>
                  </div>
                </div>

                {/* Steps Section */}
                <div className="px-8 sm:px-12 py-12 sm:py-16">
                  <div className="space-y-8 sm:space-y-12">
                    {[
                      {
                        icon: (
                          <svg className="w-6 h-6 sm:w-7 sm:h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                          </svg>
                        ),
                        title: "Browse & Compare Products",
                        description: "Explore thousands of products from verified distributors with detailed specifications, competitive pricing, and real-time availability.",
                        step: "01"
                      },
                      {
                        icon: (
                          <svg className="w-6 h-6 sm:w-7 sm:h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                          </svg>
                        ),
                        title: "Place Orders Securely",
                        description: "Purchase individual units or optimize your orders with our intelligent pallet-based system for better rates and shipping efficiency.",
                        step: "02"
                      },
                      {
                        icon: (
                          <svg className="w-6 h-6 sm:w-7 sm:h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                          </svg>
                        ),
                        title: "Track & Receive",
                        description: "Monitor your shipments in real-time with our advanced tracking system and receive your products through our optimized logistics network.",
                        step: "03"
                      }
                    ].map((step, index) => (
                      <motion.div
                        key={index}
                        className="flex items-start gap-6 sm:gap-8 group"
                        variants={stepVariants}
                        initial="hidden"
                        animate="visible"
                        transition={{ delay: index * 0.2 }}
                      >
                        <div className="flex-shrink-0">
                          <div className="relative">
                            <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center text-white shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                              {step.icon}
                            </div>
                            <div className="absolute -top-2 -right-2 w-8 h-8 bg-orange-400 rounded-full flex items-center justify-center text-white text-sm font-bold">
                              {step.step}
                            </div>
                          </div>
                        </div>
                        <div className="flex-1 pt-2">
                          <h4 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                            {step.title}
                          </h4>
                          <p className="text-gray-600 leading-relaxed text-base sm:text-lg">
                            {step.description}
                          </p>
                        </div>
                      </motion.div>
                    ))}
                  </div>

                  {/* CTA Section */}
                  <div className="mt-12 sm:mt-16 text-center">
                    <Link
                      href="/signup/user"
                      className="inline-flex items-center gap-3 px-8 sm:px-10 py-4 sm:py-5 bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-semibold rounded-2xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 focus:outline-none focus:ring-4 focus:ring-blue-200 focus:ring-opacity-50"
                      aria-label="Join as a buyer to start purchasing wholesale products"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                      </svg>
                      Join as Buyer
                      <svg className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                      </svg>
                    </Link>
                    <p className="mt-4 text-sm text-gray-500">
                      Join thousands of businesses already buying wholesale
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {/* Distributors Panel */}
          {activeTab === 'distributors' && (
            <motion.div
              id="distributors-panel"
              role="tabpanel"
              aria-labelledby="distributors-tab"
              className="bg-white rounded-3xl shadow-2xl border border-gray-200 overflow-hidden"
              variants={itemVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
            >
              <div className="relative">
                {/* Header Section */}
                <div className="bg-gradient-to-r from-emerald-500 to-green-600 px-8 sm:px-12 py-12 sm:py-16 text-white relative overflow-hidden">
                  <div className="absolute top-0 left-0 w-64 h-64 bg-white opacity-10 rounded-full -ml-32 -mt-32" aria-hidden="true"></div>
                  <div className="absolute bottom-0 right-0 w-80 h-80 bg-white opacity-5 rounded-full -mr-40 -mb-40" aria-hidden="true"></div>

                  <div className="relative z-10 text-center">
                    <div className="inline-flex items-center justify-center w-20 h-20 sm:w-24 sm:h-24 bg-white/20 backdrop-blur-sm rounded-2xl mb-6">
                      <svg className="w-10 h-10 sm:w-12 sm:h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                    </div>
                    <h3 className="text-3xl sm:text-4xl font-bold mb-4">For Distributors</h3>
                    <p className="text-green-100 text-lg sm:text-xl">Expand your digital presence and reach</p>
                  </div>
                </div>

                {/* Steps Section */}
                <div className="px-8 sm:px-12 py-12 sm:py-16">
                  <div className="space-y-8 sm:space-y-12">
                    {[
                      {
                        icon: (
                          <svg className="w-6 h-6 sm:w-7 sm:h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                          </svg>
                        ),
                        title: "Register & Get Verified",
                        description: "Complete our comprehensive verification process and easily upload your product catalog with our intuitive management tools.",
                        step: "01"
                      },
                      {
                        icon: (
                          <svg className="w-6 h-6 sm:w-7 sm:h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                          </svg>
                        ),
                        title: "Manage Orders & Inventory",
                        description: "Use our powerful dashboard to track orders, update inventory levels in real-time, and manage your entire business operations efficiently.",
                        step: "02"
                      },
                      {
                        icon: (
                          <svg className="w-6 h-6 sm:w-7 sm:h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        ),
                        title: "Earn & Grow",
                        description: "Get paid promptly with our secure payment processing system, transparent fee structure, and access to detailed analytics to grow your business.",
                        step: "03"
                      }
                    ].map((step, index) => (
                      <motion.div
                        key={index}
                        className="flex items-start gap-6 sm:gap-8 group"
                        variants={stepVariants}
                        initial="hidden"
                        animate="visible"
                        transition={{ delay: index * 0.2 }}
                      >
                        <div className="flex-shrink-0">
                          <div className="relative">
                            <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-r from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center text-white shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                              {step.icon}
                            </div>
                            <div className="absolute -top-2 -right-2 w-8 h-8 bg-orange-400 rounded-full flex items-center justify-center text-white text-sm font-bold">
                              {step.step}
                            </div>
                          </div>
                        </div>
                        <div className="flex-1 pt-2">
                          <h4 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3 group-hover:text-green-600 transition-colors duration-300">
                            {step.title}
                          </h4>
                          <p className="text-gray-600 leading-relaxed text-base sm:text-lg">
                            {step.description}
                          </p>
                        </div>
                      </motion.div>
                    ))}
                  </div>

                  {/* CTA Section */}
                  <div className="mt-12 sm:mt-16 text-center space-y-4">
                    <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                      <Link
                        href="/signup/partner"
                        className="inline-flex items-center gap-3 px-8 sm:px-10 py-4 sm:py-5 bg-gradient-to-r from-emerald-500 to-green-600 text-white font-semibold rounded-2xl hover:from-emerald-600 hover:to-green-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 focus:outline-none focus:ring-4 focus:ring-green-200 focus:ring-opacity-50"
                        aria-label="Become a distributor to start selling wholesale products"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                        Become a Distributor
                        <svg className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                        </svg>
                      </Link>
                      <button
                        onClick={() => showComingSoon('Pricing Plans')}
                        className="inline-flex items-center gap-3 px-6 sm:px-8 py-3 sm:py-4 bg-green-50 border-2 border-green-200 text-green-700 font-semibold rounded-2xl hover:bg-green-100 hover:border-green-300 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-green-200 focus:ring-opacity-50"
                        aria-label="View pricing plans for distributors"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                        See Pricing Plans
                      </button>
                    </div>
                    <p className="text-sm text-gray-500">
                      Join hundreds of distributors already growing their business
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </motion.div>
      </div>

      <ComingSoonPopup
        isOpen={isPopupOpen}
        onClose={hideComingSoon}
        featureName={featureName}
      />
    </section>
  );
}
