"use client";

import { useState, useEffect, memo, useCallback } from 'react';
import Link from 'next/link';
import SubNavbar from './SubNavbar';
import LoadingNavbar from './LoadingNavbar';
import { useCart } from '../../context/CartContext';
import { useAuth } from '../../context/AuthContext.js';

function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const { cartCount } = useCart();
  const { user, logout, isAuthenticated, loading, initialized } = useAuth();

  // Memoize logout handler to prevent re-renders
  const handleLogout = useCallback(() => {
    logout();
    setUserMenuOpen(false);
  }, [logout]);

  // Handle scroll effect - only after component is mounted
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  // Handle click outside to close user menu - only after component is mounted
  useEffect(() => {
    if (typeof document === 'undefined') return;

    const handleClickOutside = (event) => {
      if (userMenuOpen && !event.target.closest('.user-menu-container')) {
        setUserMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [userMenuOpen]);

  // Show loading navbar until context is initialized
  if (!initialized) {
    return <LoadingNavbar />;
  }

  return (
    <nav className={`sticky top-0 z-50 transition-all duration-300 ${scrolled
      ? 'bg-white shadow-lg'
      : 'bg-white'
      }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16 sm:h-20 xl:h-16">
          {/* Logo */}
          <div className="flex-shrink-0 flex items-center">
            <Link href="/" className="font-bold text-xl text-black hover:text-orange-500 transition-colors">
              <span className='text-orange-500'>D</span>iptouch
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex space-x-4 mx-2">
            <Link href="/" className="inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-800 hover:text-orange-500 transition-colors border-b-2 border-transparent hover:border-orange-500">
              Home
            </Link>
            <div className="relative group inline-flex">
              <Link href="/categories" className="inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-800 hover:text-orange-500 transition-colors border-b-2 border-transparent hover:border-orange-500">
                Categories<span className="ml-1 transition-transform group-hover:rotate-180 duration-300">▾</span>
              </Link>
              <div className="absolute w-48 left-0 top-full origin-top-right bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition duration-300 border border-gray-200 z-50">
                {/* <div className="py-1">
                  <Link href="/categories/electronics" className="block px-4 py-2 text-sm text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">Electronics</Link>
                  <Link href="/categories/apparel" className="block px-4 py-2 text-sm text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">Apparel</Link>
                  <Link href="/categories/food" className="block px-4 py-2 text-sm text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">Food & Beverages</Link>
                  <Link href="/categories/all" className="block px-4 py-2 text-sm text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">View All</Link>
                </div> */}
              </div>
            </div>
            <Link href="/products" className="inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-800 hover:text-orange-500 transition-colors border-b-2 border-transparent hover:border-orange-500">
              All Products
            </Link>
            <Link href="/signup/partner" className="inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-800 hover:text-orange-500 transition-colors border-b-2 border-transparent hover:border-orange-500">
              Become a Distributor
            </Link>
            <Link href="/about" className="inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-800 hover:text-orange-500 transition-colors border-b-2 border-transparent hover:border-orange-500">
              About Us
            </Link>
            <Link href="/contact" className="inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-800 hover:text-orange-500 transition-colors border-b-2 border-transparent hover:border-orange-500">
              Contact
            </Link>
            {/* Temporary SEO Dashboard Link for Development */}
            <Link href="/seo-admin" className="inline-flex items-center px-1 pt-1 text-sm font-medium text-orange-600 hover:text-orange-700 transition-colors border-b-2 border-orange-500 hover:border-orange-600">
              SEO Dashboard
            </Link>
          </div>

          {/* Right side navigation */}
          <div className="hidden md:flex items-center space-x-4">
            {/* Language Selector */}
            <div className="relative group">
              <button className="flex items-center text-sm font-medium text-gray-700 hover:text-black transition-all duration-300 transform hover:scale-105">
                <span>EN</span>
                <span className="ml-1 transition-transform group-hover:rotate-180 duration-300">▾</span>
              </button>
              <div className="absolute right-0 w-24 origin-top-right bg-white/95 backdrop-blur-sm rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 border border-gray-200/50 z-50 transform group-hover:translate-y-0 translate-y-2">
                <div className="py-1">
                  <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">English</button>
                  {/* <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">Bangla</button> */}
                </div>
              </div>
            </div>

            {/* Authentication Section */}
            {isAuthenticated ? (
              /* User Menu */
              <div className="relative user-menu-container">
                <button
                  onClick={() => setUserMenuOpen(!userMenuOpen)}
                  className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-orange-100 transition-all duration-300 focus:outline-none group"
                >
                  {/* User Avatar */}
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-orange-400 to-orange-600 flex items-center justify-center text-white font-semibold text-sm">
                    {user?.displayName?.charAt(0) || 'U'}
                  </div>
                  {/* User Info */}
                  <div className="hidden sm:block text-left">
                    <div className="text-sm font-medium text-gray-800 group-hover:text-orange-600">
                      {user?.displayName || 'User'}
                    </div>
                    <div className="text-xs text-gray-500 capitalize">
                      {user?.role === 'buyer' ? 'Customer' : user?.role === 'vendor' ? 'Partner' : 'Super Admin'}
                    </div>
                  </div>
                  {/* Dropdown Arrow */}
                  <svg
                    className={`w-4 h-4 text-gray-500 transition-transform duration-300 ${userMenuOpen ? 'rotate-180' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {/* User Dropdown Menu */}
                {userMenuOpen && (
                  <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 z-50 overflow-hidden">
                    {/* User Info Header */}
                    <div className="px-4 py-3 bg-gradient-to-r from-orange-50 to-orange-100 border-b border-gray-200">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-r from-orange-400 to-orange-600 flex items-center justify-center text-white font-semibold">
                          {user?.displayName?.charAt(0) || 'U'}
                        </div>
                        <div>
                          <div className="font-medium text-gray-800">{user?.displayName}</div>
                          <div className="text-sm text-gray-600 capitalize">
                            {user?.role === 'buyer' ? 'Customer' : user?.role === 'vendor' ? 'Partner' : 'Super Admin'}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Menu Items */}
                    <div className="py-1">
                      {/* Dashboard Link */}
                      {user?.role === 'buyer' && (
                        <Link
                          href="/customer-admin"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-600 transition-colors"
                          onClick={() => setUserMenuOpen(false)}
                        >
                          <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                          My Dashboard
                        </Link>
                      )}
                      {user?.role === 'vendor' && (
                        <Link
                          href="/partner-admin"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-600 transition-colors"
                          onClick={() => setUserMenuOpen(false)}
                        >
                          <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-6 4h6" />
                          </svg>
                          Partner Dashboard
                        </Link>
                      )}
                      {user?.role === 'super-admin' && (
                        <Link
                          href="/super-admin"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-600 transition-colors"
                          onClick={() => setUserMenuOpen(false)}
                        >
                          <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                          </svg>
                          Admin Panel
                        </Link>
                      )}

                      <div className="border-t border-gray-200 my-1"></div>

                      {/* Logout */}
                      <button
                        onClick={handleLogout}
                        className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                      >
                        <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                        </svg>
                        Logout
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              /* Login/Signup for non-authenticated users */
              <>
                {/* Login */}
                <div className="relative group inline-flex">
                  <div className="inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-800 hover:text-orange-500 transition-colors border-b-2 border-transparent hover:border-orange-500">
                    Login<span className="ml-1 transition-transform group-hover:rotate-180 duration-300">▾</span>
                  </div>
                  <div className="absolute w-24 left-0 top-full origin-top-right bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition duration-300 border border-gray-200 z-50">
                    <div className="">
                      <Link href="/login/user" className="block px-4 py-2 text-sm text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">User</Link>
                      <Link href="/login/partner" className="block px-4 py-2 text-sm text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">Partner</Link>
                    </div>
                  </div>
                </div>
                {/* Signup */}
                <div className="relative group inline-flex">
                  <div className="inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-800 hover:text-orange-500 transition-colors border-b-2 border-transparent hover:border-orange-500">
                    Sign Up<span className="ml-1 transition-transform group-hover:rotate-180 duration-300">▾</span>
                  </div>
                  <div className="absolute w-24 left-0 top-full origin-top-right bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition duration-300 border border-gray-200 z-50">
                    <div className="">
                      <Link href="/signup/user" className="block px-4 py-2 text-sm text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">User</Link>
                      <Link href="/signup/partner" className="block px-4 py-2 text-sm text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">Partner</Link>
                    </div>
                  </div>
                </div>
              </>
            )}

            {/* Cart with enhanced animation */}
            <Link href="/cart" className="relative p-2 rounded-full hover:bg-orange-100 transition-all duration-300 hover:scale-110 group">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-700 group-hover:text-orange-500 transition-colors duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              {cartCount > 0 && (
                <span className="absolute top-0 right-0 inline-flex items-center justify-center px-1.5 py-0.5 rounded-full text-xs font-bold leading-none text-white bg-red-500 transform translate-x-1/2 -translate-y-1/2 animate-pulse">
                  {cartCount}
                </span>
              )}
            </Link>

            {/* Your Pallet with enhanced effects */}
            {/* <div className="flex flex-col sm:flex-row gap-3 md:gap-5 justify-center md:justify-start mb-12 sm:mb-0">
              <Link href="/pallet" className="px-6 py-2.5 text-white rounded-full text-sm font-medium shadow-lg hover:shadow-orange-400/30 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-orange-500 hover:to-yellow-400 text-center transform hover:-translate-y-1 hover:scale-105 mx-auto group relative overflow-hidden transition-all duration-300">
                <span className="absolute right-0 w-8 h-32 -mt-12 transition-all duration-1000 transform translate-x-12 bg-white opacity-10 rotate-12 group-hover:-translate-x-40 ease"></span>
                <span className="relative">Your Pallet</span>
              </Link>
            </div> */}
          </div>

          {/* Mobile menu button with improved animation */}
          <div className="md:hidden flex items-center space-x-3">
            {/* Mobile Cart with enhanced animation */}
            <Link href="/cart" className="relative p-2 rounded-full hover:bg-orange-100 transition-all duration-300 hover:scale-110 group">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-700 hover:text-black transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              {cartCount > 0 && (
                <span className="absolute top-0 right-0 inline-flex items-center justify-center px-1.5 py-0.5 rounded-full text-xs font-bold leading-none text-white bg-red-500 transform translate-x-1/2 -translate-y-1/2">
                  {cartCount}
                </span>
              )}
            </Link>

            {/* Hamburger menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-orange-500 hover:bg-orange-100 transition-all duration-300 focus:outline-none transform hover:scale-105"
              aria-expanded={isMenuOpen}
            >
              <span className="sr-only">{isMenuOpen ? 'Close menu' : 'Open menu'}</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white/95 backdrop-blur-sm border-t border-gray-200 animate-fadeIn">
          <div className="px-2 pt-2 pb-3 space-y-1">
            <Link href="/" className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">
              Home
            </Link>
            <Link href="/categories" className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">
              Categories<span>▾</span>
            </Link>
            <Link href="/products" className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">
              All Products
            </Link>
            <Link href="/signup/partner" className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">
              Become a Distributor
            </Link>
            <Link href="/about" className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">
              About Us
            </Link>
            <Link href="/contact" className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">
              Contact
            </Link>
            {/* Temporary SEO Dashboard Link for Development */}
            <Link href="/seo-admin" className="block px-3 py-2 rounded-md text-base font-medium text-orange-600 hover:bg-orange-100 hover:text-orange-700 transition-colors">
              SEO Dashboard
            </Link>

            {/* Mobile Authentication Section */}
            {isAuthenticated ? (
              <>
                <div className="px-3 py-2 border-t border-gray-200 mt-2">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-r from-orange-400 to-orange-600 flex items-center justify-center text-white font-semibold">
                      {user?.displayName?.charAt(0) || 'U'}
                    </div>
                    <div>
                      <div className="font-medium text-gray-800">{user?.displayName}</div>
                      <div className="text-sm text-gray-600 capitalize">
                        {user?.role === 'buyer' ? 'Customer' : user?.role === 'vendor' ? 'Partner' : 'Super Admin'}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Dashboard Links */}
                {user?.role === 'buyer' && (
                  <Link href="/customer-admin" className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">
                    My Dashboard
                  </Link>
                )}
                {user?.role === 'vendor' && (
                  <Link href="/partner-admin" className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">
                    Partner Dashboard
                  </Link>
                )}
                {user?.role === 'superadmin' && (
                  <Link href="/super-admin" className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">
                    Admin Panel
                  </Link>
                )}

                <button
                  onClick={() => {
                    handleLogout();
                    setIsMenuOpen(false);
                  }}
                  className="block w-full text-left px-3 py-2 rounded-md text-base font-medium text-red-600 hover:bg-red-50 transition-colors"
                >
                  Logout
                </button>
              </>
            ) : (
              <>
                <Link href="/login/user" className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">
                  User Login
                </Link>
                <Link href="/login/partner" className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">
                  Partner Login
                </Link>
              </>
            )}
            <Link href="/products" className="flex items-center px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors">
              <span>Your Pallet</span>
              {cartCount > 0 && (
                <span className="ml-2 inline-flex items-center justify-center px-2 py-0.5 rounded-full text-xs font-bold leading-none text-white bg-red-500">
                  {cartCount}
                </span>
              )}
            </Link>
          </div>
        </div>
      )}

      {/* sub navbar for desktop */}
      <SubNavbar />
    </nav>
  );
}

// Memoize the component to prevent unnecessary re-renders
export default memo(Navbar);
