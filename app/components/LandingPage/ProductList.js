'use client';
import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';

// API function to fetch categories tree from backend
export async function getCategoriesTree() {
  try {
    console.log('🔍 Fetching categories tree from API...');
    const response = await fetch('https://b2b.instinctfusionx.xyz/public/api/v1/categories/tree', {
      cache: 'force-cache',
      next: { revalidate: 3600 }, // Revalidate every hour
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Categories tree fetched successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Error fetching categories:', error);
    // Return fallback structure on error
    return {
      success: false,
      message: 'Failed to fetch categories',
      data: { categories: [] }
    };
  }
}

// API function to fetch all brands with pagination handling
export async function getAllBrands() {
  try {
    console.log('🔍 Fetching all brands from API...');
    let allBrands = [];
    let currentPage = 1;
    let hasMorePages = true;

    while (hasMorePages) {
      const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/brands?page=${currentPage}`, {
        cache: 'force-cache',
        next: { revalidate: 3600 }, // Revalidate every hour
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.data?.brands?.data) {
        allBrands = [...allBrands, ...data.data.brands.data];

        // Check if there are more pages
        hasMorePages = currentPage < data.data.brands.last_page;
        currentPage++;

        console.log(`✅ Fetched page ${currentPage - 1} of brands (${data.data.brands.data.length} brands)`);
      } else {
        hasMorePages = false;
      }
    }

    console.log(`✅ All brands fetched successfully: ${allBrands.length} total brands`);
    return {
      success: true,
      data: { brands: allBrands }
    };
  } catch (error) {
    console.error('❌ Error fetching brands:', error);
    return {
      success: false,
      message: 'Failed to fetch brands',
      data: { brands: [] }
    };
  }
}



// Loading skeleton components
const CategorySkeleton = () => (
  <div className="flex flex-col items-center text-center group flex-shrink-0">
    <div className="w-24 h-20 rounded-lg border border-gray-200 bg-gray-100 animate-pulse flex flex-col items-center justify-center p-1">
      <div className="h-12 w-12 bg-gray-200 rounded mb-1 animate-pulse"></div>
      <div className="h-3 w-16 bg-gray-200 rounded animate-pulse"></div>
    </div>
  </div>
);

const DropdownSkeleton = () => (
  <div className="absolute z-50 left-0 mt-2 w-72 bg-white rounded-md shadow-lg border border-gray-200">
    <div className="py-2 divide-y divide-gray-100">
      <div className="px-4 py-2">
        <div className="h-4 w-24 bg-gray-200 rounded animate-pulse mb-2"></div>
        <div className="space-y-2">
          {[1, 2, 3, 4, 5].map((item) => (
            <div key={item} className="flex items-center px-2 py-1.5">
              <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse mr-2"></div>
              <div className="h-4 w-32 bg-gray-200 rounded animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>
);

export default function ProductList() {
  const [categories, setCategories] = useState([]);
  const [brands, setBrands] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isProductsDropdownOpen, setIsProductsDropdownOpen] = useState(false);
  const [isBrandsDropdownOpen, setIsBrandsDropdownOpen] = useState(false);
  const [activeCategoryDropdown, setActiveCategoryDropdown] = useState(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const productsDropdownRef = useRef(null);
  const brandsDropdownRef = useRef(null);
  const scrollContainerRef = useRef(null);
  const mobileMenuRef = useRef(null);

  // Fetch data on component mount
  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);

        // Fetch categories and brands in parallel
        const [categoriesResponse, brandsResponse] = await Promise.all([
          getCategoriesTree(),
          getAllBrands()
        ]);

        // Process categories
        if (categoriesResponse.success && categoriesResponse.data?.categories) {
          // Filter only featured and active categories for the navbar
          const featuredCategories = categoriesResponse.data.categories.filter(category =>
            category.is_featured &&
            category.is_active &&
            !['test1', 'test2', 'test3', 'test4', 'test5'].includes(category.slug) // Filter out test categories
          );

          // Sort by sort_order if available, then by name
          const sortedCategories = featuredCategories.sort((a, b) => {
            if (a.sort_order && b.sort_order) {
              return parseInt(a.sort_order) - parseInt(b.sort_order);
            }
            return a.name.localeCompare(b.name);
          });

          setCategories(sortedCategories);
        }

        // Process brands
        if (brandsResponse.success && brandsResponse.data?.brands) {
          // Filter only featured and active brands
          const featuredBrands = brandsResponse.data.brands.filter(brand =>
            brand.is_featured &&
            brand.is_active &&
            brand.product_count > 0 // Only show brands with products
          );

          // Sort by sort_order if available, then by product_count (descending), then by name
          const sortedBrands = featuredBrands.sort((a, b) => {
            if (a.sort_order && b.sort_order) {
              return parseInt(a.sort_order) - parseInt(b.sort_order);
            }
            if (a.product_count !== b.product_count) {
              return b.product_count - a.product_count; // Descending order
            }
            return a.name.localeCompare(b.name);
          });

          setBrands(sortedBrands.slice(0, 10)); // Limit to top 10 brands
        }

      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data');
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (productsDropdownRef.current && !productsDropdownRef.current.contains(event.target)) {
        setIsProductsDropdownOpen(false);
        setActiveCategoryDropdown(null);
      }
      if (brandsDropdownRef.current && !brandsDropdownRef.current.contains(event.target)) {
        setIsBrandsDropdownOpen(false);
      }
      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target)) {
        setIsMobileMenuOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isMobileMenuOpen]);

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -200, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 200, behavior: 'smooth' });
    }
  };



  const toggleCategoryDropdown = (index) => {
    if (activeCategoryDropdown === index) {
      setActiveCategoryDropdown(null);
    } else {
      setActiveCategoryDropdown(index);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="bg-white shadow-md border-gray-200 py-3">
        {/* Desktop View */}
        <div className="hidden md:block">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            <div className="flex items-center h-20 relative">
              {/* Loading dropdowns */}
              <div className="relative mr-4 flex-shrink-0">
                <div className="h-6 w-24 bg-gray-200 rounded animate-pulse"></div>
              </div>
              <div className="relative mr-4 flex-shrink-0">
                <div className="h-6 w-16 bg-gray-200 rounded animate-pulse"></div>
              </div>

              {/* Loading category cards */}
              <div className="flex items-center space-x-4 overflow-x-auto hide-scrollbar py-2 flex-grow">
                {[1, 2, 3, 4, 5, 6, 7, 8].map((item) => (
                  <CategorySkeleton key={item} />
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Mobile View */}
        <div className="md:hidden">
          <div className="px-2 py-3 overflow-x-auto hide-scrollbar">
            <div className="flex space-x-3">
              {[1, 2, 3, 4, 5, 6].map((item) => (
                <div key={item} className="flex flex-col items-center text-center flex-shrink-0">
                  <div className="w-20 h-20 rounded-lg border border-gray-200 bg-gray-100 animate-pulse flex flex-col items-center justify-center p-1">
                    <div className="h-8 w-8 bg-gray-200 rounded mb-1 animate-pulse"></div>
                    <div className="h-3 w-12 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state (but don't break the layout)
  if (error) {
    console.error('ProductList error:', error);
    return null; // Hide component on error
  }

  // Don't render if no categories
  if (!categories || categories.length === 0) {
    return null;
  }

  return (
    <div className="bg-white shadow-md border-gray-200 py-3">
      {/* Desktop View */}
      <div className="hidden md:block">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="flex items-center h-20 relative">
            {/* All Products Dropdown */}
            <div className="relative mr-4 flex-shrink-0" ref={productsDropdownRef}>
              <button
                onClick={() => setIsProductsDropdownOpen(!isProductsDropdownOpen)}
                className="flex items-center text-gray-700 hover:text-orange-500 font-medium transition-colors"
              >
                All Products
                <svg className="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {isProductsDropdownOpen && (
                <div className="absolute z-50 left-0 mt-2 w-72 bg-white rounded-md shadow-lg border border-gray-200">
                  <div className="py-2 divide-y divide-gray-100">
                    <div className="px-4 py-2">
                      <h3 className="text-sm font-semibold text-gray-900 mb-2">Browse by Category</h3>
                      <div className="space-y-2 max-h-64 overflow-y-auto">
                        {categories.filter(cat => cat.product_count > 0).map((category) => (
                          <Link
                            key={category.id}
                            href={`/products/categories/${category.id}`}
                            className="flex items-center px-2 py-1.5 text-sm text-gray-700 hover:bg-orange-100 hover:text-orange-500 rounded transition-colors"
                          >
                            {category.image ? (
                              <img
                                src={category.image}
                                alt={category.name}
                                className="w-8 h-8 rounded-full object-cover mr-2"
                                onError={(e) => {
                                  // Hide broken image and show placeholder instead
                                  e.target.style.display = 'none';
                                  const placeholder = document.createElement('div');
                                  placeholder.className = 'w-8 h-8 rounded-full bg-gray-200 mr-2 flex items-center justify-center';
                                  placeholder.innerHTML = `<span class="text-xs text-gray-500 font-medium">${category.name.charAt(0).toUpperCase()}</span>`;
                                  e.target.parentNode.insertBefore(placeholder, e.target);
                                }}
                              />
                            ) : (
                              <div className="w-8 h-8 rounded-full bg-gray-200 mr-2 flex items-center justify-center">
                                <span className="text-xs text-gray-500 font-medium">{category.name.charAt(0).toUpperCase()}</span>
                              </div>
                            )}
                            <span className="flex-1">{category.name}</span>
                            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                              {category.product_count}
                            </span>
                          </Link>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* task!!! prodcuts under brand id */}
            {/* Brands Dropdown */}
            {/* <div className="relative mr-4 flex-shrink-0" ref={brandsDropdownRef}>
              <button
                onClick={() => setIsBrandsDropdownOpen(!isBrandsDropdownOpen)}
                className="flex items-center text-gray-700 hover:text-orange-500 font-medium transition-colors"
              >
                Brands
                <svg className="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {isBrandsDropdownOpen && (
                <div className="absolute z-50 left-0 mt-2 w-72 bg-white rounded-md shadow-lg border border-gray-200">
                  <div className="py-2 divide-y divide-gray-100">
                    <div className="px-4 py-2">
                      <h3 className="text-sm font-semibold text-gray-900 mb-2">Browse by Brand</h3>
                      <div className="space-y-2 max-h-64 overflow-y-auto">
                        {brands.map((brand) => (
                          <Link
                            key={brand.id}
                            href={`/products/brands/${brand.id}`}
                            className="flex items-center px-2 py-1.5 text-sm text-gray-700 hover:bg-orange-100 hover:text-orange-500 rounded transition-colors"
                          >
                            {brand.logo ? (
                              <img
                                src={brand.logo}
                                alt={brand.name}
                                className="w-8 h-8 rounded-full object-cover mr-2"
                                onError={(e) => {
                                  // Hide broken image and show placeholder instead
                                  e.target.style.display = 'none';
                                  const placeholder = document.createElement('div');
                                  placeholder.className = 'w-8 h-8 rounded-full bg-gray-200 mr-2 flex items-center justify-center';
                                  placeholder.innerHTML = `<span class="text-xs text-gray-500 font-medium">${brand.name.charAt(0).toUpperCase()}</span>`;
                                  e.target.parentNode.insertBefore(placeholder, e.target);
                                }}
                              />
                            ) : (
                              <div className="w-8 h-8 rounded-full bg-gray-200 mr-2 flex items-center justify-center">
                                <span className="text-xs text-gray-500 font-medium">{brand.name.charAt(0).toUpperCase()}</span>
                              </div>
                            )}
                            <span className="flex-1">{brand.name}</span>
                            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                              {brand.product_count}
                            </span>
                          </Link>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div> */}

            {/* Scroll Left Button */}
            <button
              onClick={scrollLeft}
              className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-white shadow-md rounded-full p-2 hover:bg-gray-50 transition-colors"
              style={{ marginLeft: '200px' }}
            >
              <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            {/* Category Cards Container */}
            <div
              ref={scrollContainerRef}
              className="flex items-center space-x-4 overflow-x-auto hide-scrollbar py-2 flex-grow"
              style={{ scrollBehavior: 'smooth' }}
            >
              {categories.map((category) => (
                <div key={category.id} className="flex flex-col items-center text-center group flex-shrink-0">
                  <Link href={`/products/categories/${category.id}`}>
                    <div className="w-24 h-20 rounded-lg border border-gray-200 hover:border-orange-300 transition-colors bg-white hover:shadow-md flex flex-col items-center justify-center p-1 cursor-pointer">
                      {category.image ? (
                        <img
                          src={category.image}
                          alt={category.name}
                          className="h-12 w-12 object-cover rounded mb-1"
                          onError={(e) => {
                            // Hide broken image and show placeholder instead
                            e.target.style.display = 'none';
                            const placeholder = document.createElement('div');
                            placeholder.className = 'h-12 w-12 bg-gray-200 rounded mb-1 flex items-center justify-center';
                            placeholder.innerHTML = `<span class="text-lg text-gray-500 font-medium">${category.name.charAt(0).toUpperCase()}</span>`;
                            e.target.parentNode.insertBefore(placeholder, e.target);
                          }}
                        />
                      ) : (
                        <div className="h-12 w-12 bg-gray-200 rounded mb-1 flex items-center justify-center">
                          <span className="text-lg text-gray-500 font-medium">{category.name.charAt(0).toUpperCase()}</span>
                        </div>
                      )}
                      <span className="text-xs font-medium text-gray-700 group-hover:text-orange-500 transition-colors text-center leading-tight">
                        {category.name}
                        {category.product_count > 0 && (
                          <span className="block text-xs text-gray-500">({category.product_count})</span>
                        )}
                      </span>
                    </div>
                  </Link>
                </div>
              ))}
            </div>

            {/* Scroll Right Button */}
            <button
              onClick={scrollRight}
              className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-white shadow-md rounded-full p-2 hover:bg-gray-50 transition-colors"
            >
              <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile View */}
      <div className="md:hidden">
        <div className="px-2 py-3 overflow-x-auto hide-scrollbar">
          <div className="flex space-x-3">
            {categories.map((category) => (
              <div key={category.id} className="flex flex-col items-center text-center flex-shrink-0">
                <Link href={`/products/categories/${category.id}`}>
                  <div className="w-20 h-20 rounded-lg border border-gray-200 hover:border-orange-300 transition-colors bg-white hover:shadow-md flex flex-col items-center justify-center p-1 cursor-pointer">
                    {category.image ? (
                      <img
                        src={category.image}
                        alt={category.name}
                        className="h-8 w-8 object-cover rounded mb-1"
                        onError={(e) => {
                          // Hide broken image and show placeholder instead
                          e.target.style.display = 'none';
                          const placeholder = document.createElement('div');
                          placeholder.className = 'h-8 w-8 bg-gray-200 rounded mb-1 flex items-center justify-center';
                          placeholder.innerHTML = `<span class="text-sm text-gray-500 font-medium">${category.name.charAt(0).toUpperCase()}</span>`;
                          e.target.parentNode.insertBefore(placeholder, e.target);
                        }}
                      />
                    ) : (
                      <div className="h-8 w-8 bg-gray-200 rounded mb-1 flex items-center justify-center">
                        <span className="text-sm text-gray-500 font-medium">{category.name.charAt(0).toUpperCase()}</span>
                      </div>
                    )}
                    <span className="text-xs font-medium text-gray-700 text-center leading-tight">
                      {category.name.length > 10 ? category.name.substring(0, 10) + '...' : category.name}
                      {category.product_count > 0 && (
                        <span className="block text-xs text-gray-500">({category.product_count})</span>
                      )}
                    </span>
                  </div>
                </Link>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Custom CSS for hiding scrollbar */}
      <style jsx>{`
        .hide-scrollbar {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .hide-scrollbar::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  );
}