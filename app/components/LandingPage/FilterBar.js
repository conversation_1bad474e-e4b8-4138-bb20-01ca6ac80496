'use client';
import { useState, useEffect } from 'react';
import Image from 'next/image'; // Added import

export default function FilterBar() {
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState({
    categories: [],
    priceRange: [0, 1000],
    rating: null,
    availability: 'all',
    distributor: [],
    discount: false
  });

  const [activeFiltersCount, setActiveFiltersCount] = useState(0);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);

    // Count active filters
    let count = 0;
    if (selectedFilters.categories.length > 0) count++;
    if (selectedFilters.priceRange[0] > 0 || selectedFilters.priceRange[1] < 1000) count++;
    if (selectedFilters.rating !== null) count++;
    if (selectedFilters.availability !== 'all') count++;
    if (selectedFilters.distributor.length > 0) count++;
    if (selectedFilters.discount) count++;

    setActiveFiltersCount(count);
  }, [selectedFilters]);

  const categories = [
    { id: 'electronics', name: 'Electronics', icon: '🖥️' },
    { id: 'apparel', name: 'Apparel', icon: '👕' },
    { id: 'food', name: 'Food & Beverages', icon: '🍽️' },
    { id: 'home', name: 'Home & Garden', icon: '🏡' },
    { id: 'health', name: 'Health & Beauty', icon: '💆' },
    { id: 'toys', name: 'Toys & Games', icon: '🎮' }
  ];

  const distributors = [
    { id: 'tech-elite', name: 'TechElite Inc.', rating: 4.8 },
    { id: 'eco-apparel', name: 'EcoApparel', rating: 4.5 },
    { id: 'home-goods', name: 'HomeGoods Direct', rating: 4.7 },
    { id: 'global-foods', name: 'Global Foods', rating: 4.9 },
    { id: 'fitness-supplies', name: 'FitLife Supplies', rating: 4.6 }
  ];

  const handleCategoryChange = (categoryId) => {
    setSelectedFilters(prev => {
      const isSelected = prev.categories.includes(categoryId);
      return {
        ...prev,
        categories: isSelected
          ? prev.categories.filter(id => id !== categoryId)
          : [...prev.categories, categoryId]
      };
    });
  };

  const handleDistributorChange = (distributorId) => {
    setSelectedFilters(prev => {
      const isSelected = prev.distributor.includes(distributorId);
      return {
        ...prev,
        distributor: isSelected
          ? prev.distributor.filter(id => id !== distributorId)
          : [...prev.distributor, distributorId]
      };
    });
  };

  const handleRatingChange = (rating) => {
    setSelectedFilters(prev => ({
      ...prev,
      rating: prev.rating === rating ? null : rating
    }));
  };

  const handlePriceChange = (e, index) => {
    const value = parseInt(e.target.value);
    setSelectedFilters(prev => {
      const newPriceRange = [...prev.priceRange];
      newPriceRange[index] = value;
      return { ...prev, priceRange: newPriceRange };
    });
  };

  const clearAllFilters = () => {
    setSelectedFilters({
      categories: [],
      priceRange: [0, 1000],
      rating: null,
      availability: 'all',
      distributor: [],
      discount: false
    });
  };

  // Handle filter visibility without AnimatePresence
  const filterVisible = isFilterOpen || (isMounted && typeof window !== 'undefined' && !window.matchMedia('(max-width: 768px)').matches);

  return (
    <section className="py-12 bg-gradient-to-b from-gray-50 to-white">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="flex flex-wrap justify-between items-center mb-8">
          <div className="flex items-center space-x-4">
            <h2 className="text-2xl font-bold text-gray-900">Product Discovery</h2>
            {activeFiltersCount > 0 && (
              <span className="bg-indigo-100 text-indigo-800 text-sm font-medium px-3 py-1 rounded-full">
                {activeFiltersCount} {activeFiltersCount === 1 ? 'filter' : 'filters'} applied
              </span>
            )}
          </div>

          <button
            onClick={() => setIsFilterOpen(!isFilterOpen)}
            className="md:hidden flex items-center px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
            </svg>
            {isFilterOpen ? 'Hide Filters' : 'Show Filters'}
          </button>
        </div>

        <div className="md:grid md:grid-cols-12 gap-8">
          {/* Filter sidebar */}
          {filterVisible && (
            <div
              className="md:col-span-3 mb-6 md:mb-0 transition-all duration-300 ease-in-out"
              style={{
                opacity: isMounted ? 1 : 0,
                transform: isMounted ? 'translateX(0)' : 'translateX(-20px)'
              }}
            >
              <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
                <div className="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-4">
                  <h3 className="font-bold text-white text-lg flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
                    </svg>
                    Filter Options
                  </h3>
                </div>

                <div className="p-4 space-y-6">
                  {/* Categories */}
                  <div className="border-b border-gray-200 pb-6">
                    <h4 className="font-semibold text-gray-800 mb-4 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5 4a3 3 0 00-3 3v6a3 3 0 003 3h10a3 3 0 003-3V7a3 3 0 00-3-3H5zm-1 9v-1h5v2H5a1 1 0 01-1-1zm7 1h4a1 1 0 001-1v-1h-5v2zm0-4h5V8h-5v2zM9 8H4v2h5V8z" clipRule="evenodd" />
                      </svg>
                      Categories
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {categories.map((category) => (
                        <div
                          key={category.id}
                          className={`
                            flex items-center p-2 rounded-lg cursor-pointer transition-all flex-wrap gap-2 justify-center
                            ${selectedFilters.categories.includes(category.id)
                              ? 'bg-indigo-100 text-indigo-800 border-indigo-200'
                              : 'bg-gray-50 text-gray-700 hover:bg-gray-100 border-gray-100'
                            } border
                          `}
                          onClick={() => handleCategoryChange(category.id)}>
                          <span className="">{category.icon}</span>
                          <span className="text-sm font-medium">{category.name}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Price Range */}
                  <div className="border-b border-gray-200 pb-6">
                    <h4 className="font-semibold text-gray-800 mb-4 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                      </svg>
                      Price Range
                    </h4>

                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <div className="bg-gray-50 rounded-lg p-2 flex-grow mr-3">
                          <label className="block text-xs text-gray-500 mb-1">Min ($)</label>
                          <input
                            type="number"
                            value={selectedFilters.priceRange[0]}
                            onChange={(e) => handlePriceChange(e, 0)}
                            className="w-full bg-transparent focus:outline-none text-gray-800 font-medium"
                            min="0"
                            max={selectedFilters.priceRange[1]}
                          />
                        </div>
                        <div className="bg-gray-50 rounded-lg p-2 flex-grow">
                          <label className="block text-xs text-gray-500 mb-1">Max ($)</label>
                          <input
                            type="number"
                            value={selectedFilters.priceRange[1]}
                            onChange={(e) => handlePriceChange(e, 1)}
                            className="w-full bg-transparent focus:outline-none text-gray-800 font-medium"
                            min={selectedFilters.priceRange[0]}
                          />
                        </div>
                      </div>

                      <div className="relative pt-2">
                        <div className="h-1 bg-gray-200 rounded-full"></div>
                        <div
                          className="absolute h-1 bg-indigo-500 rounded-full"
                          style={{
                            left: `${(selectedFilters.priceRange[0] / 1000) * 100}%`,
                            right: `${100 - (selectedFilters.priceRange[1] / 1000) * 100}%`
                          }}
                        ></div>
                      </div>

                      <div className="flex justify-between text-xs text-gray-500">
                        <span>$0</span>
                        <span>$500</span>
                        <span>$1000+</span>
                      </div>
                    </div>
                  </div>

                  {/* Rating */}
                  <div className="border-b border-gray-200 pb-6">
                    <h4 className="font-semibold text-gray-800 mb-4 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                      Rating
                    </h4>

                    <div className="space-y-2">
                      {[5, 4, 3, 2, 1].map((rating) => (
                        <div
                          key={rating}
                          className={`
                            flex items-center justify-between p-2 rounded-lg cursor-pointer
                            ${selectedFilters.rating === rating
                              ? 'bg-indigo-100 text-indigo-800'
                              : 'hover:bg-gray-50'
                            }
                          `}
                          onClick={() => handleRatingChange(rating)}
                        >
                          <div className="flex items-center">
                            {Array(rating).fill().map((_, i) => (
                              <svg key={i} xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                              </svg>
                            ))}
                            {Array(5 - rating).fill().map((_, i) => (
                              <svg key={i} xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                              </svg>
                            ))}
                            <span className="ml-2 text-sm">{rating}+ stars</span>
                          </div>

                          {selectedFilters.rating === rating && (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Availability */}
                  <div className="border-b border-gray-200 pb-6">
                    <h4 className="font-semibold text-gray-800 mb-4 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                      </svg>
                      Availability
                    </h4>
                    <div className="flex gap-5 flex-wrap">
                      <button
                        onClick={() => setSelectedFilters(prev => ({ ...prev, availability: 'all' }))}
                        className={`
                          px-4 py-2 rounded-lg text-sm flex-1 transition-colors
                          ${selectedFilters.availability === 'all'
                            ? 'bg-indigo-500 text-white'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }
                        `}
                      >
                        All Items
                      </button>
                      <button
                        onClick={() => setSelectedFilters(prev => ({ ...prev, availability: 'in-stock' }))}
                        className={`
                          px-4 py-2 rounded-lg text-sm flex-1 transition-colors
                          ${selectedFilters.availability === 'in-stock'
                            ? 'bg-indigo-500 text-white'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }
                        `}
                      >
                        In Stock Only
                      </button>
                    </div>
                  </div>

                  {/* Distributors */}
                  <div className="border-b border-gray-200 pb-6">
                    <h4 className="font-semibold text-gray-800 mb-4 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
                        <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1v-5a1 1 0 00-.293-.707l-2-2A1 1 0 0015 7h-1z" />
                      </svg>
                      Distributors
                    </h4>
                    <div className="space-y-2 max-h-44 overflow-y-auto pr-2">
                      {distributors.map((distributor) => (
                        <div
                          key={distributor.id}
                          className={`
                            flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all
                            ${selectedFilters.distributor.includes(distributor.id)
                              ? 'bg-indigo-100 text-indigo-800 border-indigo-200'
                              : 'bg-gray-50 text-gray-700 hover:bg-gray-100 border-gray-100'
                            } border
                          `}
                          onClick={() => handleDistributorChange(distributor.id)}
                        >
                          <div>
                            <div className="font-medium text-sm">{distributor.name}</div>
                            <div className="flex items-center mt-1">
                              {Array(Math.floor(distributor.rating)).fill().map((_, i) => (
                                <svg key={i} xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                </svg>
                              ))}
                              {distributor.rating % 1 >= 0.5 && (
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                </svg>
                              )}
                              <span className="text-xs text-gray-500 ml-1">{distributor.rating.toFixed(1)}</span>
                            </div>
                          </div>

                          {selectedFilters.distributor.includes(distributor.id) && (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Discount toggle */}
                  <div className="mb-4">
                    <label className="flex items-center cursor-pointer">
                      <div className="relative">
                        <input
                          type="checkbox"
                          className="sr-only"
                          checked={selectedFilters.discount}
                          onChange={() => setSelectedFilters(prev => ({ ...prev, discount: !prev.discount }))}
                        />
                        <div className={`block w-10 h-6 rounded-full transition-colors ${selectedFilters.discount ? 'bg-orange-400' : 'bg-gray-300'}`}></div>
                        <div className={`absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform ${selectedFilters.discount ? 'transform translate-x-4' : ''}`}></div>
                      </div>
                      <div className="ml-3 text-gray-700">
                        Discounted Items Only
                        {selectedFilters.discount && (
                          <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                            Sale
                          </span>
                        )}
                      </div>
                    </label>
                  </div>

                  <button
                    onClick={clearAllFilters}
                    className="w-full py-3 bg-white border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                    </svg>
                    Clear All Filters
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Content area */}
          <div
            className="md:col-span-9"
            style={{
              opacity: isMounted ? 1 : 0,
              transition: 'opacity 0.4s ease-in-out'
            }}
          >
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
              {/* Search & Sort controls */}
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <div className="flex flex-wrap justify-between items-center gap-4">
                  <div className="relative flex-grow max-w-md">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    </div>
                    <input
                      type="search"
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:border-transparent"
                      placeholder="Search products..."
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">Sort by:</span>
                    <select className="border border-gray-300 rounded-lg bg-white py-2 pl-3 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:border-transparent">
                      <option>Most Relevant</option>
                      <option>Price: Low to High</option>
                      <option>Price: High to Low</option>
                      <option>Newest First</option>
                      <option>Best Rated</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Results area - placeholder for filtered products */}
              <div className="p-8">
                <div className="flex flex-col items-center text-center">
                  <div className="h-40 w-40 mb-6 relative"> {/* Added relative positioning */}
                    <Image
                      src="https://illustrations.popsy.co/amber/app-launch.svg"
                      alt="Filter Products"
                      className="object-contain"
                      fill
                      sizes="(max-width: 768px) 100vw, 160px"
                    />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">Apply Filters to See Results</h3>
                  <p className="text-gray-600 max-w-md mx-auto">
                    Use the filters on the left to narrow down your product search. Your filtered results will appear here in real-time.
                  </p>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-12 w-full max-w-2xl">
                    <div className="aspect-square rounded-xl bg-gray-100 animate-pulse"></div>
                    <div className="aspect-square rounded-xl bg-gray-100 animate-pulse"></div>
                    <div className="aspect-square rounded-xl bg-gray-100 animate-pulse"></div>
                    <div className="aspect-square rounded-xl bg-gray-100 animate-pulse"></div>
                    <div className="aspect-square rounded-xl bg-gray-100 animate-pulse"></div>
                    <div className="aspect-square rounded-xl bg-gray-100 animate-pulse"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
