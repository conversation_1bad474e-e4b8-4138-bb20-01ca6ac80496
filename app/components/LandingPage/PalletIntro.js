"use client";

import Link from 'next/link';
import { motion } from 'framer-motion';

export default function PalletIntro() {
  return (
    <section className="py-20 bg-gradient-to-b from-orange-50 to-white">
      <div className="container mx-auto px-6 md:px-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="flex flex-col md:flex-row items-center gap-8 md:gap-16 relative overflow-hidden"
        >
          {/* Visual representation of pallet concept */}
          <div className="w-full md:w-1/2 relative h-[350px] md:h-[480px]">
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              whileInView={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="absolute inset-0 bg-gradient-to-br from-blue-700 to-purple-700 rounded-2xl overflow-hidden shadow-xl"
            >
              <div className="absolute inset-0 opacity-10"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="relative w-[80%] h-[80%]">
                  {/* 3D Pallet Illustration */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="relative w-[280px] h-[200px] md:w-[350px] md:h-[250px] perspective-effect">
                      {/* Pallet Base */}
                      <div className="absolute bottom-0 w-full h-[40px] bg-amber-800 rounded-md transform-3d"></div>

                      {/* Product Boxes */}
                      <motion.div
                        initial={{ y: -20, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ delay: 0.5, duration: 0.5 }}
                        className="absolute bottom-[40px] left-[20px] w-[70px] h-[80px] bg-white rounded-md shadow-lg border border-gray-200"
                      >
                        <div className="h-[15px] bg-orange-500 w-full rounded-t-md"></div>
                      </motion.div>

                      <motion.div
                        initial={{ y: -20, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ delay: 0.7, duration: 0.5 }}
                        className="absolute bottom-[40px] left-[100px] w-[90px] h-[100px] bg-white rounded-md shadow-lg border border-gray-200"
                      >
                        <div className="h-[15px] bg-green-500 w-full rounded-t-md"></div>
                      </motion.div>

                      <motion.div
                        initial={{ y: -20, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ delay: 0.9, duration: 0.5 }}
                        className="absolute bottom-[40px] right-[30px] w-[80px] h-[120px] bg-white rounded-md shadow-lg border border-gray-200"
                      >
                        <div className="h-[15px] bg-purple-500 w-full rounded-t-md"></div>
                      </motion.div>

                      {/* Pallet Fill Status Indicator */}
                      <div className="absolute top-[-30px] left-0 right-0 flex justify-center">
                        <div className="bg-white bg-opacity-90 px-4 py-1 rounded-full shadow-md flex items-center">
                          <div className="w-[50px] h-[6px] bg-gray-200 rounded-full mr-2">
                            <div className="w-[60%] h-full bg-gradient-to-r from-orange-400 to-orange-500 rounded-full"></div>
                          </div>
                          <span className="text-xs font-medium text-gray-700">60% filled</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Floating Elements */}
            <motion.div
              animate={{
                y: [0, -10, 0],
                x: [0, 5, 0]
              }}
              transition={{
                repeat: Infinity,
                duration: 5,
                ease: "easeInOut"
              }}
              className="absolute top-[50px] right-[50px] w-12 h-12 rounded-full bg-yellow-400 bg-opacity-20 backdrop-blur-sm z-10"
            ></motion.div>

            <motion.div
              animate={{
                y: [0, 15, 0],
                x: [0, -8, 0]
              }}
              transition={{
                repeat: Infinity,
                duration: 7,
                ease: "easeInOut",
                delay: 1
              }}
              className="absolute bottom-[70px] left-[40px] w-16 h-16 rounded-full bg-orange-400 bg-opacity-20 backdrop-blur-sm z-10"
            ></motion.div>
          </div>

          {/* Content */}
          <div className="w-full md:w-1/2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900 leading-tight">
                Shop <span className="text-orange-400">Smarter</span> With Pallet Orders
              </h2>
              <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                <span className='text-orange-500'>D</span>iptouch is designed for wholesale buyers. Purchase products in any quantity,
                but maximize your efficiency with our pallet-based shopping system — optimized
                for serious retailers.
              </p>

              <div className="bg-white rounded-xl shadow-sm p-6 mb-8 border border-gray-100">
                <ul className="space-y-4">
                  {[
                    {
                      title: "Custom Pallet Creation",
                      desc: "Mix and match products from multiple brands into one pallet",
                      icon: "📦"
                    },
                    {
                      title: "Optimized Shipping",
                      desc: "Get better shipping rates with consolidated orders",
                      icon: "🚚"
                    },
                    {
                      title: "Inventory Management",
                      desc: "Streamline inventory replenishment with bulk ordering",
                      icon: "📋"
                    },
                    {
                      title: "Real-Time Tracking",
                      desc: "Track pallet fill status in real time as you shop",
                      icon: "📊"
                    }
                  ].map((item, i) => (
                    <motion.li
                      key={i}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 0.4 + (i * 0.1) }}
                      viewport={{ once: true }}
                      className="flex items-start group"
                    >
                      <div className="h-10 w-10 rounded-full bg-orange-100 flex items-center justify-center text-lg mr-4 group-hover:bg-orange-200 hover:scale-125 transition-all duration: 300">
                        {item.icon}
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{item.title}</h3>
                        <p className="text-gray-600 text-sm">{item.desc}</p>
                      </div>
                    </motion.li>
                  ))}
                </ul>
              </div>

              <motion.div
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.97 }}
              >
                <Link
                  href="/products"
                  className="group px-8 py-4 bg-gradient-to-r from-yellow-300 to-orange-400 text-white font-medium rounded-xl hover:to-yellow-300 hover:from-orange-500 transition duration-300 inline-flex items-center shadow-lg hover:shadow-blue-500/30"
                >
                  Start Filling Your Pallet
                  <svg className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </Link>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </div>

      <style jsx>{`
        .perspective-effect {
          transform: perspective(1000px) rotateX(10deg) rotateY(-10deg);
        }
        .transform-3d {
          transform: perspective(1000px) rotateX(70deg);
        }
      `}</style>
    </section>
  );
}
