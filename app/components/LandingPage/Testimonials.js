'use client';
import { useState, useEffect, useRef, useCallback } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';

export default function Testimonials() {
  const [activeIndex, setActiveIndex] = useState(0);
  const [isManualChange, setIsManualChange] = useState(false);
  const intervalRef = useRef(null);

  const testimonials = [
    {
      id: 1,
      text: "Diptouch has transformed how we source products for our retail chain. The pallet optimization system alone has saved us thousands in shipping costs, and the streamlined ordering process has cut our procurement time by 60%.",
      name: "<PERSON>",
      role: "Purchasing Manager",
      company: "RetailPlus Group",
      image: "https://placehold.co/100x100/f3f4f6/64748b?text=SJ",
      rating: 5
    },
    {
      id: 2,
      text: "As a small distributor, we struggled to compete with larger players until we joined Diptouch. Now we have access to buyers across the country, and our sales have increased by 75% in just six months. The platform is intuitive and the support team is always ready to help.",
      name: "<PERSON>",
      role: "Owner",
      company: "Chen Distribution Co.",
      image: "https://placehold.co/100x100/f3f4f6/64748b?text=MC",
      rating: 4.8
    },
    {
      id: 3,
      text: "We've been using Diptouch for over a year now, and it's become an integral part of our supply chain. The ability to track our orders in real-time and manage our inventory through the platform has improved our operational efficiency tremendously.",
      name: "Priya Patel",
      role: "Supply Chain Director",
      company: "Global Foods Inc.",
      image: "https://placehold.co/100x100/f3f4f6/64748b?text=PP",
      rating: 5
    }
  ];

  const resetInterval = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    if (!isManualChange) {
      intervalRef.current = setInterval(() => {
        setActiveIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
      }, 8000);
    }
  }, [isManualChange, testimonials.length]);

  // Auto-rotate testimonials
  useEffect(() => {
    resetInterval();
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [resetInterval]);

  // Handle manual navigation
  const handleDotClick = (index) => {
    setActiveIndex(index);
    setIsManualChange(true);
    setTimeout(() => setIsManualChange(false), 5000);
    resetInterval();
  };

  // Generate star ratings
  const renderStars = (rating) => {
    return Array(5).fill(0).map((_, i) => {
      const isHalf = i + 0.5 === Math.floor(rating + 0.5);
      return (
        <svg
          key={i}
          className={`h-5 w-5 ${i < rating ? 'text-yellow-400' : 'text-gray-300'}`}
          fill={i < rating ? 'currentColor' : 'none'}
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          {i < rating ? (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
            />
          ) : (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
            />
          )}
        </svg>
      );
    });
  };

  const variants = {
    enter: (direction) => {
      return {
        x: direction > 0 ? 100 : -100,
        opacity: 0,
        scale: 0.95
      };
    },
    center: {
      x: 0,
      opacity: 1,
      scale: 1
    },
    exit: (direction) => {
      return {
        x: direction < 0 ? 100 : -100,
        opacity: 0,
        scale: 0.95
      };
    }
  };

  return (
    <section className="py-24 relative">
      <div className="absolute inset-0 bg-gradient-to-b from-gray-50 to-gray-100"></div>
      <div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-white to-transparent"></div>
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-white to-transparent"></div>

      <div className="container mx-auto px-6 md:px-12 relative z-10">
        <motion.div
          className="max-w-screen-lg mx-auto"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <div className="flex flex-col items-center text-center mb-16">
            <motion.div
              className="w-20 h-1.5 bg-gradient-to-r from-yellow-300 to-orange-400 rounded-full mb-6"
              initial={{ width: 0 }}
              whileInView={{ width: 200 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2, duration: 0.8 }}
            ></motion.div>
            <motion.h2
              className="text-4xl md:text-5xl font-bold mb-6 text-gray-900"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.1 }}
            >
              Trusted by Growing Businesses <span className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-orange-400">Across the Globe</span>
            </motion.h2>
          </div>

          <div className="relative overflow-hidden">
            <div className="absolute inset-1/2 w-[300px] h-[300px] -translate-x-1/2 -translate-y-1/2 bg-gradient-to-br from-yellow-300/20 to-orange-400/10 rounded-full blur-3xl"></div>
            <AnimatePresence initial={false} mode="wait" custom={activeIndex}>
              {testimonials.map((testimonial, index) => (
                activeIndex === index && (
                  <motion.div
                    key={testimonial.id}
                    custom={activeIndex}
                    variants={variants}
                    initial="enter"
                    animate="center"
                    exit="exit"
                    transition={{
                      x: { type: "spring", stiffness: 300, damping: 30 },
                      opacity: { duration: 0.5 }
                    }}
                    className="w-full"
                  >
                    <div className="bg-white rounded-2xl shadow-xl p-8 md:p-12 relative z-10">
                      <div className="absolute -top-5 right-10 text-orange-300 opacity-50 transform rotate-12">
                        <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" fill="currentColor" viewBox="0 0 256 256">
                          <path d="M116,72v88a48.05,48.05,0,0,1-48,48,8,8,0,0,1,0-16,32,32,0,0,0,32-32v-8H40a24,24,0,0,1-24-24V72A24,24,0,0,1,40,48h52A24,24,0,0,1,116,72ZM216,48H164a24,24,0,0,0-24,24v56a24,24,0,0,0,24,24h60v8a32,32,0,0,1-32,32,8,8,0,0,0,0,16,48.05,48.05,0,0,0,48-48V72A24,24,0,0,0,216,48Z"></path>
                        </svg>
                      </div>

                      <div className="flex gap-2 mb-6">
                        {renderStars(testimonial.rating)}
                      </div>

                      <p className="text-xl md:text-2xl text-gray-700 font-medium mb-10 leading-relaxed">
                        &ldquo;{testimonial.text}&rdquo;
                      </p>

                      <div className="flex items-center">
                        <div className="relative h-16 w-16 rounded-full overflow-hidden mr-5 border-2 border-white shadow-lg">
                          <Image
                            src={testimonial.image}
                            alt={testimonial.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-gray-900 mb-1">{testimonial.name}</h3>
                          <p className="text-orange-400">{testimonial.role}, <span className="text-gray-600">{testimonial.company}</span></p>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )
              ))}
            </AnimatePresence>

            {/* Navigation arrows */}
            <div className="flex justify-between mt-8">
              <button
                onClick={() => handleDotClick((activeIndex - 1 + testimonials.length) % testimonials.length)}
                className="p-3 rounded-full bg-white shadow-md text-gray-700 hover:bg-gray-50 hover:text-orange-500 transition-all duration-300"
                aria-label="Previous testimonial"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>

              {/* Navigation dots */}
              <div className="flex justify-center space-x-3 items-center">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => handleDotClick(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${index === activeIndex
                      ? 'bg-orange-400 w-8'
                      : 'bg-gray-300 hover:bg-gray-400'
                      }`}
                    aria-label={`View testimonial ${index + 1}`}
                  />
                ))}
              </div>

              <button
                onClick={() => handleDotClick((activeIndex + 1) % testimonials.length)}
                className="p-3 rounded-full bg-white shadow-md text-gray-700 hover:bg-gray-50 hover:text-orange-400 transition-all duration-300"
                aria-label="Next testimonial"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
