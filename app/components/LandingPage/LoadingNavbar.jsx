'use client';

import Link from 'next/link';

export default function LoadingNavbar() {
  return (
    <nav className="sticky top-0 z-50 bg-white shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16 sm:h-20 xl:h-16">
          {/* Logo */}
          <div className="flex-shrink-0 flex items-center">
            <Link href="/" className="font-bold text-xl text-black hover:text-orange-500 transition-colors">
              <span className='text-orange-500'>D</span>iptouch
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex space-x-4 mx-2">
            <Link href="/" className="inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-800 hover:text-orange-500 transition-colors border-b-2 border-transparent hover:border-orange-500">
              Home
            </Link>
            <Link href="/products" className="inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-800 hover:text-orange-500 transition-colors border-b-2 border-transparent hover:border-orange-500">
              All Products
            </Link>
          </div>

          {/* Right side - Loading state */}
          <div className="hidden md:flex items-center space-x-4">
            {/* Loading skeleton for auth section */}
            <div className="flex items-center space-x-3">
              <div className="animate-pulse">
                <div className="h-4 w-16 bg-gray-200 rounded"></div>
              </div>
              <div className="animate-pulse">
                <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
              </div>
            </div>

            {/* Cart */}
            <Link href="/cart" className="relative p-2 rounded-full hover:bg-orange-100 transition-all duration-300 hover:scale-110 group">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-700 group-hover:text-orange-500 transition-colors duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center space-x-3">
            <Link href="/cart" className="relative p-2 rounded-full hover:bg-orange-100 transition-all duration-300 hover:scale-110 group">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-700 hover:text-black transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </Link>

            <div className="animate-pulse">
              <div className="h-6 w-6 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}
