"use client";

import { useState, useEffect, useMemo } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import PrimaryGradientBtn from '../Buttons/PrimaryGradientBtn';

// API function to fetch all categories with complete pagination handling
export async function getAllCategories() {
  try {
    console.log('🔍 Fetching all categories from API...');
    let allCategories = [];
    let currentPage = 1;
    let hasMorePages = true;

    while (hasMorePages) {
      const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/categories?page=${currentPage}`, {
        cache: 'force-cache',
        next: { revalidate: 3600 }, // Revalidate every hour
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.data?.categories?.data) {
        // Filter out test categories and only include active categories
        const validCategories = data.data.categories.data.filter(category =>
          category.is_active &&
          !['test1', 'test2', 'test3', 'test4', 'test5'].some(test =>
            category.name.toLowerCase().includes(test) ||
            category.slug.toLowerCase().includes(test)
          )
        );

        allCategories = [...allCategories, ...validCategories];

        // Check if there are more pages
        hasMorePages = currentPage < data.data.categories.last_page;
        currentPage++;

        console.log(`✅ Fetched page ${currentPage - 1} for categories (${validCategories.length} valid categories)`);
      } else {
        hasMorePages = false;
      }
    }

    console.log(`✅ All categories fetched: ${allCategories.length} total categories`);
    return {
      success: true,
      message: 'Categories retrieved successfully',
      data: allCategories
    };
  } catch (error) {
    console.error('❌ Error fetching categories:', error);
    return {
      success: false,
      message: `Failed to fetch categories: ${error.message}`,
      data: []
    };
  }
}

// Fisher-Yates shuffle algorithm for randomizing categories array
const shuffleArray = (array) => {
  if (!array || array.length === 0) return [];

  const shuffled = [...array]; // Create a copy to avoid mutating original array
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

// Helper function to get category image URL
const getCategoryImageUrl = (category) => {
  if (!category) return null;

  // Handle multiple images separated by commas (take first one)
  if (category.image && typeof category.image === 'string' && category.image.trim() !== '') {
    const images = category.image.split(',');
    const firstImage = images[0].trim();

    // If it's a relative path, make it absolute
    if (firstImage.startsWith('/storage/')) {
      return `https://b2b.instinctfusionx.xyz${firstImage}`;
    }

    return firstImage;
  }

  return null;
};

// Category Image Component with fallback
const CategoryImage = ({ category, className = "" }) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  const imageUrl = getCategoryImageUrl(category);
  const hasValidImage = imageUrl && !imageError;

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {hasValidImage ? (
        <>
          <Image
            src={imageUrl}
            alt={category.name}
            fill
            sizes="(max-width: 768px) 100vw, 300px"
            className="object-cover transition-transform duration-500 group-hover:scale-110"
            onLoad={() => setImageLoading(false)}
            onError={() => {
              console.log(`❌ Image failed to load for category ${category.id}:`, imageUrl);
              setImageError(true);
              setImageLoading(false);
            }}
          />
          {imageLoading && (
            <div className="absolute inset-0 bg-gray-200 flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-400"></div>
            </div>
          )}
        </>
      ) : (
        // Gray div placeholder with category initials
        <div className="absolute inset-0 bg-gray-200 flex items-center justify-center">
          <div className="text-center text-gray-500">
            <div className="w-16 h-16 mx-auto mb-2 bg-gray-300 rounded-full flex items-center justify-center">
              <span className="text-2xl font-bold">
                {category.name ? category.name.charAt(0).toUpperCase() : 'C'}
              </span>
            </div>
            <span className="text-sm font-medium">
              {imageUrl ? 'Image Failed' : 'No Image'}
            </span>
          </div>
        </div>
      )}

      <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300"></div>
    </div>
  );
};

// Loading skeleton component for categories
const CategorySkeleton = () => (
  <div className="h-full rounded-2xl overflow-hidden shadow-md bg-white">
    <div className="relative h-52 w-full bg-gray-200 animate-pulse"></div>
    <div className="p-5 space-y-3">
      <div className="flex justify-between items-center">
        <div className="h-6 w-3/4 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-6 w-16 bg-gray-200 rounded-full animate-pulse"></div>
      </div>
      <div className="h-4 w-1/2 bg-gray-200 rounded animate-pulse"></div>
    </div>
  </div>
);

export default function CategoryGrid() {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Memoize the shuffled categories to maintain consistency during component lifecycle
  const shuffledCategories = useMemo(() => {
    if (categories.length === 0) return [];

    console.log(`🔀 Randomizing ${categories.length} categories for CategoryGrid`);
    const shuffled = shuffleArray(categories);

    // Return only 10 random categories for display
    return shuffled.slice(0, 10);
  }, [categories]);

  // Fetch categories on component mount
  useEffect(() => {
    async function fetchCategories() {
      try {
        setLoading(true);
        const response = await getAllCategories();

        if (response.success && response.data) {
          setCategories(response.data);
        } else {
          setError(response.message || 'Failed to load categories');
        }
      } catch (err) {
        console.error('❌ Error fetching categories:', err);
        setError('Failed to load categories');
      } finally {
        setLoading(false);
      }
    }

    fetchCategories();
  }, []);

  // Show loading state
  if (loading) {
    return (
      <section className="py-20 bg-gradient-to-b from-white to-gray-50">
        <div className="container mx-auto px-6 md:px-12">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Explore by Category</h2>
            <div className="h-1 w-24 bg-orange-400 mx-auto rounded-full"></div>
            <p className="text-gray-600 mt-4 max-w-2xl mx-auto">Discover our wide range of products organized in convenient categories to help you find exactly what you need.</p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 md:gap-8">
            {[...Array(8)].map((_, index) => (
              <CategorySkeleton key={index} />
            ))}
          </div>
        </div>
      </section>
    );
  }

  // Show error state
  if (error) {
    return (
      <section className="py-20 bg-gradient-to-b from-white to-gray-50">
        <div className="container mx-auto px-6 md:px-12">
          <div className="text-center">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Categories</h2>
            <div className="h-1 w-24 bg-orange-400 mx-auto rounded-full mb-8"></div>
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
              <p className="text-red-600 font-medium">Failed to load categories</p>
              <p className="text-red-500 text-sm mt-2">{error}</p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  // Show empty state
  if (shuffledCategories.length === 0) {
    return (
      <section className="py-20 bg-gradient-to-b from-white to-gray-50">
        <div className="container mx-auto px-6 md:px-12">
          <div className="text-center">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Explore by Category</h2>
            <div className="h-1 w-24 bg-orange-400 mx-auto rounded-full mb-8"></div>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 max-w-md mx-auto">
              <p className="text-gray-600 font-medium">No categories available</p>
              <p className="text-gray-500 text-sm mt-2">Categories will appear here once they are added.</p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 bg-gradient-to-b from-white to-gray-50">
      <div className="container mx-auto px-6 md:px-12">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Explore by Category</h2>
          <div className="h-1 w-24 bg-orange-400 mx-auto rounded-full"></div>
          <p className="text-gray-600 mt-4 max-w-2xl mx-auto">
            Discover our wide range of products organized in convenient categories to help you find exactly what you need.
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 md:gap-8">
          {shuffledCategories.map((category) => (
            <Link key={category.id} href={`/products/categories/${category.id}`} className="block group">
              <div className="h-full rounded-2xl overflow-hidden shadow-md hover:shadow-2xl transition-all duration-300 bg-white transform hover:-translate-y-1">
                <CategoryImage category={category} className="h-52 w-full" />

                {/* Category badges */}
                <div className="absolute top-3 left-3 flex flex-wrap gap-2">
                  {category.is_featured && (
                    <span className="px-2 py-1 bg-orange-400 text-white text-xs font-medium rounded-full">
                      FEATURED
                    </span>
                  )}
                  {category.product_count > 50 && (
                    <span className="px-2 py-1 bg-green-500 text-white text-xs font-medium rounded-full">
                      POPULAR
                    </span>
                  )}
                  {category.product_count === 0 && (
                    <span className="px-2 py-1 bg-gray-400 text-white text-xs font-medium rounded-full">
                      NEW
                    </span>
                  )}
                </div>

                <div className="p-5">
                  <div className="flex justify-between items-center">
                    <h3 className="font-bold text-lg text-gray-800 group-hover:text-orange-400 transition-colors duration-300">
                      {category.name}
                    </h3>
                    <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                      {category.product_count || 0} items
                    </span>
                  </div>
                  <div className="mt-3 flex items-center">
                    <span className="text-orange-400 text-sm font-medium group-hover:underline">Browse products</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1 text-orange-500 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        <div className="text-center mt-12">
          <PrimaryGradientBtn
            text="View All Categories"
            navLink="/categories"
          />
        </div>
      </div>
    </section>
  );
}
