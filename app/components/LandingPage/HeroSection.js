'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';

export default function HeroSection() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [currentSlide, setCurrentSlide] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [isImagesLoaded, setIsImagesLoaded] = useState(false);

  // Auto-fill search query from URL params if user comes back
  useEffect(() => {
    const urlSearchQuery = searchParams.get('search');
    if (urlSearchQuery) {
      setSearchQuery(decodeURIComponent(urlSearchQuery));
    }
  }, [searchParams]);

  // Optimized slides with preload hints
  const slides = useMemo(() => [
    {
      image: "/images/hero1.jpg",
      alt: "Wholesale distribution products"
    },
    {
      image: "/images/hero2.jpg",
      alt: "Pallet base products"
    },
    {
      image: "/images/hero3.jpg",
      alt: "Colleagues oversee"
    },
    {
      image: "/images/hero4.jpg",
      alt: "Ecommerce platform"
    },
    {
      image: "/images/hero5.jpg",
      alt: "Modern warehouse"
    }
  ], []);

  // Auto-slide functionality
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [slides.length]);

  // Optimized search handlers with useCallback
  const handleSearch = useCallback((e) => {
    e.preventDefault();
    const trimmedQuery = searchQuery.trim();
    if (!trimmedQuery) return;

    const sanitizedQuery = trimmedQuery.replace(/[<>]/g, '');
    const encodedQuery = encodeURIComponent(sanitizedQuery);
    router.push(`/products?search=${encodedQuery}`);
  }, [searchQuery, router]);

  const handlePopularSearch = useCallback((term) => {
    setSearchQuery(term);
    const encodedTerm = encodeURIComponent(term);
    router.push(`/products?search=${encodedTerm}`);
  }, [router]);

  // Optimized particle positions (reduced from 20 to 12 for better performance)
  const particles = useMemo(() =>
    Array.from({ length: 12 }, (_, i) => ({
      left: `${(i * 8) % 100}%`,
      top: `${(i * 11) % 100}%`,
      delay: `${i * 0.3}s`,
      duration: `${6 + (i % 4)}s`
    })), []
  );

  // Image loading handler
  const handleImageLoad = useCallback(() => {
    setIsImagesLoaded(true);
  }, []);

  return (
    <section className="relative h-[600px] sm:h-[700px] md:h-[750px] lg:h-[800px] xl:h-[850px] overflow-hidden">
      {/* Loading skeleton for first render */}
      {!isImagesLoaded && (
        <div className="absolute inset-0 bg-gradient-to-br from-gray-800 via-gray-700 to-gray-900 animate-pulse z-10">
          <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 via-transparent to-purple-900/10"></div>
        </div>
      )}

      {/* Optimized gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-black/40 via-transparent to-black/60 z-20"></div>

      {/* Optimized hero image slider */}
      <div className="absolute inset-0 w-full h-full">
        <AnimatePresence mode="wait">
          {slides.map((slide, index) => (
            index === currentSlide && (
              <motion.div
                key={slide.image}
                initial={{ opacity: 0, scale: 1.02 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 1.02 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
                className="absolute inset-0"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-orange-500/20 via-transparent to-purple-900/30 z-10"></div>
                <div className="relative w-full h-full">
                  <Image
                    src={slide.image}
                    alt={slide.alt}
                    fill
                    style={{ objectFit: "cover" }}
                    priority={index === 0}
                    quality={85}
                    onLoad={index === 0 ? handleImageLoad : undefined}
                    className="brightness-[0.75] contrast-[1.1] saturate-[1.1]"
                    sizes="100vw"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-60"></div>
                </div>
              </motion.div>
            )
          ))}
        </AnimatePresence>
      </div>

      {/* Optimized floating particles */}
      <div className="absolute inset-0 z-15 pointer-events-none">
        {particles.map((particle, i) => (
          <motion.div
            key={i}
            className={`absolute rounded-full ${i % 3 === 0 ? 'w-3 h-3 bg-orange-400/25' :
              i % 3 === 1 ? 'w-2 h-2 bg-yellow-300/30' :
                'w-1 h-1 bg-white/40'
              }`}
            style={{
              left: particle.left,
              top: particle.top,
            }}
            animate={{
              y: [0, -25, 0],
              opacity: [0.2, 0.6, 0.2],
              scale: [1, 1.1, 1]
            }}
            transition={{
              duration: parseFloat(particle.duration),
              delay: parseFloat(particle.delay),
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>

      {/* Optimized content overlay */}
      <div className="absolute inset-0 flex items-center z-30">
        <div className="container mx-auto px-6 md:px-16 lg:px-20">
          <motion.div
            className="max-w-4xl mx-auto text-white text-center md:text-left md:mx-0"
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            <motion.h1
              className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-black mb-4 sm:mb-6 md:mb-8 leading-[0.9] tracking-tight"
              initial={{ opacity: 0, y: 30, rotateX: 20 }}
              animate={{ opacity: 1, y: 0, rotateX: 0 }}
              transition={{ duration: 1, delay: 0.3, ease: "easeOut" }}
            >
              <motion.span
                className="bg-gradient-to-r from-white via-orange-100 to-yellow-200 bg-clip-text text-transparent inline-block drop-shadow-2xl"
                whileHover={{
                  scale: 1.02,
                  textShadow: "0 0 30px rgba(255,255,255,0.5)"
                }}
                transition={{ type: "spring", stiffness: 200, damping: 10 }}
              >
                Powering Distribution
              </motion.span>
              <br />
              <motion.span
                className="bg-gradient-to-r from-orange-300 via-yellow-300 to-orange-400 bg-clip-text text-transparent inline-block drop-shadow-2xl"
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 1, delay: 0.6 }}
                whileHover={{
                  scale: 1.02,
                  textShadow: "0 0 30px rgba(251,146,60,0.5)"
                }}
              >
                for the Digital Age
              </motion.span>
            </motion.h1>

            <motion.p
              className="text-lg sm:text-xl md:text-2xl lg:text-3xl mb-6 sm:mb-8 md:mb-10 font-light text-white/90 max-w-3xl leading-relaxed"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.8 }}
            >
              <motion.span
                className="inline-block"
                whileHover={{ scale: 1.05, color: '#fbbf24' }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                Explore, compare, and buy
              </motion.span>{" "}
              <motion.span
                className="inline-block text-orange-200"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1.2 }}
              >
                directly from verified distributors.
              </motion.span>{" "}
              <motion.span
                className="inline-block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent font-semibold"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.5, type: "spring", stiffness: 200 }}
              >
                Join the revolution in digital B2B commerce.
              </motion.span>
            </motion.p>

            {/* Enhanced Search Field with premium design */}
            <motion.div
              className="relative mb-8 sm:mb-10 md:mb-12 w-full max-w-3xl mx-auto md:mx-0"
              initial={{ opacity: 0, y: 40, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 1, delay: 1.2, ease: "easeOut" }}
              whileHover={{ y: -4, scale: 1.01 }}
            >
              <form onSubmit={handleSearch} className="relative group">
                {/* Glowing background effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-orange-400/20 via-yellow-400/20 to-orange-400/20 rounded-3xl blur-xl opacity-60 group-hover:opacity-80 transition-opacity duration-500"></div>

                <motion.div
                  className={`relative flex items-center bg-white/98 backdrop-blur-xl rounded-3xl overflow-hidden shadow-2xl transition-all duration-700 ease-out border-2 ${isFocused
                    ? 'border-orange-400/50 ring-8 ring-orange-400/20 scale-[1.02] shadow-orange-500/30'
                    : 'border-white/30 hover:border-orange-300/50 hover:shadow-2xl hover:shadow-orange-500/10'
                    }`}
                  whileHover={{ scale: 1.01, y: -2 }}
                  whileTap={{ scale: 0.99 }}
                  style={{
                    boxShadow: isFocused
                      ? '0 25px 50px -12px rgba(251, 146, 60, 0.25), 0 0 0 1px rgba(251, 146, 60, 0.1)'
                      : '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
                  }}
                >
                  <div className="flex items-center w-full">
                    <div className="flex-shrink-0 pl-4 sm:pl-6 md:pl-8">
                      <motion.div
                        animate={{
                          scale: isFocused ? 1.1 : 1,
                          rotate: isFocused ? 360 : 0
                        }}
                        transition={{ duration: 0.3 }}
                      >
                        <svg className="w-5 h-5 sm:w-6 sm:h-6 md:w-6 md:h-6 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                      </motion.div>
                    </div>

                    <input
                      type="text"
                      placeholder="Search for products, categories, brands..."
                      className="flex-1 py-3 sm:py-4 md:py-5 px-3 sm:px-4 md:px-6 text-gray-800 text-base sm:text-lg md:text-xl font-medium outline-none bg-transparent touch-manipulation placeholder:text-gray-400 placeholder:font-normal"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onFocus={() => setIsFocused(true)}
                      onBlur={() => setIsFocused(false)}
                      autoComplete="off"
                      maxLength={100}
                      aria-label="Search for products, categories, brands"
                    />
                  </div>

                  <motion.button
                    type="submit"
                    onClick={handleSearch}
                    className="bg-gradient-to-r from-orange-400 via-orange-500 to-yellow-400 hover:from-yellow-400 hover:via-orange-400 hover:to-orange-500 text-white p-3 sm:p-4 md:p-4 m-1.5 sm:m-2 md:m-3 rounded-xl sm:rounded-2xl transition-all duration-500 flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 relative overflow-hidden group cursor-pointer shadow-lg hover:shadow-xl"
                    whileHover={{
                      scale: 1.05,
                      rotate: [0, -5, 5, 0],
                      boxShadow: "0 15px 30px -8px rgba(251, 146, 60, 0.4)"
                    }}
                    whileTap={{ scale: 0.95 }}
                    transition={{ duration: 0.3 }}
                    aria-label="Search"
                  >
                    {/* Simplified search icon */}
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      className="w-5 h-5 sm:w-6 sm:h-6 md:w-6 md:h-6"
                      strokeWidth={2.5}
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </motion.button>
                </motion.div>
                <motion.div
                  className="absolute -bottom-14 sm:-bottom-14 md:-bottom-18 w-full text-center"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.8, duration: 0.8 }}
                >
                  <motion.span
                    className="text-xs sm:text-sm md:text-base text-white/70 font-medium"
                    whileHover={{ color: '#fbbf24' }}
                  >
                    Popular searches:
                  </motion.span>
                  <div className="flex flex-wrap justify-center gap-1.5 sm:gap-2 md:gap-3 mt-1.5 sm:mt-2">
                    {['Drinks', 'Dishwashing Tablets', 'Liquid Detergent'].map((term, index) => (
                      <motion.button
                        key={term}
                        type="button"
                        onClick={() => handlePopularSearch(term)}
                        className="px-2 py-1 sm:px-3 sm:py-1 md:px-4 md:py-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-xs sm:text-xs md:text-sm text-white/90 hover:bg-orange-400/20 hover:border-orange-400/40 hover:text-white transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-orange-400/50"
                        whileHover={{
                          scale: 1.05,
                          y: -1,
                          backgroundColor: 'rgba(251, 146, 60, 0.2)'
                        }}
                        whileTap={{ scale: 0.95 }}
                        initial={{ opacity: 0, y: 10, scale: 0.9 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        transition={{
                          delay: 2 + index * 0.1,
                          type: "spring",
                          stiffness: 200
                        }}
                        aria-label={`Search for ${term}`}
                      >
                        {term}
                      </motion.button>
                    ))}
                  </div>
                </motion.div>
              </form>
            </motion.div>

            {/* Enhanced CTA button with premium design */}
            <motion.div
              className="flex flex-col sm:flex-row gap-3 sm:gap-4 md:gap-6 justify-center md:justify-start mb-12 sm:mb-16 md:mb-0"
              initial={{ opacity: 0, y: 40, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 1, delay: 2.2, ease: "easeOut" }}
            >
              <div className="mx-auto md:ml-0 mt-10">
                <Link href="/cart">
                  <motion.button
                    className="group relative px-6 sm:px-8 md:px-10 lg:px-12 py-3 sm:py-4 md:py-4 text-white rounded-xl sm:rounded-2xl font-bold text-base sm:text-lg md:text-xl shadow-2xl bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 overflow-hidden"
                    whileHover={{ scale: 1.05, y: -3 }}
                    whileTap={{ scale: 0.95 }}
                    transition={{ duration: 0.2 }}
                  >
                    {/* Simplified shimmer effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 translate-x-[-100%] group-hover:translate-x-[200%] transition-transform duration-1000 ease-out"></div>

                    {/* Button content */}
                    <span className="relative flex items-center gap-2 sm:gap-3">
                      <span>Your Pallet</span>
                      <svg
                        className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 group-hover:translate-x-1 transition-transform"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </span>
                  </motion.button>
                </Link>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Enhanced slider navigation with premium design */}
      <motion.div
        className="absolute bottom-6 md:bottom-10 left-0 right-0 z-40"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2.5, duration: 0.8 }}
      >
        <div className="flex justify-center items-center gap-3 md:gap-4">
          {slides.map((_, index) => (
            <motion.button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`relative rounded-full transition-all duration-500 backdrop-blur-sm border ${index === currentSlide
                ? 'bg-orange-400/80 border-orange-300/50 shadow-lg shadow-orange-400/30'
                : 'bg-white/20 border-white/30 hover:bg-white/40 hover:border-white/50'
                }`}
              whileHover={{
                scale: 1.3,
                backgroundColor: index === currentSlide ? 'rgba(251, 146, 60, 0.9)' : 'rgba(255, 255, 255, 0.6)'
              }}
              whileTap={{ scale: 0.9 }}
              initial={{
                width: index === currentSlide ? 32 : 12,
                height: 12,
                opacity: 0,
                y: 20
              }}
              animate={{
                width: index === currentSlide ? 32 : 12,
                height: 12,
                opacity: 1,
                y: 0
              }}
              transition={{
                duration: 0.4,
                delay: 2.5 + index * 0.1,
                type: "spring",
                stiffness: 200
              }}
              aria-label={`Go to slide ${index + 1}`}
            >
              {/* Active indicator dot */}
              {index === currentSlide && (
                <motion.div
                  className="absolute inset-1 bg-white rounded-full"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.3 }}
                />
              )}
            </motion.button>
          ))}
        </div>

        {/* Slide counter */}
        <motion.div
          className="text-center mt-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 3 }}
        >
          <span className="text-white/60 text-sm font-medium">
            {currentSlide + 1} / {slides.length}
          </span>
        </motion.div>
      </motion.div>
    </section>
  );
}
