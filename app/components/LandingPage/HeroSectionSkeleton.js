export default function HeroSectionSkeleton() {
  return (
    <section className="relative min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-100 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-32 h-32 bg-orange-300 rounded-full animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-orange-200 rounded-full animate-pulse"></div>
        <div className="absolute bottom-32 left-1/4 w-16 h-16 bg-orange-400 rounded-full animate-pulse"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
          
          {/* Left Content - Text Skeleton */}
          <div className="text-center lg:text-left space-y-8">
            {/* Title Skeleton */}
            <div className="space-y-4">
              <div className="h-8 bg-gray-200 rounded-lg animate-pulse w-3/4"></div>
              <div className="h-8 bg-gray-200 rounded-lg animate-pulse w-full"></div>
              <div className="h-8 bg-gray-200 rounded-lg animate-pulse w-2/3"></div>
            </div>

            {/* Subtitle Skeleton */}
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded animate-pulse w-full"></div>
              <div className="h-4 bg-gray-200 rounded animate-pulse w-5/6"></div>
              <div className="h-4 bg-gray-200 rounded animate-pulse w-4/5"></div>
            </div>

            {/* Search Bar Skeleton */}
            <div className="max-w-2xl mx-auto lg:mx-0">
              <div className="relative">
                <div className="h-14 bg-gray-200 rounded-xl animate-pulse"></div>
                <div className="absolute right-2 top-2 h-10 w-24 bg-gray-300 rounded-lg animate-pulse"></div>
              </div>
            </div>

            {/* Popular Searches Skeleton */}
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded animate-pulse w-32"></div>
              <div className="flex flex-wrap gap-2 justify-center lg:justify-start">
                {[1, 2, 3, 4, 5].map((item) => (
                  <div key={item} className="h-8 bg-gray-200 rounded-full animate-pulse w-20"></div>
                ))}
              </div>
            </div>

            {/* Stats Skeleton */}
            <div className="grid grid-cols-3 gap-6 pt-8">
              {[1, 2, 3].map((item) => (
                <div key={item} className="text-center space-y-2">
                  <div className="h-8 bg-gray-200 rounded animate-pulse w-16 mx-auto"></div>
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-20 mx-auto"></div>
                </div>
              ))}
            </div>
          </div>

          {/* Right Content - Image Carousel Skeleton */}
          <div className="relative">
            <div className="relative h-96 lg:h-[500px] rounded-2xl overflow-hidden bg-gray-200 animate-pulse">
              {/* Main Image Skeleton */}
              <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300"></div>
              
              {/* Overlay Content Skeleton */}
              <div className="absolute inset-0 bg-black bg-opacity-20 flex items-end">
                <div className="p-6 w-full space-y-3">
                  <div className="h-6 bg-white bg-opacity-80 rounded animate-pulse w-3/4"></div>
                  <div className="h-4 bg-white bg-opacity-60 rounded animate-pulse w-1/2"></div>
                </div>
              </div>

              {/* Navigation Dots Skeleton */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                {[1, 2, 3, 4].map((dot) => (
                  <div key={dot} className="w-3 h-3 bg-white bg-opacity-50 rounded-full animate-pulse"></div>
                ))}
              </div>

              {/* Navigation Arrows Skeleton */}
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                <div className="w-10 h-10 bg-white bg-opacity-30 rounded-full animate-pulse"></div>
              </div>
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                <div className="w-10 h-10 bg-white bg-opacity-30 rounded-full animate-pulse"></div>
              </div>
            </div>

            {/* Floating Elements Skeleton */}
            <div className="absolute -top-4 -right-4 w-20 h-20 bg-orange-200 rounded-full animate-pulse opacity-60"></div>
            <div className="absolute -bottom-6 -left-6 w-16 h-16 bg-orange-300 rounded-full animate-pulse opacity-40"></div>
          </div>
        </div>

        {/* Bottom Features Skeleton */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
          {[1, 2, 3].map((feature) => (
            <div key={feature} className="text-center space-y-4">
              <div className="w-16 h-16 bg-orange-200 rounded-full mx-auto animate-pulse"></div>
              <div className="space-y-2">
                <div className="h-5 bg-gray-200 rounded animate-pulse w-32 mx-auto"></div>
                <div className="h-4 bg-gray-200 rounded animate-pulse w-48 mx-auto"></div>
                <div className="h-4 bg-gray-200 rounded animate-pulse w-40 mx-auto"></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Loading Indicator */}
      <div className="absolute top-4 right-4 flex items-center space-x-2 text-gray-500">
        <div className="w-4 h-4 border-2 border-orange-300 border-t-orange-500 rounded-full animate-spin"></div>
        <span className="text-sm font-medium">Loading...</span>
      </div>
    </section>
  );
}
