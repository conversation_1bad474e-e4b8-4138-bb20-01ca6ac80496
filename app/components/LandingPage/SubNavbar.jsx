'use client';

import Link from 'next/link'
import React, { useEffect, useState } from 'react'

// API function to fetch categories tree from backend
export async function getCategoriesTree() {
    try {
        console.log('🔍 Fetching categories tree from API...');
        const response = await fetch('https://b2b.instinctfusionx.xyz/public/api/v1/categories/tree', {
            cache: 'force-cache',
            next: { revalidate: 3600 }, // Revalidate every hour
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('✅ Categories tree fetched successfully:', data);
        return data;
    } catch (error) {
        console.error('❌ Error fetching categories:', error);
        // Return fallback structure on error
        return {
            success: false,
            message: 'Failed to fetch categories',
            data: { categories: [] }
        };
    }
}

// Helper function to get fallback image for categories
const getCategoryFallbackImage = (slug) => {
    const fallbackImages = {
        'drinks': '/images/productListImage/drinks.png',
        'electronics-gadgets': '/images/productListImage/electronics.png',
        'general-products': '/images/productListImage/general.png',
        'personal-care': '/images/productListImage/personal-care.png',
        'household': '/images/productListImage/household.png',
        'furniture': '/images/productListImage/furniture.png',
        'baby-wipes': '/images/productListImage/baby-care.png',
        'washing-powder': '/images/productListImage/laundry.png',
        'liquid-detergent': '/images/productListImage/cleaning.png',
        'laundry-prods': '/images/productListImage/laundry.png',
        'dishwashing-tablets': '/images/productListImage/kitchen.png',
        'toilet-bowl-cleaner': '/images/productListImage/bathroom.png'
    };
    return fallbackImages[slug] || '/images/productListImage/default-category.png';
};

// Loading skeleton component
const SubNavbarSkeleton = () => (
    <section className="hidden md:block bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-evenly h-16 sm:h-20 xl:h-16">
                <ul className="hidden md:flex space-x-4 flex-wrap justify-center gap-1">
                    {[1, 2, 3, 4, 5, 6, 7, 8].map((item) => (
                        <li key={item} className="relative group inline-flex">
                            <div className="inline-flex items-center px-1 text-sm font-medium my-1">
                                <div className="h-4 bg-gray-200 rounded animate-pulse w-20"></div>
                                <span className="ml-1">▾</span>
                            </div>
                        </li>
                    ))}
                </ul>
            </div>
        </div>
    </section>
);

export default function SubNavbar() {
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [scrolled, setScrolled] = useState(false);

    // Fetch categories on component mount
    useEffect(() => {
        async function fetchCategories() {
            try {
                setLoading(true);
                const response = await getCategoriesTree();

                if (response.success && response.data?.categories) {
                    // Filter only featured and active categories for the navbar
                    const featuredCategories = response.data.categories.filter(category =>
                        category.is_featured &&
                        category.is_active &&
                        !['test1', 'test2', 'test3', 'test4', 'test5'].includes(category.slug) // Filter out test categories
                    );

                    // Sort by sort_order if available, then by name
                    const sortedCategories = featuredCategories.sort((a, b) => {
                        if (a.sort_order && b.sort_order) {
                            return parseInt(a.sort_order) - parseInt(b.sort_order);
                        }
                        return a.name.localeCompare(b.name);
                    });

                    setCategories(sortedCategories);
                } else {
                    setError('Failed to load categories');
                }
            } catch (err) {
                console.error('Error fetching categories:', err);
                setError('Failed to load categories');
            } finally {
                setLoading(false);
            }
        }

        fetchCategories();
    }, []);

    // Handle scroll effect
    useEffect(() => {
        if (typeof window === 'undefined') return;

        const handleScroll = () => {
            const isScrolled = window.scrollY > 10;
            if (isScrolled !== scrolled) {
                setScrolled(isScrolled);
            }
        };

        window.addEventListener('scroll', handleScroll);
        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, [scrolled]);

    // Show loading skeleton while fetching data
    if (loading) {
        return <SubNavbarSkeleton />;
    }

    // Show error state (but don't break the layout)
    if (error) {
        console.error('SubNavbar error:', error);
        return null; // Hide navbar on error
    }

    // Don't render if no categories
    if (!categories || categories.length === 0) {
        return null;
    }

    return (
        <section className={`hidden md:block transition-all duration-300 ${scrolled
            ? 'bg-white shadow-lg'
            : 'bg-white'
            }`}>
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-5">
                <div className="flex justify-evenly h-16 sm:h-20 xl:h-16">
                    {/* Desktop Navigation */}
                    <ul className="hidden md:flex space-x-4 flex-wrap justify-center gap-1">
                        {categories.map((category, index) => (
                            <li key={category.id} className="relative group inline-flex">
                                <Link
                                    href={`/products/categories/${category.id}`}
                                    className="inline-flex items-center px-1 text-sm font-medium text-gray-800 hover:text-orange-500 transition-colors border-b-2 border-transparent hover:border-orange-500 my-1 text-nowrap"
                                >
                                    {category.name}
                                    {category.product_count > 0 && (
                                        <span className="ml-1 text-xs text-gray-500">
                                            ({category.product_count})
                                        </span>
                                    )}
                                    {category.children && category.children.length > 0 && (
                                        <span className="ml-1 transition-transform group-hover:rotate-180 duration-300">▾</span>
                                    )}
                                </Link>

                                {/* Dropdown for categories with children */}
                                {category.children && category.children.length > 0 && (
                                    <div className={`absolute w-64 top-full origin-top-right bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition duration-300 border border-gray-200 z-50 ${index >= categories.length - 2 ? "right-0" : "left-0"
                                        }`}>
                                        {/* Category header with image and description */}
                                        <div className="p-3 border-b border-gray-100 bg-orange-50">
                                            <div className="flex items-center space-x-3">
                                                {category.image && (
                                                    <img
                                                        src={category.image}
                                                        alt={category.name}
                                                        className="w-8 h-8 rounded-full object-cover"
                                                        onError={(e) => {
                                                            e.target.src = getCategoryFallbackImage(category.slug);
                                                        }}
                                                    />
                                                )}
                                                <div>
                                                    <h4 className="font-medium text-gray-900">{category.name}</h4>
                                                    {category.description && (
                                                        <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                                                            {category.description}
                                                        </p>
                                                    )}
                                                </div>
                                            </div>
                                        </div>

                                        {/* Subcategories */}
                                        <ul className="py-1 max-h-64 overflow-y-auto">
                                            {category.children.map((child) => (
                                                <li key={child.id}>
                                                    <Link
                                                        href={`/categories/${child.id}`}
                                                        className="flex items-center justify-between px-4 py-2 text-sm text-gray-700 hover:bg-orange-100 hover:text-orange-500 transition-colors"
                                                    >
                                                        <span>{child.name}</span>
                                                        {child.product_count > 0 && (
                                                            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                                                                {child.product_count}
                                                            </span>
                                                        )}
                                                    </Link>
                                                </li>
                                            ))}
                                        </ul>

                                        {/* View all link */}
                                        <div className="border-t border-gray-100 p-2">
                                            <Link
                                                href={`/categories/${category.id}`}
                                                className="block w-full text-center px-3 py-2 text-sm font-medium text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded transition-colors"
                                            >
                                                View All {category.name}
                                            </Link>
                                        </div>
                                    </div>
                                )}
                            </li>
                        ))}
                    </ul>
                </div>
            </div>
        </section>
    );
}