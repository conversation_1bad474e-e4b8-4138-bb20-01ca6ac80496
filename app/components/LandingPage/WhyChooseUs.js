'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';

export default function WhyChooseUs() {
  const [isVisible, setIsVisible] = useState(false);
  const [focusedCard, setFocusedCard] = useState(null);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const features = [
    {
      id: 1,
      title: "Verified Distributors Only",
      description: "We thoroughly vet all distributors to ensure quality and reliability through comprehensive background checks and performance monitoring.",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-8 w-8 sm:h-10 sm:w-10 lg:h-12 lg:w-12 text-orange-500"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          aria-hidden="true"
          role="img"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
        </svg>
      ),
      color: "from-orange-400 to-red-500",
      bgColor: "bg-orange-50",
      borderColor: "border-orange-200",
      hoverColor: "hover:border-orange-400"
    },
    {
      id: 2,
      title: "Powerful Product Discovery Tools",
      description: "Find exactly what you need with advanced search, AI-powered filtering, and intelligent product recommendations.",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-8 w-8 sm:h-10 sm:w-10 lg:h-12 lg:w-12 text-orange-500"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          aria-hidden="true"
          role="img"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      ),
      color: "from-orange-400 to-amber-500",
      bgColor: "bg-orange-50",
      borderColor: "border-orange-200",
      hoverColor: "hover:border-orange-400"
    },
    {
      id: 3,
      title: "Secure Payment System",
      description: "Rest easy with our bank-grade security, encrypted transactions, and transparent payment processing with fraud protection.",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-8 w-8 sm:h-10 sm:w-10 lg:h-12 lg:w-12 text-orange-500"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          aria-hidden="true"
          role="img"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
        </svg>
      ),
      color: "from-orange-400 to-yellow-500",
      bgColor: "bg-orange-50",
      borderColor: "border-orange-200",
      hoverColor: "hover:border-orange-400"
    },
    {
      id: 4,
      title: "AI-Powered Insights & Suggestions",
      description: "Get smart recommendations based on your purchasing history, market trends, and predictive analytics for better business decisions.",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-8 w-8 sm:h-10 sm:w-10 lg:h-12 lg:w-12 text-orange-500"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          aria-hidden="true"
          role="img"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      ),
      color: "from-orange-400 to-red-500",
      bgColor: "bg-orange-50",
      borderColor: "border-orange-200",
      hoverColor: "hover:border-orange-400"
    },
    {
      id: 5,
      title: "Mobile App for Anytime Access",
      description: "Manage your buying and selling on the go with our native mobile application featuring offline capabilities and real-time notifications.",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-8 w-8 sm:h-10 sm:w-10 lg:h-12 lg:w-12 text-orange-500"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          aria-hidden="true"
          role="img"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
      ),
      color: "from-orange-400 to-amber-500",
      bgColor: "bg-orange-50",
      borderColor: "border-orange-200",
      hoverColor: "hover:border-orange-400"
    },
    {
      id: 6,
      title: "Pallet-Based Shipping Optimization",
      description: "Save on shipping costs with our intelligent pallet optimization system, route planning, and bulk shipping discounts.",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-8 w-8 sm:h-10 sm:w-10 lg:h-12 lg:w-12 text-orange-500"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          aria-hidden="true"
          role="img"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
        </svg>
      ),
      color: "from-orange-400 to-yellow-500",
      bgColor: "bg-orange-50",
      borderColor: "border-orange-200",
      hoverColor: "hover:border-orange-400"
    }
  ];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 30,
      scale: 0.95
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const handleCardFocus = (id) => {
    setFocusedCard(id);
  };

  const handleCardBlur = () => {
    setFocusedCard(null);
  };

  return (
    <section
      className="py-12 sm:py-16 lg:py-20 bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50 relative overflow-hidden"
      aria-labelledby="why-choose-us-heading"
      role="region"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5" aria-hidden="true">
        <div className="absolute top-0 left-0 w-full h-full" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f97316' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header Section */}
        <motion.div
          className="text-center mb-12 sm:mb-16 lg:mb-20"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : -20 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <div className="inline-flex items-center justify-center p-2 bg-orange-100 rounded-full mb-6">
            <span className="bg-orange-400 text-white text-sm font-semibold px-4 py-2 rounded-full">
              Why Choose Us
            </span>
          </div>

          <h2
            id="why-choose-us-heading"
            className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight"
          >
            Why Choose{' '}
            <span className="bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent">
              Diptouch?
            </span>
          </h2>

          <p className="text-lg sm:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            We&apos;ve built a platform that addresses the unique challenges of B2B distribution
            in the digital age, delivering unmatched value and efficiency.
          </p>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-10"
          variants={containerVariants}
          initial="hidden"
          animate={isVisible ? "visible" : "hidden"}
        >
          {features.map((feature) => (
            <motion.article
              key={feature.id}
              className={`group relative ${feature.bgColor} rounded-2xl sm:rounded-3xl p-6 sm:p-8 lg:p-10 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border-2 ${feature.borderColor} ${feature.hoverColor} focus-within:ring-4 focus-within:ring-orange-200 focus-within:ring-opacity-50 ${focusedCard === feature.id ? 'ring-4 ring-orange-300 ring-opacity-75' : ''
                }`}
              variants={itemVariants}
              whileHover={{
                scale: 1.02,
                transition: { duration: 0.2 }
              }}
              onFocus={() => handleCardFocus(feature.id)}
              onBlur={handleCardBlur}
              onMouseEnter={() => handleCardFocus(feature.id)}
              onMouseLeave={handleCardBlur}
              tabIndex={0}
              role="article"
              aria-labelledby={`feature-title-${feature.id}`}
              aria-describedby={`feature-desc-${feature.id}`}
            >
              {/* Gradient Overlay */}
              <div className={`absolute inset-0 bg-gradient-to-r ${feature.color} opacity-0 group-hover:opacity-10 rounded-2xl sm:rounded-3xl transition-opacity duration-500`} aria-hidden="true"></div>

              <div className="relative z-10">
                {/* Icon Container */}
                <div className="flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 mb-6 sm:mb-8 rounded-2xl bg-white shadow-lg group-hover:shadow-xl transition-all duration-500 group-hover:scale-110">
                  <div className={`p-3 sm:p-4 rounded-xl bg-gradient-to-r ${feature.color}`}>
                    {feature.icon}
                  </div>
                </div>

                {/* Content */}
                <h3
                  id={`feature-title-${feature.id}`}
                  className="text-xl sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-6 group-hover:text-gray-800 transition-colors duration-300 leading-tight"
                >
                  {feature.title}
                </h3>

                <p
                  id={`feature-desc-${feature.id}`}
                  className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300 text-base sm:text-lg"
                >
                  {feature.description}
                </p>
              </div>

              {/* Bottom Accent Line */}
              <div className={`absolute bottom-0 left-0 right-0 h-1 sm:h-2 bg-gradient-to-r ${feature.color} transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 rounded-b-2xl sm:rounded-b-3xl`} aria-hidden="true"></div>

              {/* Corner Accent */}
              <div className={`absolute top-4 right-4 w-3 h-3 bg-gradient-to-r ${feature.color} rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500`} aria-hidden="true"></div>
            </motion.article>
          ))}
        </motion.div>

        {/* Call to Action */}
        <motion.div
          className="mt-16 sm:mt-20 lg:mt-24 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
          transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
        >
          <div className="inline-flex items-center justify-center p-1 bg-gradient-to-r from-orange-400 to-red-500 rounded-full shadow-2xl">
            <Link
              href="/signup/user"
              className="bg-white text-gray-900 font-semibold py-3 sm:py-4 px-8 sm:px-12 rounded-full hover:bg-gray-50 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-orange-200 focus:ring-opacity-50 group"
              aria-label="Get started with our platform today"
            >
              <span className="flex items-center gap-2">
                Get Started Today
                <svg
                  className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </span>
            </Link>
          </div>

          <p className="mt-4 text-sm text-gray-500">
            Join thousands of businesses already using our platform
          </p>
        </motion.div>
      </div>
    </section>
  );
}
