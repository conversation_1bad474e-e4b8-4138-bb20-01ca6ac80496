"use client";

import React, { useState, useEffect } from 'react'
// import PrimaryCartBtn from '../Buttons/PrimaryCartBtn'
import Link from 'next/link';
import Image from 'next/image';
import calculations from '@/app/utils/calculations';
import { FaSpinner } from 'react-icons/fa6';
import { motion } from 'framer-motion';
import { BsBoxes } from 'react-icons/bs';
import { FaCartPlus, FaPallet } from 'react-icons/fa';
import { FaEye, FaMinus, FaPlus } from 'react-icons/fa6';
import toast from 'react-hot-toast';
import { useCart } from '../../context/CartContext';
import AuthRequiredPopup from '../UI/AuthRequiredPopup';

// Helper function to get the correct product image URL from inconsistent API formats
const getProductImageUrl = (product) => {
    // Safety check for product object
    if (!product) return null;

    // Format 1: Images array (multiple images)
    if (product.images && Array.isArray(product.images) && product.images.length > 0) {
        // Filter out invalid images and sort by sort_order (lowest first)
        const validImages = product.images.filter(img =>
            img && (img.image_url || img.full_image_url)
        );

        if (validImages.length === 0) return null;

        const sortedImages = validImages.sort((a, b) => {
            const orderA = parseInt(a.sort_order) || 0;
            const orderB = parseInt(b.sort_order) || 0;
            return orderA - orderB;
        });

        const firstImage = sortedImages[0];
        // Prefer image_url over full_image_url (full_image_url seems to have double URL issue)
        const imageUrl = firstImage.image_url || firstImage.full_image_url;

        // Validate URL format
        if (imageUrl && typeof imageUrl === 'string' && imageUrl.trim() !== '') {
            return imageUrl.trim();
        }
    }

    // Format 2: Single image string
    if (product.image && typeof product.image === 'string' && product.image.trim() !== '') {
        return product.image.trim();
    }

    // No valid image found
    return null;
};

// Helper function to get alt text for product image
const getProductImageAltText = (product) => {
    // Safety check for product object
    if (!product) return 'Product Image';

    // Try to get alt_text from images array
    if (product.images && Array.isArray(product.images) && product.images.length > 0) {
        // Filter valid images and sort by sort_order
        const validImages = product.images.filter(img =>
            img && (img.image_url || img.full_image_url)
        );

        if (validImages.length > 0) {
            const sortedImages = validImages.sort((a, b) => {
                const orderA = parseInt(a.sort_order) || 0;
                const orderB = parseInt(b.sort_order) || 0;
                return orderA - orderB;
            });

            const firstImage = sortedImages[0];
            if (firstImage.alt_text && typeof firstImage.alt_text === 'string' && firstImage.alt_text.trim() !== '') {
                return firstImage.alt_text.trim();
            }
        }
    }

    // Fallback to product name, then generic text
    if (product.name && typeof product.name === 'string' && product.name.trim() !== '') {
        return product.name.trim();
    }

    return 'Product Image';
};

// Product Image Component with robust fallback handling
const ProductImage = ({ product, className = "", size = "medium" }) => {
    const [imageError, setImageError] = useState(false);
    const [imageLoading, setImageLoading] = useState(true);

    // Get the appropriate image URL using our helper function
    const imageUrl = getProductImageUrl(product);
    const altText = getProductImageAltText(product);
    const hasValidImage = imageUrl && !imageError;

    const iconSize = size === "large" ? "text-3xl" : "text-2xl";
    const textSize = size === "large" ? "text-sm" : "text-xs";

    // Debug logging for image handling
    React.useEffect(() => {
        if (product.id) {
            // console.log(`🖼️ Product ${product.id} image handling:`, {
            //     hasImages: !!product.images,
            //     imagesCount: product.images?.length || 0,
            //     hasSingleImage: !!product.image,
            //     selectedImageUrl: imageUrl,
            //     altText: altText
            // });
        }
    }, [product.id, imageUrl, altText]);

    return (
        <div className={`relative bg-gray-100 ${className}`}>
            {hasValidImage ? (
                <>
                    <Image
                        src={imageUrl}
                        alt={altText}
                        fill
                        style={{ objectFit: 'cover' }}
                        className="transition-transform duration-300 hover:scale-105"
                        onLoad={() => setImageLoading(false)}
                        onError={() => {
                            // console.log(`❌ Image failed to load for product ${product.id}:`, imageUrl);
                            setImageError(true);
                            setImageLoading(false);
                        }}
                    />
                    {imageLoading && (
                        <div className="absolute inset-0 bg-gray-200 flex items-center justify-center">
                            <FaSpinner className="animate-spin text-gray-400 text-xl" />
                        </div>
                    )}
                </>
            ) : (
                // Gray div placeholder following established pattern
                <div className="absolute inset-0 bg-gray-200 flex items-center justify-center">
                    <div className="text-center text-gray-500">
                        <div className={`w-12 h-12 mx-auto mb-2 bg-gray-300 rounded-full flex items-center justify-center ${iconSize === "text-3xl" ? "w-16 h-16" : "w-12 h-12"}`}>
                            <span className={`font-medium ${iconSize === "text-3xl" ? "text-xl" : "text-lg"}`}>
                                {product.name ? product.name.charAt(0).toUpperCase() : 'P'}
                            </span>
                        </div>
                        <span className={`${textSize} font-medium`}>
                            {imageUrl ? 'Image Failed' : 'No Image'}
                        </span>
                    </div>
                </div>
            )}

            {(product.special_price || product.pack_price?.per_pack_special_price) && (
                <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium z-10">
                    Sale
                </div>
            )}
        </div>
    );
};


export default function publicProductCard({ product, index, viewMode = 'grid', tailwindCss = '', cartPosition = 'left', text = 'Add to Cart' }) {
    const [quantity, setQuantity] = useState(0);
    const [showAuthPopup, setShowAuthPopup] = useState(false);
    const { incrementCartCount } = useCart();

    // console.log(product);
    // useEffect(() => {
    //     (async function () {
    //         const cal = await calculations.singleProduct(product, 200, 'customer');
    //         console.log(cal, "-------------------");
    //     })();
    // }, [product]);

    function handleQuantityChange(e) {
        // console.log(e);

        // For manual input, we need e.target.value, not e
        const value = e.target?.value || e;

        // Handle increment/decrement buttons
        if (typeof e === 'number' && quantity > -1) {
            setQuantity(Math.max(0, quantity + e));
            return;
        }

        // Handle backspace and empty input
        if (value === '') {
            setQuantity('');
            return;
        }

        // Only allow integer numbers
        if (!/^\d+$/.test(value)) {
            return;
        }

        // Convert to integer and validate
        const newValue = parseInt(value);
        if (isNaN(newValue)) {
            return;
        }

        // Ensure minimum value is 0
        setQuantity(Math.max(0, newValue));
    };

    // Add to Cart Button
    async function handleAddToCart() {
        try {
            // Check if quantity is valid
            if (!quantity || quantity <= 0) {
                toast.error('Please select a valid quantity');
                return;
            }

            // Check authentication first
            const token = localStorage.getItem('userAuthToken') ||
                localStorage.getItem('partnerAuthToken') ||
                localStorage.getItem('superAdminAuthToken');

            if (!token) {
                setShowAuthPopup(true);
                return;
            }

            // Then check for cart
            const cartId = localStorage.getItem('cartId');
            if (!cartId) {
                toast.error('No cart found. Please try again later.');
                return;
            }

            const response = await fetch(`https://b2b.instinctfusionx.xyz/public/api/v1/carts/${cartId}/items`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    product_id: product.id,
                    quantity: quantity,
                    is_bulk_pricing: false,
                    notes: ''
                })
            });

            const data = await response.json();
            // console.log(data);

            if (data.success === true && data.data.cart.id) {
                toast.success(data.message || 'Product added to cart successfully!');
                // Update cart count in navbar
                incrementCartCount(quantity);
            } else {
                toast.error(data.message || 'Failed to add product to cart',
                    {
                        duration: 5000, // Duration in milliseconds (e.g., 5 seconds)
                    }
                );
            }
        } catch (error) {
            console.error('Error adding to cart:', error);
            toast.error('Failed to add product to cart', {
                duration: 5000, // Duration in milliseconds (e.g., 5 seconds)
            });
        }
    }

    if (viewMode === 'grid') {
        return (
            <>
                <AuthRequiredPopup
                    isOpen={showAuthPopup}
                    onClose={() => setShowAuthPopup(false)}
                />
                <motion.li
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden min-w-[300px] h-full">
                <div className="flex flex-col h-full">
                    <Link href={`/products/${product.id}`} className="block hover:opacity-90 transition-opacity">
                        <ProductImage
                            product={product}
                            className="w-full h-64"
                            size="medium"
                        />
                    </Link>

                    {/* Product Info - Flexible content area */}
                    <div className="flex-1 flex flex-col">
                        <Link href={`/products/${product.id}`} className="block">
                            <div className="flex flex-col space-y-3 px-4 py-2 w-full">
                                <h2 className="text-2xl font-semibold text-orange-500 hover:text-orange-600 transition-colors">
                                    {product.name}
                                </h2>
                                <p className="text-sm text-gray-600 line-clamp-2 min-h-[40px]">
                                    {product?.description || "Description: NA"}
                                    {product?.description?.length > 50 && (
                                        <span className="text-orange-400 hover:text-orange-500 ml-1 inline-block">
                                            read more...
                                        </span>
                                    )}
                                </p>
                                <div className="text-sm text-gray-600 space-y-2">
                                    {/* <p>Category: {product.category?.name}</p> */}
                                    <p className="flex items-center gap-2">
                                        <span className="font-medium min-w-[80px]">Brand:</span>
                                        <span className="text-gray-700">{product.brand}</span>
                                    </p>
                                    {/* <p>Weight: {product.weight} {product.weight_unit}</p> */}
                                    <p className="flex items-center gap-2">
                                        <span className="font-medium min-w-[80px]">SKU:</span>
                                        <span className="text-gray-700">{product.sku}</span>
                                    </p>
                                    <p className="flex items-center gap-2">
                                        <span className="font-medium min-w-[80px]">Items/Pack:</span>
                                        <span className="text-gray-700">{product.pack_price.number_of_products || "NA"} units</span>
                                    </p>
                                    <p className="flex items-center gap-2">
                                        <span className="font-medium min-w-[80px]">Stock:</span>
                                        <span className={`${product.quentity > 0 ? 'text-green-600 font-medium' : 'text-red-600 font-medium'}`}>
                                            {product.quantity > 0 ? `${product.stock_status}` : "Out of Stock"}
                                        </span>
                                    </p>
                                </div>
                                <div className="flex items-center gap-4 mt-2 bg-orange-50 p-3 rounded-lg">
                                    <span className="text-2xl font-bold text-orange-500">
                                        ${
                                            (
                                                (
                                                    parseFloat(product.pack_price.per_pack_special_price || product.pack_price.per_pack_price)
                                                ) +
                                                parseFloat((product.pack_price.per_pack_special_price || product.pack_price.per_pack_price) * parseFloat(product.pack_price.customer_margin) / 100)
                                            ).toFixed(2)
                                        }<span className="text-xs font-medium"> per pack </span>
                                    </span>

                                    {product.pack_price.per_pack_special_price > 0 && (
                                        <>
                                            <span className="text-sm line-through text-gray-400">
                                                ${
                                                    (
                                                        parseFloat(product.pack_price.per_pack_price) +
                                                        parseFloat(product.pack_price.per_pack_price) * parseFloat(product.pack_price.customer_margin) / 100
                                                    ).toFixed(2)
                                                }
                                            </span>
                                            <span className="text-sm font-medium px-3 py-1 bg-green-100 text-green-600 rounded-full">
                                                Save ${
                                                    (
                                                        (
                                                            parseFloat(product.pack_price.per_pack_price) -
                                                            parseFloat(product.pack_price.per_pack_special_price)
                                                        ) + (
                                                            (parseFloat(product.pack_price.per_pack_price) -
                                                                parseFloat(product.pack_price.per_pack_special_price)) * parseFloat(product.pack_price.customer_margin) / 100
                                                        )
                                                    ).toFixed(2)
                                                }
                                            </span>
                                        </>
                                    )}
                                </div>
                            </div>
                        </Link>
                    </div>

                    {/* Action buttons section - positioned at bottom */}
                    <div className='mt-auto space-y-4 w-full px-3 text-gray-600 py-3'>
                        <div className='flex items-center justify-between rounded-xl bg-orange-50'>
                            <button
                                className="bg-orange-400 hover:bg-orange-500 transition-colors duration-200 rounded-l-lg px-4 py-3 text-white"
                                onClick={() => handleQuantityChange(-1)}
                            >
                                <FaMinus className='text-3xl' />
                            </button>
                            <input
                                type="text"
                                className="text-center w-full text-xl font-semibold text-orange-600 bg-transparent focus:outline-none"
                                value={quantity}
                                onChange={(e) => handleQuantityChange(e.target.value)}
                            />
                            <button
                                className="bg-orange-400 hover:bg-orange-500 transition-colors duration-200 rounded-r-lg px-4 py-3 text-white"
                                onClick={() => handleQuantityChange(1)}
                            >
                                <FaPlus className='text-3xl' />
                            </button>
                        </div>

                        <div>
                            <p className='flex items-center border-b-3 py-2 border-orange-400'>
                                <BsBoxes
                                    className='text-4xl text-indigo-600' />
                                <span
                                    className='mx-2 text-gray-500 text-sm'>Buy Bulks</span>
                            </p>

                            <div className="grid grid-cols-2 items-center justify-between gap-3 py-3">
                                {!!product.min_order_quantity > 0 && <button
                                    className="flex-1 p-2 rounded-lg bg-orange-100 hover:bg-orange-200 transition-colors duration-200 text-orange-600 font-medium hover:outline-1"
                                    onClick={() => handleQuantityChange(parseInt(product.min_order_quantity))}
                                >
                                    <p className='font-semibold'>Buy {parseInt(product.min_order_quantity)} Packs</p>
                                    <p className=''>Per Pack ${
                                        (
                                            (
                                                parseFloat(product.per_pack_special_price || product.per_pack_price)
                                            ) +
                                            parseFloat((product.per_pack_special_price || product.per_pack_price) * parseFloat(product.customer_margin) / 100)
                                        ).toFixed(2)
                                    }</p>
                                    {
                                        product?.per_pack_special_price > 0 && <>
                                            <span className="text-sm line-through text-gray-400">
                                                ${
                                                    (
                                                        parseFloat(product.per_pack_price) +
                                                        parseFloat(product.per_pack_price) * parseFloat(product.customer_margin) / 100
                                                    ).toFixed(2)
                                                }
                                            </span>
                                            <span className="text-sm font-medium px-2 py-1 bg-red-100 text-red-600 rounded inline-block">
                                                Save ${
                                                    (
                                                        (
                                                            parseFloat(product.per_pack_price) - parseFloat(product.per_pack_special_price)
                                                        ) + (
                                                            (parseFloat(product.per_pack_price) - parseFloat(product.per_pack_special_price)) * parseFloat(product.customer_margin) / 100
                                                        )
                                                    ).toFixed(2)}
                                            </span>
                                        </>
                                    }
                                </button>
                                }

                                {
                                    (product.bulk_prices.length > 0 && product?.bulk_prices.map((bulkPrice, index) =>
                                        <button key={index}
                                            className="flex-1 p-2 rounded-lg bg-orange-100 hover:bg-orange-200 transition-colors duration-200 text-orange-600 font-medium hover:outline-1"
                                            onClick={() => handleQuantityChange(parseInt(bulkPrice?.number_of_packs))}
                                        >
                                            <p className='font-semibold'>Buy {parseInt(bulkPrice?.number_of_packs)} Packs</p>
                                            <p className=''>Per Pack ${
                                                (
                                                    (
                                                        parseFloat(bulkPrice.per_pack_special_price || bulkPrice.per_pack_price)
                                                    ) +
                                                    parseFloat((bulkPrice.per_pack_special_price || bulkPrice.per_pack_price) * parseFloat(bulkPrice.customer_margin) / 100)
                                                ).toFixed(2)
                                            }</p>
                                            {
                                                bulkPrice?.per_pack_special_price > 0 && <>
                                                    <span className="text-sm line-through text-gray-400">
                                                        ${
                                                            (
                                                                parseFloat(bulkPrice.per_pack_price) +
                                                                parseFloat(bulkPrice.per_pack_price) * parseFloat(bulkPrice.customer_margin) / 100
                                                            ).toFixed(2)
                                                        }
                                                    </span>
                                                    <span className="text-sm font-medium px-2 py-1 bg-red-100 text-red-600 rounded inline-block">
                                                        Save ${
                                                            (
                                                                (
                                                                    parseFloat(bulkPrice.per_pack_price) - parseFloat(bulkPrice.per_pack_special_price)
                                                                ) + (
                                                                    (parseFloat(bulkPrice.per_pack_price) - parseFloat(bulkPrice.per_pack_special_price)) * parseFloat(bulkPrice.customer_margin) / 100
                                                                )
                                                            ).toFixed(2)}
                                                    </span>
                                                </>
                                            }
                                        </button>)) || (
                                        <div className="w-full p-4 text-center bg-orange-50 rounded-lg">
                                            <p className="text-orange-600 font-medium">No Bulk Offers Available</p>
                                        </div>
                                    )
                                }
                            </div>
                        </div>

                        <div>
                            <p className='flex items-center border-b-3 py-2 border-orange-400'>
                                <FaPallet
                                    className='text-4xl text-purple-600' />
                                <span
                                    className='mx-2 text-gray-500 text-sm'>Buy Pallets {product.pallet_delivery_fee === true ? `(Free Delivery)` : ``} </span>
                            </p>
                            {
                                product.products_per_pallet > 0 ?
                                    <div className="grid grid-cols-2 items-center justify-between gap-3 py-3">
                                        <button
                                            className="flex-1 p-2 rounded-lg bg-orange-100 hover:bg-orange-200 transition-colors duration-200 text-orange-600 font-medium hover:outline-1"
                                            onClick={() => handleQuantityChange(parseInt(product.products_per_pallet))}
                                        >
                                            <p className='font-semibold'>Buy {product.products_per_pallet} Packs</p>
                                            {/* <p className=''>Per Pack ${100}</p> */}
                                        </button>
                                    </div> :
                                    <div className="grid grid-cols-2 items-center justify-between gap-3 py-3">
                                        <button
                                            className="flex-1 p-2 rounded-lg bg-orange-100 hover:bg-orange-200 transition-colors duration-200 text-orange-600 font-medium hover:outline-1"
                                        >
                                            <p className='font-semibold'>Not Available</p>
                                            {/* <p className=''>Per Pack ${100}</p> */}
                                        </button>
                                    </div>
                            }
                        </div>

                        <div className='flex flex-col gap-5 '>
                            <button className={`flex p-3 text-xl rounded-lg bg-orange-400 hover:bg-orange-500 transition-colors duration-200 font-semibold text-white items-center justify-center gap-3 w-full shadow-sm hover:shadow-md ${tailwindCss}`}
                                onClick={handleAddToCart}>
                                {cartPosition === 'left' && <FaCartPlus className="text-3xl" />}
                                {text || 'Add to Cart'}
                                {cartPosition === 'right' && <FaCartPlus className="text-3xl" />}
                            </button>

                            {product && (
                                <Link href={`/products/${product.id}`}>
                                    <button className={`flex p-3 text-xl rounded-lg bg-gray-400 hover:bg-gray-500 transition-colors duration-200 font-semibold text-white items-center justify-center gap-3 w-full shadow-sm hover:shadow-md ${tailwindCss}`}>
                                        <FaEye className="text-3xl" /> <span>View Details</span>
                                    </button>
                                </Link>
                            )}
                        </div>
                    </div>
                </div>
            </motion.li >
            </>
        )
    }

    if (viewMode === 'list') {
        return (
            <>
                <AuthRequiredPopup
                    isOpen={showAuthPopup}
                    onClose={() => setShowAuthPopup(false)}
                />
                <motion.li
                key={product.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden"
            >
                <div className="flex flex-col md:flex-row">
                    {/* Product Image */}
                    <div className="md:w-56 flex-shrink-0">
                        <ProductImage
                            product={product}
                            className="w-full h-48 md:h-56"
                            size="large"
                        />
                    </div>

                    {/* Main Content Area */}
                    <div className="flex-1 flex flex-col">
                        {/* Top Section: Product Info + Price - Flexible content area */}
                        <div className="flex-1 p-4 md:p-6">
                            <div className="flex flex-col xl:flex-row xl:justify-between xl:items-start gap-4">
                                {/* Product Info */}
                                <div className="flex-1">
                                    <Link href={`/products/${product.id}`} className="block mb-4">
                                        <h2 className="text-xl md:text-2xl font-semibold text-orange-500 hover:text-orange-600 transition-colors mb-2">
                                            {product.name}
                                        </h2>
                                        <p className="text-sm text-gray-600 line-clamp-2">
                                            {product?.description || "Description: NA"}
                                            {product?.description?.length > 100 && (
                                                <span className="text-orange-400 hover:text-orange-500 ml-1 inline-block">
                                                    read more...
                                                </span>
                                            )}
                                        </p>
                                    </Link>

                                    {/* Product Details - Horizontal on larger screens */}
                                    <div className="grid grid-cols-2 xl:grid-cols-4 gap-3 text-sm text-gray-600">
                                        <div className="flex flex-col">
                                            <span className="font-medium text-gray-700">Brand</span>
                                            <span className="text-gray-900">{product.brand}</span>
                                        </div>
                                        <div className="flex flex-col">
                                            <span className="font-medium text-gray-700">SKU</span>
                                            <span className="text-gray-900">{product.sku}</span>
                                        </div>
                                        <div className="flex flex-col">
                                            <span className="font-medium text-gray-700">Items/Pack</span>
                                            <span className="text-gray-900">{product.pack_price.number_of_products || "NA"}</span>
                                        </div>
                                        <div className="flex flex-col">
                                            <span className="font-medium text-gray-700">Stock</span>
                                            <span className={`font-medium ${product.quantity > 0 ? 'text-green-600' : 'text-red-600'}`}>
                                                {product.quantity > 0 ? `${product.stock_status}` : "Out of Stock"}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {/* Price Section - Right aligned on large screens */}
                                <div className="xl:text-right xl:min-w-[200px]">
                                    <div className="flex xl:flex-col items-center xl:items-end gap-2 xl:gap-1">
                                        <span className="text-2xl md:text-3xl font-bold text-orange-500">
                                            ${
                                                (
                                                    (
                                                        parseFloat(product.pack_price.per_pack_special_price || product.pack_price.per_pack_price)
                                                    ) +
                                                    parseFloat((product.pack_price.per_pack_special_price || product.pack_price.per_pack_price) * parseFloat(product.pack_price.customer_margin) / 100)
                                                ).toFixed(2)
                                            }<span className="text-xs font-medium"> per pack </span>
                                        </span>
                                        {product.pack_price.per_pack_special_price > 0 && (
                                            <div className="flex xl:flex-col items-center xl:items-end gap-2 xl:gap-1">
                                                <span className="text-sm line-through text-gray-400">
                                                    ${
                                                        (
                                                            parseFloat(product.pack_price.per_pack_price) +
                                                            parseFloat(product.pack_price.per_pack_price) * parseFloat(product.pack_price.customer_margin) / 100
                                                        ).toFixed(2)
                                                    }
                                                </span>
                                                <span className="text-xs font-medium px-2 py-1 bg-green-100 text-green-600 rounded-full">
                                                    Save ${
                                                        (
                                                            (
                                                                parseFloat(product.pack_price.per_pack_price) -
                                                                parseFloat(product.pack_price.per_pack_special_price)
                                                            ) + (
                                                                (parseFloat(product.pack_price.per_pack_price) -
                                                                    parseFloat(product.pack_price.per_pack_special_price)) * parseFloat(product.pack_price.customer_margin) / 100
                                                            )
                                                        ).toFixed(2)
                                                    }
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Bottom Section: Actions - positioned at bottom */}
                        <div className="mt-auto border-t border-gray-100 bg-gray-50 p-4 md:p-6">
                            <div className="flex flex-col lg:flex-row gap-4">
                                {/* Quantity Selector */}
                                <div className="lg:w-48">
                                    <label className="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
                                    <div className="flex items-center rounded-lg bg-white border border-gray-200 shadow-sm">
                                        <button
                                            className="bg-orange-400 hover:bg-orange-500 transition-colors duration-200 rounded-l-lg px-3 py-2 text-white"
                                            onClick={() => handleQuantityChange(-1)}
                                        >
                                            <FaMinus className="text-sm" />
                                        </button>
                                        <input
                                            type="text"
                                            className="text-center flex-1 text-lg font-semibold text-orange-600 bg-transparent focus:outline-none py-2 min-w-0"
                                            value={quantity}
                                            onChange={(e) => handleQuantityChange(e.target.value)}
                                        />
                                        <button
                                            className="bg-orange-400 hover:bg-orange-500 transition-colors duration-200 rounded-r-lg px-3 py-2 text-white"
                                            onClick={() => handleQuantityChange(1)}
                                        >
                                            <FaPlus className="text-sm" />
                                        </button>
                                    </div>
                                </div>
                                {/* Quick Actions & Bulk Options */}
                                <div className="flex-1">
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        {/* Buy Bulks */}
                                        <div>
                                            <div className="flex items-center gap-2 mb-2">
                                                <BsBoxes className="text-lg text-indigo-600" />
                                                <span className="text-sm font-medium text-gray-700">Buy Bulks</span>
                                            </div>
                                            <div className="space-y-2">
                                                {!!product.min_order_quantity > 0 && (
                                                    <button
                                                        className="w-full p-2 rounded-lg bg-orange-100 hover:bg-orange-200 transition-colors duration-200 text-orange-600 text-sm font-medium text-left"
                                                        onClick={() => handleQuantityChange(parseInt(product.min_order_quantity))}
                                                    >
                                                        <div className="font-semibold">Buy {parseInt(product.min_order_quantity)} Packs</div>
                                                        <div className="text-xs">Per Pack ${
                                                            (
                                                                (
                                                                    parseFloat(product.pack_price.per_pack_special_price || product.pack_price.per_pack_price)
                                                                ) +
                                                                parseFloat((product.pack_price.per_pack_special_price || product.pack_price.per_pack_price) * parseFloat(product.pack_price.customer_margin) / 100)
                                                            ).toFixed(2)
                                                        }</div>
                                                        {product.pack_price?.per_pack_special_price > 0 && (
                                                            <div className="flex items-center gap-1 mt-1">
                                                                <span className="text-xs line-through text-gray-400">
                                                                    ${
                                                                        (
                                                                            parseFloat(product.pack_price.per_pack_price) +
                                                                            parseFloat(product.pack_price.per_pack_price) * parseFloat(product.pack_price.customer_margin) / 100
                                                                        ).toFixed(2)
                                                                    }
                                                                </span>
                                                                <span className="text-xs font-medium px-1 py-0.5 bg-red-100 text-red-600 rounded">
                                                                    Save ${
                                                                        (
                                                                            (
                                                                                parseFloat(product.pack_price.per_pack_price) - parseFloat(product.pack_price.per_pack_special_price)
                                                                            ) + (
                                                                                (parseFloat(product.pack_price.per_pack_price) - parseFloat(product.pack_price.per_pack_special_price)) * parseFloat(product.pack_price.customer_margin) / 100
                                                                            )
                                                                        ).toFixed(2)
                                                                    }
                                                                </span>
                                                            </div>
                                                        )}
                                                    </button>
                                                )}

                                                {product.bulk_prices.length > 0 ? (
                                                    product.bulk_prices.slice(0, 2).map((bulkPrice, index) => (
                                                        <button
                                                            key={index}
                                                            className="w-full p-2 rounded-lg bg-orange-100 hover:bg-orange-200 transition-colors duration-200 text-orange-600 text-sm font-medium text-left"
                                                            onClick={() => handleQuantityChange(parseInt(bulkPrice?.number_of_packs))}
                                                        >
                                                            <div className="font-semibold">Buy {parseInt(bulkPrice?.number_of_packs)} Packs</div>
                                                            <div className="text-xs">Per Pack ${
                                                                (
                                                                    (
                                                                        parseFloat(bulkPrice.per_pack_special_price || bulkPrice.per_pack_price)
                                                                    ) +
                                                                    parseFloat((bulkPrice.per_pack_special_price || bulkPrice.per_pack_price) * parseFloat(bulkPrice.customer_margin) / 100)
                                                                ).toFixed(2)
                                                            }</div>
                                                            {bulkPrice?.per_pack_special_price > 0 && (
                                                                <div className="flex items-center gap-1 mt-1">
                                                                    <span className="text-xs line-through text-gray-400">
                                                                        ${
                                                                            (
                                                                                parseFloat(bulkPrice.per_pack_price) +
                                                                                parseFloat(bulkPrice.per_pack_price) * parseFloat(bulkPrice.customer_margin) / 100
                                                                            ).toFixed(2)
                                                                        }
                                                                    </span>
                                                                    <span className="text-xs font-medium px-1 py-0.5 bg-red-100 text-red-600 rounded">
                                                                        Save ${
                                                                            (
                                                                                (
                                                                                    parseFloat(bulkPrice.per_pack_price) - parseFloat(bulkPrice.per_pack_special_price)
                                                                                ) + (
                                                                                    (parseFloat(bulkPrice.per_pack_price) - parseFloat(bulkPrice.per_pack_special_price)) * parseFloat(bulkPrice.customer_margin) / 100
                                                                                )
                                                                            ).toFixed(2)
                                                                        }
                                                                    </span>
                                                                </div>
                                                            )}
                                                        </button>
                                                    ))
                                                ) : (
                                                    <div className="w-full p-2 text-center bg-orange-50 rounded-lg border border-orange-200">
                                                        <p className="text-orange-600 font-medium text-sm">No Bulk Offers Available</p>
                                                    </div>
                                                )}
                                            </div>
                                        </div>

                                        {/* Buy Pallets */}
                                        <div>
                                            <div className="flex items-center gap-2 mb-2">
                                                <FaPallet className="text-lg text-purple-600" />
                                                <span className="text-sm font-medium text-gray-700">
                                                    Buy Pallets {product.pallet_delivery_fee === true ? '(Free Delivery)' : ''}
                                                </span>
                                            </div>

                                            {product.products_per_pallet > 0 ? (
                                                <button
                                                    className="w-full p-2 rounded-lg bg-purple-100 hover:bg-purple-200 transition-colors duration-200 text-purple-600 text-sm font-medium text-left"
                                                    onClick={() => handleQuantityChange(parseInt(product.products_per_pallet))}
                                                >
                                                    <div className="font-semibold">Buy {product.products_per_pallet} Packs</div>
                                                    <div className="text-xs text-purple-500">Full pallet quantity</div>
                                                </button>
                                            ) : (
                                                <div className="w-full p-2 text-center bg-purple-50 rounded-lg border border-purple-200">
                                                    <p className="text-purple-600 font-medium text-sm">Not Available</p>
                                                </div>
                                            )}
                                        </div>

                                        {/* Action Buttons */}
                                        <div className="md:col-span-2 lg:col-span-1">
                                            <div className="space-y-2">
                                                <button
                                                    className={`w-full flex items-center justify-center gap-2 p-3 text-lg rounded-lg bg-orange-400 hover:bg-orange-500 transition-colors duration-200 font-semibold text-white shadow-sm hover:shadow-md ${tailwindCss}`}
                                                    onClick={handleAddToCart}
                                                >
                                                    {cartPosition === 'left' && <FaCartPlus className="text-xl" />}
                                                    {text || 'Add to Cart'}
                                                    {cartPosition === 'right' && <FaCartPlus className="text-xl" />}
                                                </button>

                                                {product && (
                                                    <Link href={`/products/${product.id}`} className="block">
                                                        <button className={`w-full flex items-center justify-center gap-2 p-3 text-lg rounded-lg bg-gray-400 hover:bg-gray-500 transition-colors duration-200 font-semibold text-white shadow-sm hover:shadow-md ${tailwindCss}`}>
                                                            <FaEye className="text-xl" />
                                                            <span>View Details</span>
                                                        </button>
                                                    </Link>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </motion.li>
            </>
        );
    }
}
