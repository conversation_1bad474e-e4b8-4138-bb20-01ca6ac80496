'use client';

import Signup from '@/app/components/Authentication/Signup'
import React, { Suspense } from 'react'

// Loading component for Suspense fallback
function SignupLoading() {
    return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-yellow-100 via-indigo-100 to-orange-100">
            <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
                <p className="text-gray-600 font-medium">Loading signup page...</p>
            </div>
        </div>
    )
}

export default function SignupPartnerPage() {
    return (
        <Suspense fallback={<SignupLoading />}>
            <Signup
                title="Partner with Diptouch"
                subHeading="Are you a manufacturer or supplier? Partner with us and reach more customers"
                description=""
                partner={true}
                user={false}
                userType="seller"
            />
        </Suspense>
    )
}
