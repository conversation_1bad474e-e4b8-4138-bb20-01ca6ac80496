import Login from '@/app/components/Authentication/Login'
import React, { Suspense } from 'react'

// Loading component for Suspense fallback
function LoginLoading() {
    return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-yellow-100 via-indigo-100 to-orange-100">
            <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
                <p className="text-gray-600 font-medium">Loading login page...</p>
            </div>
        </div>
    )
}

export default function LoginUserPage() {
    return (
        <Suspense fallback={<LoginLoading />}>
            <Login title={"Welcome back to Diptouch"}
                subHeading={"Select your login and access your Customer dashboard"}
                description={""}
                partner={false}
                user={true} />
        </Suspense>
    )
}
