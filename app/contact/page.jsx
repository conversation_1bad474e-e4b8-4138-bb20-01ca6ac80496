import Navbar from '../components/LandingPage/Navbar';
import Footer from '../components/LandingPage/Footer';
import { FaPhone, FaEnvelope } from 'react-icons/fa';
import { MdBusiness, MdSupport } from 'react-icons/md';

export default function Contact() {

  return (
    <div className="min-h-screen bg-white font-[family-name:var(--font-geist-sans)]">
      <Navbar />

      <main className="pt-20">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-orange-50 to-orange-100 py-16">
          <div className="container mx-auto px-6 md:px-12">
            <div className="text-center">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Contact <span className="text-orange-500">D</span>iptouch
              </h1>
              <p className="text-xl text-gray-800 max-w-3xl mx-auto">
                Get in touch with our team for bulk orders, partnerships, or any questions about our platform
              </p>
            </div>
          </div>
        </section>

        {/* Contact Content */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-6 md:px-12">
            <div className="max-w-4xl mx-auto">

              {/* Contact Information */}
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-8">Get In Touch</h2>

                <div className="space-y-6 mb-8">
                  <div className="flex items-start space-x-4">
                    <div className="bg-orange-100 p-3 rounded-lg">
                      <FaEnvelope className="text-orange-600 text-xl" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">Email Us</h3>
                      <p className="text-gray-800"><EMAIL></p>
                      <p className="text-sm text-orange-600">We'll respond within 24 hours</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="bg-orange-100 p-3 rounded-lg">
                      <FaPhone className="text-orange-600 text-xl" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">Call Us</h3>
                      <p className="text-gray-800">+****************</p>
                      <p className="text-sm text-orange-600">Monday - Friday, 9 AM - 6 PM</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="bg-orange-100 p-3 rounded-lg">
                      <MdBusiness className="text-orange-600 text-xl" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">Business Inquiries</h3>
                      <p className="text-gray-800">Bulk orders & partnerships</p>
                      <p className="text-sm text-orange-600">Special rates for large volume orders</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="bg-orange-100 p-3 rounded-lg">
                      <MdSupport className="text-orange-600 text-xl" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">Customer Support</h3>
                      <p className="text-gray-800">24/7 assistance available</p>
                      <p className="text-sm text-orange-600">Help with orders, accounts & technical issues</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
}
